# User Data Reset Procedure for LifeQuest AI

This document outlines the steps to completely reset all data associated with a specific Telegram user, effectively allowing them to start from scratch. This involves deleting records from the Supabase database and removing their associated vectors from the Cloudflare Vectorize index.

**Warning:** This is a destructive operation and cannot be undone. Ensure you have the correct `telegram_id` before proceeding.

## Prerequisites:

1.  **Supabase Access:** Credentials/permissions to connect to the Supabase database and execute SQL queries (e.g., via Supabase SQL Editor or a PSQL client). Alternatively, if operating in an environment with an MCP server connected to this project, the `mcp_supabase_execute_sql` or `mcp_supabase_apply_migration` tools could be used for Step 2, though direct SQL execution is often simpler for ad-hoc deletions.
2.  **Cloudflare API Token:** A Cloudflare API token with permissions to read and delete vectors from your Vectorize index (`lifequest-ai-memory-index`). (Note: MCP does not currently have direct Vectorize delete-by-metadata tools).
3.  **`wrangler` CLI:** Installed and configured.
4.  **Target `telegram_id`:** The unique Telegram ID of the user whose data needs to be reset.

## Steps to Reset User Data:

### Step 1: Identify User's Internal ID and Vector Owner ID

Before deleting, you need the internal `user_id` from the `users` table that corresponds to the `telegram_id`.

1.  Connect to your Supabase database.
2.  Execute the following SQL query, replacing `<TARGET_TELEGRAM_ID>` with the actual Telegram ID:
    ```sql
    SELECT id FROM public.users WHERE telegram_id = <TARGET_TELEGRAM_ID>;
    ```
3.  Note down the returned `id`. This is the `actualUserId` used in other tables and will be the `vector_owner_id` (as a string) in Vectorize. If no user is found, they have no data to reset.

### Step 2: Delete Data from Supabase Database

Execute the following SQL DELETE statements in order. Replace `<ACTUAL_USER_ID_FROM_STEP_1>` with the internal `id` you noted.

If using direct SQL access (e.g., Supabase SQL Editor):

```sql
-- 1. Delete from pending_confirmations (references users.telegram_id, but safer to use actualUserId via a subquery if needed or delete by telegram_id directly)
-- If pending_confirmations.user_telegram_id directly stores the telegram_id:
DELETE FROM public.pending_confirmations WHERE user_telegram_id = <TARGET_TELEGRAM_ID>;
-- Or, if it needs to be joined (less likely for this table based on schema):
-- DELETE FROM public.pending_confirmations WHERE user_id = <ACTUAL_USER_ID_FROM_STEP_1>; -- Adjust if schema differs

-- 2. Delete from messages
DELETE FROM public.messages WHERE user_id = <ACTUAL_USER_ID_FROM_STEP_1>;

-- 3. Delete from game_state
DELETE FROM public.game_state WHERE user_id = <ACTUAL_USER_ID_FROM_STEP_1>;

-- 4. Delete from quests
DELETE FROM public.quests WHERE user_id = <ACTUAL_USER_ID_FROM_STEP_1>;

-- 5. Delete from hall_of_fame
DELETE FROM public.hall_of_fame WHERE user_id = <ACTUAL_USER_ID_FROM_STEP_1>;

-- 6. Delete from knowledge_documents (if user-specific)
DELETE FROM public.knowledge_documents WHERE user_id = <ACTUAL_USER_ID_FROM_STEP_1>;

-- 7. Finally, delete from users table
DELETE FROM public.users WHERE id = <ACTUAL_USER_ID_FROM_STEP_1>;
```

If using MCP Supabase tools (less ideal for multi-statement, ordered deletes but possible for individual commands):
Each DELETE statement below would need to be run as a separate `mcp_supabase_execute_sql` call, ensuring the `<ACTUAL_USER_ID_FROM_STEP_1>` and `<TARGET_TELEGRAM_ID>` are correctly substituted into the query string for each call. This method is more cumbersome for this bulk operation than direct SQL.

### Step 3: Delete Vectors from Cloudflare Vectorize Index

This step is more complex as Vectorize currently requires listing vector IDs and then deleting them, potentially in batches. Filtering by metadata during delete is not directly supported via a simple `delete where metadata.field = value` command.

**Method 1: List by Prefix (If `vector_owner_id` is part of vector IDs - current design uses UUIDs for vector IDs)**
This method is not directly applicable with current random UUIDs for vector IDs.

**Method 2: List all vectors and filter client-side (Potentially slow for large indexes, but more reliable for this specific need)**

1.  **List all vector IDs from the index:**
    ```bash
    npx wrangler vectorize list lifequest-ai-memory-index > all_vectors.json
    ```
    This command might need pagination for very large indexes. The output is NDJSON.

2.  **Script to identify and delete vectors:**
    You would need a script (e.g., Node.js or Python) to:
    a.  Read `all_vectors.json`.
    b.  For each vector, fetch its metadata. (If running a script via an MCP-enabled environment, `env.VECTORIZE_INDEX.getByIds` would be used by the underlying script code, not directly by an MCP tool).
    c.  Check if `metadata.vector_owner_id` matches the stringified `actualUserId` (e.g., `"5"`).
    d.  Collect the IDs of all matching vectors.
    e.  Delete these collected vector IDs in batches using `env.VECTORIZE_INDEX.deleteByIds([...])`. (Again, this would be in script code, not a direct MCP tool for deletion by metadata).

**Simplified Approach for Step 3 (if manual and index size is manageable):**

If the number of vectors isn't enormous, and direct querying with metadata to get IDs isn't straightforward for deletion:

1.  **Mentally Prepare for Data Loss for this User in Vectorize:** Realize that precise deletion by metadata `vector_owner_id` is programmatically intensive without direct API support for "delete where metadata matches".
2.  **If testing with few users:** The simplest, albeit drastic for other users, approach would be to delete and re-create the entire Vectorize index if necessary during early testing phases. **This is NOT recommended for a live system with multiple users.**
3.  **Future Consideration:** For robust multi-user data deletion from Vectorize, the application might need to maintain its own secondary index (e.g., in Supabase) mapping `vector_owner_id` to all associated `vector_ids` in Vectorize to facilitate targeted deletions.

**Alternative for Step 3 (Using the `vector_owner_id` metadata index):**

While you can't delete by metadata filter, you *can* query by metadata filter.
1.  **Query to get IDs:**
    *   You would need to implement a temporary admin function in your Worker or a script that:
        *   Takes a `vector_owner_id` as input.
        *   Performs `env.VECTORIZE_INDEX.query(<DUMMY_QUERY_VECTOR>, { filter: { vector_owner_id: "TARGET_VECTOR_OWNER_ID_AS_STRING" }, topK: <VERY_LARGE_NUMBER>, returnMetadata: true })`. (This query could be executed via a custom MCP tool that calls a worker function, or a script with direct access).
        *   Collects all `match.id` values from the results.
2.  **Delete by IDs:**
    *   Use the collected IDs with `env.VECTORIZE_INDEX.deleteByIds([...])`. (Executed by a worker function or script).

This alternative is more targeted but requires some coding.

## Post-Reset:

-   The user can now interact with the Telegram bot and will be treated as a completely new user, starting from scratch.
-   Their old `telegram_id` will result in a new entry in the `users` table.

This procedure should ensure a clean slate for the specified user. 