{"mcpServers": {"sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": [], "alwaysAllow": ["sequentialthinking"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "MCP Docs": {"url": "https://gitmcp.io/docs"}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "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", "SUPABASE_URL": "https://abfrdvkdsmfsvwzpsnpo.supabase.co", "SUPABASE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFiZnJkdmtkc21mc3Z3enBzbnBvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc3NDc2OTUsImV4cCI6MjA2MzMyMzY5NX0.CJB995ufBloIYCVZY4x_-Bf6zpQRifEolX9TmSZpXPk"}, "alwaysAllow": ["update_subtask", "set_task_status"]}}}