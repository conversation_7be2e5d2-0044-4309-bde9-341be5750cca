"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Messages = exports.Batches = exports.BetaMessageBatchesPage = void 0;
var batches_1 = require("./batches.js");
Object.defineProperty(exports, "BetaMessageBatchesPage", { enumerable: true, get: function () { return batches_1.BetaMessageBatchesPage; } });
Object.defineProperty(exports, "Batches", { enumerable: true, get: function () { return batches_1.Batches; } });
var messages_1 = require("./messages.js");
Object.defineProperty(exports, "Messages", { enumerable: true, get: function () { return messages_1.Messages; } });
//# sourceMappingURL=index.js.map