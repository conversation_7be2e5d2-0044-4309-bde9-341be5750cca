{"version": 3, "file": "batches.mjs", "sourceRoot": "", "sources": ["../../../src/resources/beta/messages/batches.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;OACf,EAAE,gBAAgB,EAAE;OAIpB,EAAE,IAAI,EAAmB;OACzB,EAAE,YAAY,EAAE;OAChB,EAAE,cAAc,EAAE;AAEzB,MAAM,OAAO,OAAQ,SAAQ,WAAW;IACtC;;;;;;;;;OASG;IACH,MAAM,CAAC,MAAyB,EAAE,OAA6B;QAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACzD,IAAI;YACJ,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;gBAC7E,GAAG,OAAO,EAAE,OAAO;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAgBD,QAAQ,CACN,cAAsB,EACtB,SAAoD,EAAE,EACtD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAClD;QACD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,YAAY,EAAE;YAC1E,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;gBAC7E,GAAG,OAAO,EAAE,OAAO;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAcD,IAAI,CACF,SAAgD,EAAE,EAClD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;SAC9B;QACD,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gCAAgC,EAAE,sBAAsB,EAAE;YACvF,KAAK;YACL,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;gBAC7E,GAAG,OAAO,EAAE,OAAO;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAiBD,MAAM,CACJ,cAAsB,EACtB,SAAkD,EAAE,EACpD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAChD;QACD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,cAAc,YAAY,EAAE;YAC7E,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;gBAC7E,GAAG,OAAO,EAAE,OAAO;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAsBD,MAAM,CACJ,cAAsB,EACtB,SAAkD,EAAE,EACpD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAChD;QACD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,cAAc,mBAAmB,EAAE;YAClF,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;gBAC7E,GAAG,OAAO,EAAE,OAAO;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAqBD,KAAK,CAAC,OAAO,CACX,cAAsB,EACtB,SAAmD,EAAE,EACrD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACjD;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACtB,MAAM,IAAI,cAAc,CACtB,yDAAyD,KAAK,CAAC,iBAAiB,MAAM,KAAK,CAAC,EAAE,EAAE,CACjG,CAAC;SACH;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO;aAChB,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE;YACtB,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;gBAC7E,MAAM,EAAE,oBAAoB;gBAC5B,GAAG,OAAO,EAAE,OAAO;aACpB;YACD,gBAAgB,EAAE,IAAI;SACvB,CAAC;aACD,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;IAC5F,CAAC;CACF;AAED,MAAM,OAAO,sBAAuB,SAAQ,IAAsB;CAAG;AAuPrE,OAAO,CAAC,sBAAsB,GAAG,sBAAsB,CAAC"}