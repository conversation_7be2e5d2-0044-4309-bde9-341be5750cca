# @ai-sdk/azure

## 1.3.23

### Patch Changes

- Updated dependencies [d87b9d1]
  - @ai-sdk/provider-utils@2.2.8
  - @ai-sdk/openai@1.3.22

## 1.3.22

### Patch Changes

- Updated dependencies [5caac29]
  - @ai-sdk/openai@1.3.21

## 1.3.21

### Patch Changes

- Updated dependencies [dd5450e]
  - @ai-sdk/openai@1.3.20

## 1.3.20

### Patch Changes

- Updated dependencies [3cabda9]
  - @ai-sdk/openai@1.3.19

## 1.3.19

### Patch Changes

- Updated dependencies [74cd391]
  - @ai-sdk/openai@1.3.18

## 1.3.18

### Patch Changes

- Updated dependencies [ca7bce3]
  - @ai-sdk/openai@1.3.17

## 1.3.17

### Patch Changes

- Updated dependencies [bd6e457]
  - @ai-sdk/openai@1.3.16

## 1.3.16

### Patch Changes

- Updated dependencies [98d954e]
  - @ai-sdk/openai@1.3.15

## 1.3.15

### Patch Changes

- Updated dependencies [980141c]
  - @ai-sdk/openai@1.3.14

## 1.3.14

### Patch Changes

- Updated dependencies [75b9849]
  - @ai-sdk/openai@1.3.13

## 1.3.13

### Patch Changes

- Updated dependencies [575339f]
  - @ai-sdk/openai@1.3.12

## 1.3.12

### Patch Changes

- 3c26c55: feat(providers/azure): add transcribe
- Updated dependencies [beef951]
  - @ai-sdk/provider@1.1.3
  - @ai-sdk/openai@1.3.11
  - @ai-sdk/provider-utils@2.2.7

## 1.3.11

### Patch Changes

- Updated dependencies [dbe53e7]
- Updated dependencies [84ffaba]
  - @ai-sdk/openai@1.3.10

## 1.3.10

### Patch Changes

- Updated dependencies [013faa8]
- Updated dependencies [013faa8]
  - @ai-sdk/provider@1.1.2
  - @ai-sdk/openai@1.3.9
  - @ai-sdk/provider-utils@2.2.6

## 1.3.9

### Patch Changes

- Updated dependencies [c21fa6d]
  - @ai-sdk/provider-utils@2.2.5
  - @ai-sdk/provider@1.1.1
  - @ai-sdk/openai@1.3.8

## 1.3.8

### Patch Changes

- e82024e: feat (provider/azure): add OpenAI responses API support

## 1.3.7

### Patch Changes

- Updated dependencies [2c19b9a]
  - @ai-sdk/provider-utils@2.2.4
  - @ai-sdk/openai@1.3.7

## 1.3.6

### Patch Changes

- Updated dependencies [28be004]
  - @ai-sdk/provider-utils@2.2.3
  - @ai-sdk/openai@1.3.6

## 1.3.5

### Patch Changes

- Updated dependencies [52ed95f]
- Updated dependencies [b01120e]
  - @ai-sdk/openai@1.3.5
  - @ai-sdk/provider-utils@2.2.2

## 1.3.4

### Patch Changes

- Updated dependencies [b520dba]
  - @ai-sdk/openai@1.3.4

## 1.3.3

### Patch Changes

- Updated dependencies [24befd8]
  - @ai-sdk/openai@1.3.3

## 1.3.2

### Patch Changes

- Updated dependencies [db15028]
  - @ai-sdk/openai@1.3.2

## 1.3.1

### Patch Changes

- Updated dependencies [f10f0fa]
  - @ai-sdk/provider-utils@2.2.1
  - @ai-sdk/openai@1.3.1

## 1.3.0

### Minor Changes

- 5bc638d: AI SDK 4.2

### Patch Changes

- Updated dependencies [5bc638d]
  - @ai-sdk/openai@1.3.0
  - @ai-sdk/provider@1.1.0
  - @ai-sdk/provider-utils@2.2.0

## 1.2.8

### Patch Changes

- Updated dependencies [9f4f1bc]
  - @ai-sdk/openai@1.2.8

## 1.2.7

### Patch Changes

- Updated dependencies [d0c4659]
  - @ai-sdk/provider-utils@2.1.15
  - @ai-sdk/openai@1.2.7

## 1.2.6

### Patch Changes

- Updated dependencies [0bd5bc6]
  - @ai-sdk/provider@1.0.12
  - @ai-sdk/openai@1.2.6
  - @ai-sdk/provider-utils@2.1.14

## 1.2.5

### Patch Changes

- Updated dependencies [2e1101a]
  - @ai-sdk/provider@1.0.11
  - @ai-sdk/openai@1.2.5
  - @ai-sdk/provider-utils@2.1.13

## 1.2.4

### Patch Changes

- Updated dependencies [523f128]
  - @ai-sdk/openai@1.2.4

## 1.2.3

### Patch Changes

- Updated dependencies [1531959]
  - @ai-sdk/provider-utils@2.1.12
  - @ai-sdk/openai@1.2.3

## 1.2.2

### Patch Changes

- Updated dependencies [e3a389e]
  - @ai-sdk/openai@1.2.2

## 1.2.1

### Patch Changes

- Updated dependencies [e1d3d42]
  - @ai-sdk/provider@1.0.10
  - @ai-sdk/openai@1.2.1
  - @ai-sdk/provider-utils@2.1.11

## 1.2.0

### Minor Changes

- ede6d1b: feat (provider/azure): Add Azure image model support

### Patch Changes

- Updated dependencies [ede6d1b]
  - @ai-sdk/openai@1.2.0

## 1.1.15

### Patch Changes

- Updated dependencies [d8216f8]
  - @ai-sdk/openai@1.1.15

## 1.1.14

### Patch Changes

- Updated dependencies [ddf9740]
  - @ai-sdk/provider@1.0.9
  - @ai-sdk/openai@1.1.14
  - @ai-sdk/provider-utils@2.1.10

## 1.1.13

### Patch Changes

- Updated dependencies [2761f06]
  - @ai-sdk/provider@1.0.8
  - @ai-sdk/openai@1.1.13
  - @ai-sdk/provider-utils@2.1.9

## 1.1.12

### Patch Changes

- Updated dependencies [ea159cb]
  - @ai-sdk/openai@1.1.12

## 1.1.11

### Patch Changes

- Updated dependencies [2e898b4]
  - @ai-sdk/provider-utils@2.1.8
  - @ai-sdk/openai@1.1.11

## 1.1.10

### Patch Changes

- Updated dependencies [3ff4ef8]
  - @ai-sdk/provider-utils@2.1.7
  - @ai-sdk/openai@1.1.10

## 1.1.9

### Patch Changes

- Updated dependencies [c55b81a]
  - @ai-sdk/openai@1.1.9

## 1.1.8

### Patch Changes

- Updated dependencies [161be90]
  - @ai-sdk/openai@1.1.8

## 1.1.7

### Patch Changes

- Updated dependencies [0a2f026]
  - @ai-sdk/openai@1.1.7

## 1.1.6

### Patch Changes

- Updated dependencies [d89c3b9]
  - @ai-sdk/provider@1.0.7
  - @ai-sdk/openai@1.1.6
  - @ai-sdk/provider-utils@2.1.6

## 1.1.5

### Patch Changes

- Updated dependencies [3a602ca]
  - @ai-sdk/provider-utils@2.1.5
  - @ai-sdk/openai@1.1.5

## 1.1.4

### Patch Changes

- Updated dependencies [066206e]
  - @ai-sdk/provider-utils@2.1.4
  - @ai-sdk/openai@1.1.4

## 1.1.3

### Patch Changes

- Updated dependencies [39e5c1f]
  - @ai-sdk/provider-utils@2.1.3
  - @ai-sdk/openai@1.1.3

## 1.1.2

### Patch Changes

- Updated dependencies [ed012d2]
- Updated dependencies [3a58a2e]
  - @ai-sdk/provider-utils@2.1.2
  - @ai-sdk/openai@1.1.2
  - @ai-sdk/provider@1.0.6

## 1.1.1

### Patch Changes

- Updated dependencies [e7a9ec9]
- Updated dependencies [0a699f1]
  - @ai-sdk/provider-utils@2.1.1
  - @ai-sdk/openai@1.1.1
  - @ai-sdk/provider@1.0.5

## 1.1.0

### Minor Changes

- 62ba5ad: release: AI SDK 4.1

### Patch Changes

- Updated dependencies [62ba5ad]
  - @ai-sdk/openai@1.1.0
  - @ai-sdk/provider-utils@2.1.0

## 1.0.22

### Patch Changes

- Updated dependencies [00114c5]
  - @ai-sdk/provider-utils@2.0.8
  - @ai-sdk/openai@1.0.20

## 1.0.21

### Patch Changes

- Updated dependencies [218d001]
  - @ai-sdk/openai@1.0.19

## 1.0.20

### Patch Changes

- Updated dependencies [fe816e4]
  - @ai-sdk/openai@1.0.18

## 1.0.19

### Patch Changes

- Updated dependencies [ba62cf2]
- Updated dependencies [3c3fae8]
  - @ai-sdk/openai@1.0.17

## 1.0.18

### Patch Changes

- Updated dependencies [90fb95a]
- Updated dependencies [e6dfef4]
- Updated dependencies [6636db6]
  - @ai-sdk/provider-utils@2.0.7
  - @ai-sdk/openai@1.0.16

## 1.0.17

### Patch Changes

- Updated dependencies [f8c6acb]
- Updated dependencies [d0041f7]
- Updated dependencies [4d2f97b]
  - @ai-sdk/openai@1.0.15

## 1.0.16

### Patch Changes

- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [6337688]
  - @ai-sdk/openai@1.0.14
  - @ai-sdk/provider@1.0.4
  - @ai-sdk/provider-utils@2.0.6

## 1.0.15

### Patch Changes

- Updated dependencies [b19aa82]
  - @ai-sdk/openai@1.0.13

## 1.0.14

### Patch Changes

- Updated dependencies [a4241ff]
  - @ai-sdk/openai@1.0.12

## 1.0.13

### Patch Changes

- 5ed5e45: chore (config): Use ts-library.json tsconfig for no-UI libs.
- Updated dependencies [5ed5e45]
  - @ai-sdk/provider-utils@2.0.5
  - @ai-sdk/provider@1.0.3
  - @ai-sdk/openai@1.0.11

## 1.0.12

### Patch Changes

- Updated dependencies [d4fad4e]
  - @ai-sdk/openai@1.0.10

## 1.0.11

### Patch Changes

- Updated dependencies [3fab0fb]
- Updated dependencies [e956eed]
- Updated dependencies [6faab13]
  - @ai-sdk/openai@1.0.9

## 1.0.10

### Patch Changes

- Updated dependencies [09a9cab]
  - @ai-sdk/provider@1.0.2
  - @ai-sdk/openai@1.0.8
  - @ai-sdk/provider-utils@2.0.4

## 1.0.9

### Patch Changes

- Updated dependencies [0984f0b]
  - @ai-sdk/provider-utils@2.0.3
  - @ai-sdk/openai@1.0.7

## 1.0.8

### Patch Changes

- Updated dependencies [a9a19cb]
  - @ai-sdk/openai@1.0.6

## 1.0.7

### Patch Changes

- Updated dependencies [fc18132]
  - @ai-sdk/openai@1.0.5

## 1.0.6

### Patch Changes

- 153c563: feat (provider/azure): add api version provider option

## 1.0.5

### Patch Changes

- Updated dependencies [b446ae5]
  - @ai-sdk/provider@1.0.1
  - @ai-sdk/openai@1.0.4
  - @ai-sdk/provider-utils@2.0.2

## 1.0.4

### Patch Changes

- Updated dependencies [b748dfb]
  - @ai-sdk/openai@1.0.3

## 1.0.3

### Patch Changes

- Updated dependencies [c3ab5de]
  - @ai-sdk/provider-utils@2.0.1
  - @ai-sdk/openai@1.0.2

## 1.0.2

### Patch Changes

- 700b2be: chore (provider/azure): update Azure OpenAI API version to 2024-10-01-preview

## 1.0.1

### Patch Changes

- Updated dependencies [5e6419a]
  - @ai-sdk/openai@1.0.1

## 1.0.0

### Major Changes

- 66060f7: chore (release): bump major version to 4.0

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [66060f7]
- Updated dependencies [dce4158]
- Updated dependencies [79644e9]
- Updated dependencies [0d3d3f5]
- Updated dependencies [c0ddc24]
- Updated dependencies [b1da952]
- Updated dependencies [dce4158]
- Updated dependencies [8426f55]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0
  - @ai-sdk/provider@1.0.0
  - @ai-sdk/openai@1.0.0

## 1.0.0-canary.3

### Patch Changes

- Updated dependencies [8426f55]
  - @ai-sdk/provider-utils@2.0.0-canary.3
  - @ai-sdk/openai@1.0.0-canary.3

## 1.0.0-canary.2

### Patch Changes

- Updated dependencies [dce4158]
- Updated dependencies [dce4158]
  - @ai-sdk/provider-utils@2.0.0-canary.2
  - @ai-sdk/openai@1.0.0-canary.2

## 1.0.0-canary.1

### Patch Changes

- Updated dependencies [79644e9]
- Updated dependencies [0d3d3f5]
- Updated dependencies [b1da952]
  - @ai-sdk/openai@1.0.0-canary.1
  - @ai-sdk/provider-utils@2.0.0-canary.1

## 1.0.0-canary.0

### Major Changes

- 66060f7: chore (release): bump major version to 4.0

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [66060f7]
- Updated dependencies [c0ddc24]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0-canary.0
  - @ai-sdk/provider@1.0.0-canary.0
  - @ai-sdk/openai@1.0.0-canary.0

## 0.0.52

### Patch Changes

- Updated dependencies [0bc4115]
  - @ai-sdk/openai@0.0.72

## 0.0.51

### Patch Changes

- Updated dependencies [54a3a59]
  - @ai-sdk/openai@0.0.71

## 0.0.50

### Patch Changes

- Updated dependencies [aa98cdb]
- Updated dependencies [1486128]
- Updated dependencies [7b937c5]
- Updated dependencies [3b1b69a]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/provider@0.0.26
  - @ai-sdk/openai@0.0.70

## 0.0.49

### Patch Changes

- Updated dependencies [b9b0d7b]
  - @ai-sdk/provider@0.0.25
  - @ai-sdk/openai@0.0.69
  - @ai-sdk/provider-utils@1.0.21

## 0.0.48

### Patch Changes

- Updated dependencies [741ca51]
  - @ai-sdk/openai@0.0.68

## 0.0.47

### Patch Changes

- 861fb35: fix (provider/azure): support token usage information when streaming

## 0.0.46

### Patch Changes

- Updated dependencies [39fccee]
  - @ai-sdk/openai@0.0.67

## 0.0.45

### Patch Changes

- Updated dependencies [3f29c10]
  - @ai-sdk/openai@0.0.66

## 0.0.44

### Patch Changes

- Updated dependencies [e8aed44]
  - @ai-sdk/openai@0.0.65

## 0.0.43

### Patch Changes

- Updated dependencies [5aa576d]
  - @ai-sdk/openai@0.0.64

## 0.0.42

### Patch Changes

- 9994f55: chore (provider/azure): update Azure OpenAI API version to 2024-08-01-preview

## 0.0.41

### Patch Changes

- Updated dependencies [d595d0d]
  - @ai-sdk/provider@0.0.24
  - @ai-sdk/openai@0.0.63
  - @ai-sdk/provider-utils@1.0.20

## 0.0.40

### Patch Changes

- Updated dependencies [7efa867]
  - @ai-sdk/openai@0.0.62

## 0.0.39

### Patch Changes

- Updated dependencies [8132a60]
  - @ai-sdk/openai@0.0.61

## 0.0.38

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19
  - @ai-sdk/openai@0.0.60

## 0.0.37

### Patch Changes

- Updated dependencies [a0991ec]
  - @ai-sdk/openai@0.0.59

## 0.0.36

### Patch Changes

- Updated dependencies [e0c36bd]
  - @ai-sdk/openai@0.0.58

## 0.0.35

### Patch Changes

- Updated dependencies [d1aaeae]
  - @ai-sdk/openai@0.0.57

## 0.0.34

### Patch Changes

- Updated dependencies [03313cd]
- Updated dependencies [3be7c1c]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/provider@0.0.23
  - @ai-sdk/openai@0.0.56

## 0.0.33

### Patch Changes

- Updated dependencies [28cbf2e]
  - @ai-sdk/openai@0.0.55

## 0.0.32

### Patch Changes

- 26515cb: feat (ai/provider): introduce ProviderV1 specification
- Updated dependencies [26515cb]
  - @ai-sdk/provider@0.0.22
  - @ai-sdk/openai@0.0.54
  - @ai-sdk/provider-utils@1.0.17

## 0.0.31

### Patch Changes

- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16
  - @ai-sdk/openai@0.0.53

## 0.0.30

### Patch Changes

- Updated dependencies [d5b6a15]
  - @ai-sdk/openai@0.0.52

## 0.0.29

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15
  - @ai-sdk/openai@0.0.51

## 0.0.28

### Patch Changes

- Updated dependencies [f2c025e]
  - @ai-sdk/provider@0.0.21
  - @ai-sdk/openai@0.0.50
  - @ai-sdk/provider-utils@1.0.14

## 0.0.27

### Patch Changes

- Updated dependencies [f42d9bd]
  - @ai-sdk/openai@0.0.49

## 0.0.26

### Patch Changes

- Updated dependencies [6ac355e]
  - @ai-sdk/provider@0.0.20
  - @ai-sdk/openai@0.0.48
  - @ai-sdk/provider-utils@1.0.13

## 0.0.25

### Patch Changes

- dd712ac: fix: use FetchFunction type to prevent self-reference
- Updated dependencies [4ffbaee]
- Updated dependencies [dd712ac]
  - @ai-sdk/openai@0.0.47
  - @ai-sdk/provider-utils@1.0.12

## 0.0.24

### Patch Changes

- Updated dependencies [89b18ca]
- Updated dependencies [dd4a0f5]
  - @ai-sdk/openai@0.0.46
  - @ai-sdk/provider@0.0.19
  - @ai-sdk/provider-utils@1.0.11

## 0.0.23

### Patch Changes

- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/provider-utils@1.0.10
  - @ai-sdk/provider@0.0.18
  - @ai-sdk/openai@0.0.45

## 0.0.22

### Patch Changes

- Updated dependencies [029af4c]
  - @ai-sdk/provider@0.0.17
  - @ai-sdk/openai@0.0.44
  - @ai-sdk/provider-utils@1.0.9

## 0.0.21

### Patch Changes

- Updated dependencies [d58517b]
- Updated dependencies [c0a73ee]
  - @ai-sdk/provider@0.0.16
  - @ai-sdk/openai@0.0.43
  - @ai-sdk/provider-utils@1.0.8

## 0.0.20

### Patch Changes

- Updated dependencies [96aed25]
  - @ai-sdk/provider@0.0.15
  - @ai-sdk/openai@0.0.42
  - @ai-sdk/provider-utils@1.0.7

## 0.0.19

### Patch Changes

- Updated dependencies [9614584]
- Updated dependencies [0762a22]
- Updated dependencies [7a2eb27]
  - @ai-sdk/provider-utils@1.0.6
  - @ai-sdk/openai@0.0.41

## 0.0.18

### Patch Changes

- b99180b5: feat (provider/azure): update Azure API version to 2024-06-01

## 0.0.17

### Patch Changes

- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5
  - @ai-sdk/provider@0.0.14
  - @ai-sdk/openai@0.0.40

## 0.0.16

### Patch Changes

- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4
  - @ai-sdk/openai@0.0.39

## 0.0.15

### Patch Changes

- Updated dependencies [2b9da0f0]
- Updated dependencies [909b9d27]
- Updated dependencies [a5b58845]
- Updated dependencies [4aa8deb3]
- Updated dependencies [13b27ec6]
  - @ai-sdk/provider@0.0.13
  - @ai-sdk/openai@0.0.38
  - @ai-sdk/provider-utils@1.0.3

## 0.0.14

### Patch Changes

- 68066fea: feat (provider/azure): add baseURL configuration option

## 0.0.13

### Patch Changes

- Updated dependencies [89947fc5]
  - @ai-sdk/openai@0.0.37

## 0.0.12

### Patch Changes

- Updated dependencies [b7290943]
  - @ai-sdk/provider@0.0.12
  - @ai-sdk/openai@0.0.36
  - @ai-sdk/provider-utils@1.0.2

## 0.0.11

### Patch Changes

- Updated dependencies [d481729f]
  - @ai-sdk/provider-utils@1.0.1
  - @ai-sdk/openai@0.0.35

## 0.0.10

### Patch Changes

- 5edc6110: feat (ai/core): add custom request header support
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider@0.0.11
  - @ai-sdk/openai@0.0.34
  - @ai-sdk/provider-utils@1.0.0

## 0.0.9

### Patch Changes

- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16
  - @ai-sdk/openai@0.0.33

## 0.0.8

### Patch Changes

- Updated dependencies [1b37b8b9]
  - @ai-sdk/openai@0.0.32

## 0.0.7

### Patch Changes

- eba071dd: feat (@ai-sdk/azure): add azure openai completion support
- 1ea890fe: feat (@ai-sdk/azure): add azure openai completion support
- Updated dependencies [eba071dd]
- Updated dependencies [1ea890fe]
  - @ai-sdk/openai@0.0.31

## 0.0.6

### Patch Changes

- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15
  - @ai-sdk/openai@0.0.30

## 0.0.5

### Patch Changes

- 4728c37f: feat (core): add text embedding model support to provider registry
- 7910ae84: feat (providers): support custom fetch implementations
- Updated dependencies [4728c37f]
- Updated dependencies [7910ae84]
  - @ai-sdk/openai@0.0.29
  - @ai-sdk/provider-utils@0.0.14

## 0.0.4

### Patch Changes

- Updated dependencies [f9db8fd6]
  - @ai-sdk/openai@0.0.28

## 0.0.3

### Patch Changes

- Updated dependencies [fc9552ec]
  - @ai-sdk/openai@0.0.27

## 0.0.2

### Patch Changes

- Updated dependencies [7530f861]
  - @ai-sdk/openai@0.0.26

## 0.0.1

### Patch Changes

- 8b1362a7: feat (@ai-sdk/azure): add Azure OpenAI provider
- Updated dependencies [8b1362a7]
  - @ai-sdk/openai@0.0.25
