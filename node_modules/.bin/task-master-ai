#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules/task-master-ai/mcp-server/node_modules:/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules/task-master-ai/node_modules:/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules:/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules/task-master-ai/mcp-server/node_modules:/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules/task-master-ai/node_modules:/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules:/Users/<USER>/Cursor projects/LifeQuest AI/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules/task-master-ai/mcp-server/server.js" "$@"
else
  exec node  "$basedir/../.pnpm/task-master-ai@0.13.2_@types+node@18.19.101_react@19.1.0/node_modules/task-master-ai/mcp-server/server.js" "$@"
fi
