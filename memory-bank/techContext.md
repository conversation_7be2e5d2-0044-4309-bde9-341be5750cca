# Tech Context: LifeQuest AI

## 1. Core Technologies (Cloudflare Edge-First Architecture)

- **Primary Language for Workers:** TypeScript / JavaScript.
- **Telegram Bot Handling:** Cloudflare Worker processing Telegram Webhooks.
    - Routing within Worker: Manual routing in `src/index.ts` for the primary webhook. `itty-router` was removed due to issues.
- **Backend Logic:** Implemented within Cloudflare Workers.
- **AI Orchestration & LLM Calls:** Cloudflare Workers AI (e.g., Mistral, LLaMA models). Langchain (JS version: `langchain-js`) might be used for specific complex chains or prompt templating if compatible and beneficial within the Worker environment.
    - Implemented via [`src/workersAIClient.ts`](lifequest-cf-worker/src/workersAIClient.ts), which now routes to native Cloudflare AI (if `AI_MODEL_ID` starts with `@cf/`) or to OpenRouter (if `AI_MODEL_ID` is set to an OpenRouter model identifier and `OPENROUTER_API_KEY` is configured).
- **Database (Relational):** Supabase (PostgreSQL) - remains the primary structured data store.
    - Client: `@supabase/supabase-js` for interaction from Cloudflare Workers, via [`src/supabaseClient.ts`](lifequest-cf-worker/src/supabaseClient.ts).
- **Vector Store & RAG:** Cloudflare Vectorize, accessed directly from Workers.
    - Embeddings: Generated using a configurable model ID (`CF_EMBEDDING_MODEL_ID`), defaulting to `@cf/baai/bge-m3`, via the `AI` binding.
    - Implemented via [`src/vectorizeClient.ts`](lifequest-cf-worker/src/vectorizeClient.ts), which interacts with the `VECTORIZE_INDEX` binding for storage and querying.
    - **Metadata Strategy:** Uses `vector_owner_id` (string) as the key in vector metadata for user-specific filtering. Requires an explicit metadata index for `vector_owner_id` on the Vectorize index for reliable filtering.
- **Data Validation/Serialization:** Native TypeScript interfaces, primarily the `Env` interface defined in [`src/types/index.ts`](lifequest-cf-worker/src/types/index.ts).
- **Environment Management:** 
    - **Deployed:** Cloudflare Secrets for API keys and sensitive configurations, accessed as environment bindings in Workers. These correspond to the `Env` interface.
    - **Local Development:** A [`.dev.vars`](lifequest-cf-worker/.dev.vars:0) file in the worker's root directory (`lifequest-cf-worker/`) is used with `wrangler dev`. Key names in `.dev.vars` must match the `Env` interface definitions in [`src/types/index.ts`](lifequest-cf-worker/src/types/index.ts) (e.g., `TELEGRAM_BOT_TOKEN`, `SUPABASE_ANON_KEY`, `SUPABASE_URL`, `SYSTEM_PROMPT_GIST_URL`, `AI_MODEL_ID`, `OPENROUTER_API_KEY`, `CF_EMBEDDING_MODEL_ID`). Bindings like `AI`, `VECTORIZE_INDEX`, `DB` are configured in `wrangler.toml` and are also part of the `Env` type.

## 2. Development Setup & Tools

- **Runtime Environment:** Cloudflare Workers.
- **Dependency Management:** `npm` with a [`package.json`](lifequest-cf-worker/package.json:0) file.
- **Version Control:** Git, hosted on a private repository.
- **Deployment Tool:** `wrangler` CLI.
- **IDE:** User's choice, with strong TypeScript/JavaScript support (e.g., VS Code).
- **Task Management:** Task Master AI (CLI and/or MCP server) for managing development tasks.
- **Local Development:** `wrangler dev` (run as `npm run dev` from within the `lifequest-cf-worker` directory) for running Workers locally with Miniflare. `console.log` output is visible in the terminal. Secrets are loaded from `.dev.vars`.

## 3. Key Technical Dependencies (Cloudflare Edge-First)

- `typescript`
- `wrangler` (CLI for Cloudflare Workers development and deployment)
- `@supabase/supabase-js` (Supabase client library for JavaScript/TypeScript)
- `@cloudflare/workers-types` (TypeScript definitions for Cloudflare Workers, including base for `Env`)
- `@grammyjs/types` (For Telegram API type safety)
- Cloudflare Workers AI bindings (provided by Cloudflare platform, via `wrangler.jsonc`, typed in `Env`)
- Cloudflare Vectorize bindings (provided by Cloudflare platform, via `wrangler.jsonc`, typed in `Env`)
- (Potentially) `langchain-js` or specific sub-packages if used within Workers.
- (Potentially) `zod` or similar for data validation if native types become complex.

## 4. Environment Variables & Secrets (`Env` Interface in `src/types/index.ts`)

Sensitive credentials and configurations are managed via Cloudflare Secrets (deployed) or [`.dev.vars`](lifequest-cf-worker/.dev.vars:0) (local). The structure is defined by the `Env` interface in [`src/types/index.ts`](lifequest-cf-worker/src/types/index.ts):

```typescript
export interface Env {
  TELEGRAM_BOT_TOKEN: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  AI: any; // Binding to Workers AI (e.g., Fetcher)
  VECTORIZE_INDEX: any; // Binding to Vectorize index (e.g., VectorizeIndex)
  DB: any; // Binding to D1 database (e.g., D1Database) - if used
  SYSTEM_PROMPT_GIST_URL?: string; // Optional URL for system prompt
  GITHUB_GIST_PAT?: string; // Optional PAT for private Gist access
  APP_ENV?: string;
  LOG_LEVEL?: string;
  MAIN_MODEL_ID?: string; // Optional: Main model ID for LLM calls, fallback for AI_MODEL_ID
  AI_MODEL_ID?: string; // Optional: Specific model ID for AI operations (e.g., '@cf/meta/llama-2-7b-chat-fp16' or 'google/gemini-1.5-flash' for OpenRouter)
  OPENROUTER_API_KEY?: string; // Optional: API Key for OpenRouter, required if using OpenRouter models via AI_MODEL_ID
  CF_EMBEDDING_MODEL_ID?: string; // Optional: Specific model ID for Cloudflare embeddings (e.g., '@cf/baai/bge-m3')
}
```
- Keys in `.dev.vars` must match these definitions (e.g., `TELEGRAM_BOT_TOKEN`, `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `MAIN_MODEL_ID`, `AI_MODEL_ID`, `OPENROUTER_API_KEY`, `CF_EMBEDDING_MODEL_ID`).
- Bindings (`AI`, `VECTORIZE_INDEX`, `DB`) are configured in `wrangler.toml` and made available on the `env` object passed to the worker, conforming to this interface.

## 5. Technical Constraints & Considerations (Cloudflare Edge-First)

- **MVP Scope:** Focus on core functionality for a single user, leveraging the scalability of the Cloudflare platform.
- **Privacy:** All data and interactions are private to the user. Cloudflare Secrets manage sensitive information.
- **Human-in-the-Loop:** Critical operations require user confirmation, with logic handled by Workers.
- **Stateless AI Core:** AI interactions rely on dynamically built context, with the system prompt and other configurations managed via Cloudflare Secrets/bindings, accessible through the `Env` object. Model selection for LLMs is controlled by `AI_MODEL_ID` (routing to native Cloudflare or OpenRouter) and `OPENROUTER_API_KEY`.
- **Cloudflare Worker Limitations:**
    - CPU time limits per request.
    - Memory limits.
    - Subrequest limits.
    - Startup time (cold starts).
    - These need to be considered for complex operations, potentially requiring breaking down tasks or using Queues/Durable Objects for longer processes.
- **Cloudflare Vectorize Limitations:** Vector dimensions, query rates, storage capacity.
- **Workers AI Model Availability & Capabilities:** Choice of LLMs and embedding models is limited to what Cloudflare Workers AI supports natively (when `AI_MODEL_ID` starts with `@cf/`). For a broader selection, OpenRouter can be used if `AI_MODEL_ID` is set accordingly and `OPENROUTER_API_KEY` is provided. Embeddings currently use `@cf/baai/bge-m3` (configurable by `CF_EMBEDDING_MODEL_ID`) via `vectorizeClient.ts`.
- **API Rate Limits:** Supabase API rate limits. Telegram API rate limits.
- **No DevOps Overhead:** A key goal is to minimize/eliminate traditional server management and deployment pipelines by relying on Cloudflare's managed services.
- **TypeScript/JavaScript Ecosystem:** Development will primarily use the TypeScript/JavaScript ecosystem.
- **Local Development (`wrangler dev` CWD):** It is critical to run `wrangler dev` (e.g., via `npm run dev`) from the root of the worker project (`lifequest-cf-worker/` in this case), not from a parent directory. This ensures `wrangler.jsonc` and `.dev.vars` are found and processed correctly, enabling proper loading of environment variables/secrets and bindings, and ensuring `console.log` output is visible.