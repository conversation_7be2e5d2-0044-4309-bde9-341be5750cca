# Project Brief: LifeQuest AI (MVP)

## 1. Introduction / General Concept

LifeQuest AI is a personal Telegram bot-coach designed for life gamification. It incorporates built-in game logic, maintains a history of achievements, tracks progress, and features a deep "memory" based on RAG (Retrieval Augmented Generation). The project is inspired by <PERSON><PERSON><PERSON><PERSON><PERSON>'s methodology, best practices in gamification, conscious growth, dream tracking, and reflection. The MVP is designed for a single user (the developer) with a private setup, built with future scalability in mind.

## 2. Key Goals and Scenarios

### Goals:
- Gamify and structure life, increasing engagement and awareness.
- Automatically (via AI) and manually (by user) maintain a history of quests, XP, victories, lessons, mistakes, and reflections.
- Provide the AI with an "intelligent memory" – relevant RAG context where important records are searched by meaning rather than date.

### Main UX Scenario:
- Interaction occurs via a Telegram bot using text-based dialogue and quick commands.
- The AI proactively engages the user (e.g., "How's your energy? What's your focus for the day?"), assists with planning, offers gamified challenges, and tracks progress.
- All game state updates and achievements require user confirmation, except for obvious actions initiated via commands.

## 3. Scope: Key MVP Features

### 3.1 Dialogue and Gamification:
- All interactions via Telegram bot (commands or free-form text).
- `/checkin`: Daily ritual (AI inquires about energy, suggests mini-quests, reflection, routines).
    - Current: User can optionally provide energy level (1-10), which is saved. AI responds asking about daily focus.
- AI can suggest saving insights to memory, requiring user confirmation.
- Game rewards (XP, coins) are proposed by AI and confirmed by the user.
- Support for routines/daily tasks, including AI-recommended "streaks" with creation/tracking/bonus logic.

### 3.2 Game State, Quests, and "Hall of Fame":
- **Game State:** Tracks current XP, coins, energy, skills, active quests (main/side/routine), streaks, and seasonal focus.
- **Quests:** CRUD operations (AI or manual creation; AI or `/complete_quest` for completion; potential automation for routines).
- **"Hall of Fame":**
    - View: `/hall show [filters]` (by type, period, tag; Markdown, emoji).
    - Add: `/hall add [text]` (AI suggests template, user confirms/augments).
    - AI reminds to add important moments.

### 3.3 Memory and RAG:
- After key interactions (check-in, quest completion, `/summary`, deep dialogue), AI extracts key facts (using GPT-3.5-Turbo) and asks "Save to memory?" before adding to Cloudflare Vectorize (with metadata: tag, date, importance, source, user_id).
- GPT-4.x prompts use a chain-of-context: last N messages, game_state, top 3-5 relevant RAG fragments, current query.
- Key artifacts (Dream, Self, core audits) can be integrated into RAG as knowledge docs.
- Full RAG integration via Langchain.

### 3.4 Telegram Bot Commands (MVP):
- `/help`: Brief help.
- `/checkin [energy]`: Daily ritual.
    - `[energy_level (1-10)]`: Optional. Logs user's energy level (1-10). Prompts for daily focus.
- `/summary [period/quest/topic]`: AI summarizes and asks to save insights.
- `/stats`: Full status (XP, stats, active quests, routines, top Hall of Fame).
- `/quests [status/type]`: View quests.
- `/add_quest [desc]`: Add quest/routine (AI assists with template).
- `/complete_quest [id/desc]`: Manually complete quest.
- `/hall show [filter]`: View Hall of Fame.
- `/hall add [text]`: Manually add to Hall of Fame.
- `/language [style/lang]`: (Optional) Change communication style/language.
- **Excluded from MVP:** `/save`, `/export`, `/pause`, `/resume`.

### 3.5 Response Formatting:
- Always Markdown, with templates, emojis, structure, progress bars, sections (skills, achievements, summaries).
- Style: Game-motivational, sometimes epic/humor/business, user-selectable.

## 4. Component Backlog (MVP Focus)

1.  **[MVP]** Telegram bot functionality hosted on Cloudflare Workers (TypeScript/JavaScript).
2.  **[MVP]** Backend logic implemented as Cloudflare Workers (TypeScript/JavaScript).
3.  **[MVP]** Supabase/PostgreSQL (schema for users, chat, quests, hall_of_fame, game_state, knowledge - remains the primary structured data store).
4.  **[MVP]** Cloudflare Vectorize integration (direct interaction from Cloudflare Workers for RAG).
5.  **[MVP]** Workers AI for LLM interactions (e.g., Mistral, LLaMA via Cloudflare).
6.  **[MVP]** Full history, update confirmations via chat/inline buttons (logic handled by Cloudflare Workers).
7.  **[MVP]** Basic commands and game logic (implemented within Cloudflare Workers).

*(V2 features like proactive AI, multi-user, export, integrations, UI/web are out of MVP scope).*