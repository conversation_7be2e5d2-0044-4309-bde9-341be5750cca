# Product Context: LifeQuest AI

## 1. The "Why" - Problem Solved

LifeQuest AI aims to address the challenge of maintaining long-term engagement, structure, and conscious progress in personal development. It provides a gamified framework to make goal-setting, habit-tracking, and reflection more engaging and sustainable for a single user initially. The core problem it solves is the difficulty in consistently applying personal growth methodologies and maintaining motivation over time.

## 2. Expected Product Behavior

- **Interactive Coaching:** The system acts as a personal Telegram bot-coach, engaging the user in text-based dialogue.
- **Gamification Core:** Implements game logic (XP, coins, quests, streaks) to motivate and track progress.
- **Intelligent Memory (RAG):** Leverages Retrieval Augmented Generation to provide contextually relevant information and insights from past interactions and stored knowledge, rather than simple chronological recall.
- **User-Confirmed Actions:** Critical game state changes (e.g., awarding XP, saving memories) require explicit user confirmation ("human-in-the-loop").
- **Proactive Engagement (MVP Light):** The AI initiates daily check-ins, suggests mini-quests, and prompts for reflection.
- **Structured Information:** Delivers information in a clear, structured Markdown format with emojis and templates.
- **Privacy-Focused:** All data and interactions are private to the user in the MVP.

## 3. User Experience Objectives

- **Engaging & Motivating:** The primary UX goal is to make personal development feel like an engaging game, increasing user involvement and adherence to their goals.
- **Structured & Clear:** Provide a clear overview of progress, active quests, and achievements through well-formatted text and commands.
- **Personalized & Adaptive:** The AI should feel like a personal coach that understands the user's context (via RAG) and adapts its suggestions and interactions.
- **Seamless Interaction:** Interaction via Telegram should be intuitive, using a mix of natural language and clear commands.
- **Empowering:** The user should feel in control, with the AI acting as a supportive tool that helps them achieve their self-defined objectives.
- **Reflective:** Encourage regular reflection and learning through AI prompts and the "Hall of Fame" mechanic.

## 4. Key User Interaction Scenarios (MVP)

### 4.1. Daily Check-in:
- User initiates `/checkin [energy_level]`.
- AI responds based on energy, asks about daily priorities, potentially forming a mini-quest.
    - User can optionally provide an energy level (1-10). This is saved to their game state.
    - AI acknowledges the check-in and asks about the user's main focus for the day.
    - (Future: AI might remind about active routines, e.g., "Don't forget your X routine - streak is Y days! +1 XP for today?").
- User confirms/declines, game state is updated.
    - (Future: User confirms/declines routine suggestions or other AI proposals).

### 4.2. Quest Completion:
- User reports completing a task (e.g., "I finished task X!" or `/complete_quest [id/desc]`).
- AI proposes XP/coin rewards (e.g., "Count 5 XP for this? (Yes/No)").
- User confirms, game state updates.
- AI may then ask: "Should this event be added to the Hall of Fame/Memory?" User decides.

### 4.3. Summaries:
- User requests a summary (e.g., `/summary week`).
- AI compiles a report of key moments and insights for the period.
- AI asks which (if any) extracted insights from the summary should be saved to RAG memory.

### 4.4. Hall of Fame Interaction:
- User manually adds an achievement: `/hall add "Overcame fear of public speaking at meetup"`.
- Entry is saved to the database and, if desired, to RAG.
- User views entries: `/hall show [filters]`.
- Output is beautifully formatted Markdown with emojis, potentially filtered by period, theme, or personal quotes.