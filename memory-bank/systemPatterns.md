# System Patterns: LifeQuest AI

## 1. System Architecture Overview

LifeQuest AI will employ a serverless, edge-first architecture leveraging Cloudflare's suite of products. Telegram interactions will be handled by a Cloudflare Worker, which will also orchestrate backend logic, AI interactions (via Workers AI), RAG processes (with Cloudflare Vectorize), and data persistence (to Supabase PostgreSQL).

```mermaid
graph TD
    User -- Telegram Webhook --> TelegramWorker[Cloudflare Worker (TypeScript/JavaScript) - Telegram Handler]
    
    TelegramWorker -- Functions/Logic --> CoreLogicWorker[Cloudflare Worker(s) - Core Logic]
    
    CoreLogicWorker -- DB Calls (JS Client/REST) --> Supabase[Supabase (PostgreSQL)]
    CoreLogicWorker -- Vector Ops --> CloudflareVectorize[Cloudflare Vectorize]
    CoreLogicWorker -- LLM Inference --> WorkersAI[Cloudflare Workers AI (Mistral, LLaMA, etc.)]
    
    Supabase --> UserData[User Data, Settings]
    Supabase --> Messages[Message History]
    Supabase --> GameState[Game State (XP, Coins, etc.)]
    Supabase --> Quests[Quests, Routines, Streaks]
    Supabase --> HallOfFame[Hall of Fame Entries]
    Supabase --> KnowledgeDocs[Knowledge Documents (Dream, Self)]
    Supabase --> PendingConfirmations[Pending Confirmations Table]
    
    CloudflareVectorize --> VectorEmbeddings[Vector Embeddings of Insights]

    subgraph "Data Stores"
        Supabase
        CloudflareVectorize
    end

    subgraph "AI Core (Cloudflare)"
        WorkersAI
        CloudflareVectorize
    end

    subgraph "Compute (Cloudflare)"
        TelegramWorker
        CoreLogicWorker
    end

    CloudflareSecrets[Cloudflare Secrets] --> TelegramWorker
    CloudflareSecrets --> CoreLogicWorker
```

## 2. Key Technical Decisions & Design Patterns

- **Telegram Webhook on Cloudflare Workers:** All Telegram bot interactions are processed by a Cloudflare Worker.
- **TypeScript/JavaScript for Workers:** Primary development language.
- **Serverless Backend Logic:** All backend logic in Cloudflare Workers.
- **Workers AI for LLM Inference:** Leveraging models like Mistral or LLaMA.
- **Direct Cloudflare Vectorize Integration:** For RAG processes.
    - Implemented via [`src/vectorizeClient.ts`](lifequest-cf-worker/src/vectorizeClient.ts) which handles embedding generation (using configurable `CF_EMBEDDING_MODEL_ID`, defaulting to `@cf/baai/bge-m3`), vector storage, and querying via the `VECTORIZE_INDEX` binding.
    - **Metadata Filtering Key:** Uses `vector_owner_id` (string) as the metadata key for user-specific vector filtering. An explicit metadata index for `vector_owner_id` is required on the Vectorize index. (Initial attempts to use `user_id` as the key were unsuccessful for filtering).
    - Vector data is stored with `vector_owner_id` and other relevant fields from `MemoryRAGChunk`.
    - Queries use `filter: { vector_owner_id: String(userId) }`.
- **Supabase as Primary Database:** For structured relational data.
- **Retrieval Augmented Generation (RAG) on the Edge:**
    - Pattern: `User Query (Telegram) -> Worker -> [Optional: Query Embedding via vectorizeClient.generateTextEmbedding] -> vectorizeClient.querySimilarMemories (uses VectorizeIndex) -> Retrieve Chunks -> Inject into Workers AI Prompt (via workersAIClient) -> Worker -> Telegram Response`.
    - Memory Storage: `... -> Worker -> vectorizeClient.storeMemoryVector (generates embedding, uses VectorizeIndex) -> Stored in Vectorize`.
    - Memory text and metadata (including `vector_owner_id`, original text, user ID, and other relevant fields from `MemoryRAGChunk`) are stored in Cloudflare Vectorize by the Worker using `vectorizeClient.storeMemoryVector`.
- **Human-in-the-Loop (HITL) & Confirmation Flow (Task #24 - Implemented & Tested):**
    - Critical actions require explicit user confirmation via Telegram inline buttons.
    - **`callback_data` Format:** `cfm:<confirmation_uuid>:<choice>` (e.g., `cfm:uuid_v4:y`).
    - **Temporary State Management:** A Supabase table (`pending_confirmations`) stores details of pending confirmations (UUID, `user_telegram_id`, action type, payload, `original_message_id`, expiration).
    - **Secure Processing:** Involves UUID validation, `user_telegram_id` matching against `callback_query.from.id`, expiration check, and deletion of the confirmation record after processing to ensure idempotency.
    - The Cloudflare Worker (`telegramHandler.ts` and `confirmationService.ts`) handles parsing `callback_data`, validating the request, executing the confirmed/cancelled action (e.g., calling `memoryService.saveMemoryInsight` or `gameStateService.awardXp`), and editing the original Telegram message.
    - The `sendConfirmationMessage` utility in [`src/utils/telegramUtils.ts`](lifequest-cf-worker/src/utils/telegramUtils.ts:0) is used to send the initial message with inline buttons.
- **Stateless AI Interactions (System Prompt in Secrets):** Core AI logic relies on a comprehensive system prompt augmented with dynamic context.
- **Modular Worker Functions:** Logic organized into services (e.g., `aiService.ts`, `questService.ts`, `confirmationService.ts`, `memoryService.ts`, `gameStateService.ts`) and clients (e.g., [`supabaseClient.ts`](lifequest-cf-worker/src/supabaseClient.ts), [`workersAIClient.ts`](lifequest-cf-worker/src/workersAIClient.ts), [`vectorizeClient.ts`](lifequest-cf-worker/src/vectorizeClient.ts)).
- **Cloudflare Secrets for Configuration (Deployed):** Secure management of API keys and configurations in the deployed environment.
- **`.dev.vars` for Local Development Secrets:** Used with `wrangler dev` (run via `npm run dev` from worker subdirectory) to load local secrets.
- **Wrangler CLI for Development & Deployment:** Primary tool for Cloudflare Workers.
- **Standardized Supabase Error Handling (Implemented via Task #41):**
  - **Custom Error Types:** Utilizes `AppError` and a more specific `SupabaseError` class (defined in `lifequest-cf-worker/src/errors/AppError.ts`) for consistent error objects.
  - **Error Handling Utilities (`lifequest-cf-worker/src/utils/supabaseErrorUtils.ts`):**
    - `handleSupabaseError(error: PostgrestError, context?: Record<string, any>): never`: Takes a `PostgrestError`, logs its `message`, `code`, `details`, and `hint` along with any provided context, then throws a new `SupabaseError` instance that includes these details. This ensures detailed logging for all caught Supabase errors.
    - `processSupabaseResponse<T>(response: { data: T | null; error: PostgrestError | null }, context?: Record<string, any>): T`: A generic utility to process Supabase client responses. If `response.error` is present, it calls `handleSupabaseError`. It also logs a warning if `response.data` is `null` when no error occurred (useful for `.maybeSingle()` or list results where null data without an error might be unexpected or needs specific handling by the caller). It returns `response.data` (cast to `T`) if no error is thrown.
  - **Service Integration:** All service functions that interact with Supabase now use `processSupabaseResponse` for queries that return data (especially those using `.select().single()` or list results) or use a try/catch block around Supabase calls, calling `handleSupabaseError` if `response.error` is present for simpler writes (e.g., inserts without a select, deletes). This results in functions consistently throwing `SupabaseError` on database interaction failures, which can then be caught and handled by calling functions (e.g., in `telegramHandler.ts` or other services).
  - **Benefits:** Provides consistent, detailed error logging for Supabase issues, standardized error types for easier catching and handling, and clearer separation of error processing logic.
- **Telegram MarkdownV2 Formatting Strategy:**
    - **Core Principle:** When using `parse_mode: 'MarkdownV2'` (or `isPreformatted: true` which implies it in `sendTelegramMessage`), the *entire final string* sent to Telegram must be valid MarkdownV2.
    - **Dynamic Content:** All dynamic data (e.g., user input, database content like quest titles, dates) *must* be escaped before being inserted into a MarkdownV2 string. Use a robust, centralized escape function (e.g., [`formatter.ts#escapeMarkdown`](lifequest-cf-worker/src/utils/formatter.ts)) that:
        - Escapes backslashes (`\\`) *first* (`\` -> `\\`).
        - Then escapes all other MarkdownV2 reserved characters (`_`, `*`, `[`, `]`, `(`, `)`, `~`, `` ` ``, `>`, `#`, `+`, `-`, `=`, `|`, `{`, `}`, `.`, `!`) by prepending a single backslash (e.g., `.` -> `\.`).
    - **Static String Literals:**
        - For purely decorative elements or text already conforming to MarkdownV2 syntax (e.g., `*Header Text*`, `---`, `A simple sentence without special chars.`), these can be used as literals.
        - For static strings containing characters that *require* MarkdownV2 escaping (e.g., `This is important! Note the period.`), it is safest to explicitly pass these literals through a robust escape function (like `escapeMarkdownV2` in [`telegramHandler.ts`](lifequest-cf-worker/src/telegramHandler.ts) or the equivalent [`formatter.ts#escapeMarkdown`](lifequest-cf-worker/src/utils/formatter.ts)) before concatenation, rather than relying on manual backslash pre-escaping within the string literal itself (e.g., prefer `escapeMarkdownV2("Important.")` over `"Important\\."`).
    - **Syntax:** Use single asterisks for bold (`*text*`) and single underscores for italics (`_text_`).
    - **Debugging:** If parsing errors occur:
        - Log the final assembled string *before* sending to Telegram to inspect its raw content.
        - Implement and use a character-by-character validation function to check the final string for unescaped reserved characters, logging their context.
        - Ensure the deployed worker code is up-to-date using unique log markers.

## 3. Component Relationships & Data Flow

### 3.1. User Interaction Flow (Cloudflare Edge-First):
1. User sends message/command via Telegram.
2. Telegram sends webhook to Cloudflare Worker.
3. Worker (`src/index.ts`) routes to `telegramHandler.ts`.
4. Handler processes, calling services.
5. Services interact with Supabase, Workers AI, Vectorize.
6. Prompts for Workers AI are dynamically built.
7. AI response processed by Worker.
8. Worker formats and sends response to Telegram.

### 3.2. RAG Memory Population Flow (Cloudflare Edge-First):
1. Worker processes dialogue, uses Workers AI to identify potential memories.
2. Worker presents memories to user for confirmation (Yes/No inline buttons).
3. If "Yes":
    - Memory text vectorized.
    - Vector, text, metadata stored in Cloudflare Vectorize by Worker.
    - Memory text and metadata (including original text, user ID, and other relevant fields) are stored in Cloudflare Vectorize by the Worker using `vectorizeClient.storeMemoryVector`. This function internally uses `vectorizeClient.generateTextEmbedding` (leveraging Workers AI) and then `env.VECTORIZE_INDEX.insert()`.

### 3.3. RAG Contextual Prompt Building Flow (Cloudflare Edge-First):
1. User query received by Worker.
2. Worker function retrieves `game_state`, `messages` (Supabase). If query text needs to be vectorized for similarity search, it uses `vectorizeClient.generateTextEmbedding`.
3. Worker function retrieves `memory_fragments` using `vectorizeClient.querySimilarMemories` (which interacts with `env.VECTORIZE_INDEX`, filtering by `vector_owner_id`).
4. Components assembled into prompt for Workers AI.

### 3.4. User Confirmation Flow (Task #24 - Implemented & Tested):
1.  **Initiation (e.g., via Test Commands like `/test_confirm_insight` or `/test_confirm_xp`):
    *   An action requiring confirmation (e.g., saving an insight) triggers the flow.
    *   A Worker function generates a unique UUID for the confirmation.
    *   A record is created in the `pending_confirmations` table in Supabase, storing the UUID, `user_telegram_id`, `action_type` (e.g., `SAVE_MEMORY_INSIGHT`), `action_payload` (data for the action), `original_message_id` (of the prompt message), and an `expires_at` timestamp.
    *   The Worker sends a Telegram message with inline buttons using the `sendConfirmationMessage` utility from [`src/utils/telegramUtils.ts`](lifequest-cf-worker/src/utils/telegramUtils.ts:0). The `callback_data` for these buttons is formatted as `cfm:<confirmation_uuid>:y` and `cfm:<confirmation_uuid>:n`.
2.  **Callback Query Handling:**
    *   User clicks an inline button. Telegram sends a `callback_query` to the Worker.
    *   The Worker's `telegramHandler.ts` (or a dedicated confirmation service) parses the `callback_data` to extract the `cfm:` prefix, the `<confirmation_uuid>`, and the user's `<choice>` ('y' or 'n').
    *   The Worker queries the `pending_confirmations` table using the extracted UUID.
    *   **Security Checks:**
        *   Verify the `callback_query.from.id` matches the `user_telegram_id` in the fetched record.
        *   Verify the current time is before `expires_at`.
    *   **Action Execution:**
        *   If valid and record found:
            *   The appropriate logic is executed based on `action_type`, `action_payload`, and the user's `<choice>`.
            *   The original Telegram message (identified by `original_message_id`) is edited to reflect the outcome (e.g., "Insight saved!", "Action cancelled.").
            *   The record is deleted from `pending_confirmations` to ensure idempotency.
        *   If invalid (UUID not found, user mismatch, expired): Log the event and inform the user (e.g., "This confirmation has expired or is invalid.").

## 4. Database Schema (Supabase - PostgreSQL)

(Refer to [`pdr.txt:299`](pdr.txt:299) "Дополнение №3: Детальная SQL-схема Supabase PostgreSQL" for the full schema. Key tables include):
- `users` (PK: `id` integer, `telegram_id` bigint UNIQUE)
- `messages`
- `game_state` (PK: `id` integer, `user_id` integer UNIQUE FK to `users.id`, `xp` integer, `last_updated` timestamp)
- `quests`
- `hall_of_fame` (PK: `id` integer, `user_id` integer FK to `users.id`, `content` text, `metadata` jsonb)
- `knowledge_documents`
- **`pending_confirmations` (Implemented & Schema Validated):**
    - `id` (UUID, PK)
    - `user_telegram_id` (BIGINT, FK to `users.telegram_id`)
    - `action_type` (TEXT, NOT NULL)
    - `action_payload` (JSONB)
    - `original_message_id` (BIGINT, NOT NULL)
    - `created_at` (TIMESTAMPTZ, DEFAULT `now()`, NOT NULL)
    - `expires_at` (TIMESTAMPTZ, NOT NULL)

## 5. Vector Store Structure (Cloudflare Vectorize)

(Refer to [`pdr.txt:408`](pdr.txt:408) "Структура данных для Cloudflare Vectorize (RAG)" for details. The structure is now formally defined by the `MemoryRAGChunk` interface in [`src/types/index.ts`](lifequest-cf-worker/src/types/index.ts). Key fields per memory chunk):

```typescript
export interface MemoryRAGChunk {
  id: string; // UUID for the memory chunk, used as the vector ID in Vectorize
  user_id: number; // The user's primary ID from the users table (Supabase users.id)
  text: string; // The actual text content of the memory (this is what gets embedded)
  // vector?: number[]; // The embedding vector (generated from 'text', not stored in Vectorize metadata directly)
  source_type: string; // e.g., "dialogue_insight", "checkin_summary", "hall_of_fame", "knowledge_doc"
  source_id?: string | number; // ID of the original source (e.g., message_id, quest_id, document_id)
  importance_score?: number; // (1-10) Assigned by LLM or user
  timestamp: string; // ISO string for when the original event occurred or memory was logged
  tags?: string[]; // Keywords or categories
  metadata?: Record<string, any>; // For any other flexible key-value pairs specific to this chunk
}
```
- When data is inserted into Cloudflare Vectorize via `env.VECTORIZE_INDEX.insert(vectorId, vector, metadataPayload)`:
    - `vectorId` is the `MemoryRAGChunk.id` (UUID).
    - `vector` is the embedding generated from `MemoryRAGChunk.text`.
    - `metadataPayload` is an object containing all other fields from `MemoryRAGChunk` (i.e., `vector_owner_id` (derived from `user_id`), `text` (original), `source_type`, `source_id`, `importance_score`, `timestamp`, `tags`, and `metadata`). This allows for filtering queries against these fields.

## 6. Critical Implementation Paths

- **RAG Pipeline:** End-to-end flow for "intelligent memory".
- **Game State Management:** Accurate and timely updates (core logic for XP award tested).
- **Human-in-the-Loop Confirmations:** Reliable and user-friendly mechanism (Task #24 - **Implemented & Tested Locally**).
- **Langchain Orchestration (If used in Workers):** Correct configuration for AI tasks.

## 7. Proposed Project Structure (Cloudflare Worker - TypeScript)

```
lifequest-cf-worker/
├── src/
│   ├── index.ts
│   ├── telegramHandler.ts
│   ├── supabaseClient.ts
│   ├── vectorizeClient.ts
│   ├── workersAIClient.ts
│   ├── services/
│   │   ├── gameStateService.ts
│   │   ├── questService.ts
│   │   ├── memoryService.ts
│   │   ├── hallOfFameService.ts
│   │   └── confirmationService.ts // (NEW - for handling confirmation logic)
│   ├── types/
│   │   └── index.ts
│   └── utils/
│       └── telegramUtils.ts // (NEW - for Telegram specific utilities like sendConfirmationMessage)
├── wrangler.toml
├── package.json
├── tsconfig.json
├── .env.example
└── README.md
```

## Data Retrieval Patterns

### Pattern: Efficient Retrieval of Large Chat Histories from Supabase

*   **Context:** Needed for features requiring extensive conversational context, such as the True Conversational Mode (Task #44), where up to ~200,000 tokens of chat history might be processed.
*   **Problem:** Retrieving a very large number of messages from the `messages` table in Supabase can lead to performance issues, database timeouts, or exceed Cloudflare Worker execution limits if done naively.
*   **Strategy/Pattern:**
    1.  **Batched Iterative Retrieval:** Fetch messages in manageable batches (e.g., 50-100 messages per batch) rather than a single large query.
    2.  **Keyset Pagination (Recommended):** Use a composite key, typically `("timestamp", id)` (where `id` is a sequential primary key), for pagination. This involves ordering by `"timestamp" DESC, id DESC` and using a `WHERE ("timestamp", id) < (last_timestamp, last_id)` clause for subsequent batches. This is generally more performant than `LIMIT`/`OFFSET` for large tables.
    3.  **Critical Database Indexing:** Ensure a composite index exists on the fields used for querying and ordering to support efficient pagination (e.g., `(user_id, "timestamp" DESC, id DESC)` on the `messages` table).
    4.  **Approximate In-Flight Token Counting:** During the iterative fetch, use a heuristic (e.g., `message.content.length / 3.5`) to estimate the token count of accumulating messages. Stop fetching when the target token count (plus a small buffer) is approached.
    5.  **Chronological Assembly:** While fetching newest-to-oldest for efficient querying with `DESC` order, prepend messages to the results array to ensure the final list is in oldest-to-newest chronological order for prompt construction.
    6.  **Error Handling:** Implement retries with exponential backoff for transient database errors, respecting Cloudflare Worker time limits.
    7.  **Safety Break:** Include a maximum iteration limit in the fetch loop as a failsafe.
*   **Benefits:** Improved performance, avoids hitting execution limits, controlled resource usage, robust handling of large datasets.
*   **Applicability:** Primarily for fetching extended chat history from the `messages` table. Could be adapted for other scenarios involving retrieval of large, ordered datasets from Supabase within Worker constraints.
*   **Files Involved:** `telegramHandler.ts` (or a dedicated message service), `supabaseClient.ts`.

### Pattern: Dynamic Context Budgeting for LLM Prompts

*   **Context:** When constructing prompts for Large Language Models (LLMs) that have a fixed maximum context window (e.g., ~200k tokens for LifeQuest AI's target model), and the prompt needs to include multiple dynamic components (e.g., system prompt, extended chat history, RAG results, game state, user query).
*   **Problem:** How to allocate the available token budget among different context components, particularly ensuring that the most variable component (like extended chat history) fits without exceeding the overall limit, and what to do if the assembled prompt is still too large.
*   **Strategy/Pattern:**
    1.  **Identify Fixed and Variable Components:**
        - System prompt, game state representation, and the current user query are often relatively fixed or have predictable token counts.
        - RAG results might have a target count.
        - Extended chat history is the most variable.
    2.  **Pre-calculate Budget for Variable History:**
        a. Use an *approximate* token counting method (e.g., `string.length / 3.5`) to estimate the token size of all *other* components that will go into the prompt (system prompt, game state, current query, placeholders/estimates for RAG results).
        b. Subtract this sum from the LLM's absolute maximum context window (e.g., 200,000 tokens). Apply a small safety margin (e.g., subtract an additional 5-10%) to this result.
        c. The remaining value becomes the `dynamicTokenTargetForHistory`.
    3.  **Pass Dynamic Target to History Fetcher:**
        - The function responsible for fetching chat history (e.g., `messageService.getExtendedChatHistory`) should accept this `dynamicTokenTargetForHistory` as a parameter (e.g., via its `options.tokenTarget`).
        - The history fetcher will then aim to retrieve messages up to this dynamic, pre-calculated budget using its own internal approximate counting.
    4.  **Centralized Prompt Assembly:**
        - A dedicated service or function (e.g., in `memoryService.ts` or `workersAIClient.ts`) orchestrates budgeting and component assembly.
    5.  **Final Precise Token Counting:**
        - After all components (including the dynamically budgeted history and formatted RAG/Game State) are gathered and preliminarily assembled into the target `LLMMessage[]` structure, perform a *precise* token count using an LLM-specific tokenizer (e.g., `tokenizerUtil.countPromptTokens`).
    6.  **Apply Truncation Strategies (If Necessary - Subtask `44.6`):**
        - If the precise token count from step 5 exceeds the LLM's context window limit (minus a small safety buffer, e.g., 10-20 tokens):
            * **A. Truncate Extended Chat History First:**
                * **Method:** "Keep Most Recent." Iteratively remove the oldest messages (from the start of the chat history message array) one by one.
                * Recalculate the total prompt token count after each removal.
                * Stop when the total token count is within the limit.
            * **B. Truncate/Reduce RAG Results Second (If history truncation is insufficient):**
                1. **Reduce Number of Snippets:** Progressively reduce the number of included RAG snippets (e.g., from top 5 to top 3, then to top 1). Recalculate tokens.
                2. **Truncate Snippet Content:** If still too long, or as a complementary step, truncate the `content` field of each *included* RAG snippet to a predefined maximum character/token length during its formatting for the prompt. Recalculate tokens.
            * **C. Game State and System Prompt:** Generally, Game State should be concise by design and not dynamically truncated. The System Prompt and Current User Query should not be truncated.
            * **D. Logging:** Log all truncation actions, including original/final token counts and what was modified.
        - **LLM-based Summarization of Context:** Deferred post-MVP as a more advanced strategy if simple truncation proves insufficient for context quality.
    7. **Benefits:** Maximizes relevant context, robustly stays within LLM limits, decouples budgeting and truncation from individual component fetchers/formatters, provides a clear order of operations for handling oversized prompts.
    * **Applicability:** LLM interactions requiring assembly of multiple, variable-sized context sources under a fixed token limit.
    * **Files Involved (Conceptual & Implementation):** Prompt assembly services (e.g., `memoryService.ts`, `workersAIClient.ts`), `messageService.ts`, `tokenizerUtil.ts`.
