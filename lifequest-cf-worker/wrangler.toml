name = "lifequest-cf-worker"
main = "src/index.ts"
compatibility_date = "2025-05-20"
keep_vars = true # RESTORED: Prevent wrangler from deleting dashboard-set variables
compatibility_flags = ["nodejs_compat"]
account_id = "bd8c2004042360ec3e6276e52b9f44b1"

# Rule for WASM files
[[rules]]
globs = ["**/*.wasm"]
type = "CompiledWasm"

# Top-level bindings (can be used for default/dev environment if no specific env is targeted)
[ai]
binding = "AI"

[[vectorize]]
binding = "VECTORIZE_INDEX"
index_name = "lifequest-ai-memory-index"

# [vars]
# LOG_LEVEL = "INFO" 

[env.production]
name = "lifequest-cf-worker" # Explicitly target this service name for production
# Production environment inherits or redefines bindings here
[env.production.ai]
binding = "AI"

[[env.production.vectorize]]
binding = "VECTORIZE_INDEX"
index_name = "lifequest-ai-memory-index"

# Define non-secret environment-specific variables here
[env.production.vars]
LOG_LEVEL = "DEBUG"
AI_MODEL_ID = "openai/gpt-4.1" 
AI_MODEL_MAX_TOKENS = "500000" # Updated to 0.5M for gpt-4.1
MAIN_MODEL_ID = "google/gemini-1.5-pro-preview" # Example, adjust as needed or remove if AI_MODEL_ID is primary
SYSTEM_PROMPT_GIST_URL = "https://gist.githubusercontent.com/donhiddenname/4dc30be8fdecebdcde5d4ae97c9048b4/raw/57cd27402492db01674e5f01ad9519d6e24e2915/system_prompt.txt" # Replace with your actual Gist URL
CF_EMBEDDING_MODEL_ID = "@cf/baai/bge-m3" 