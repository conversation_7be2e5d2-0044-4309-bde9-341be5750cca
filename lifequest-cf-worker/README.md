# LifeQuest AI (Cloudflare Worker)

## Overview

LifeQuest AI is a personal Telegram bot-coach designed for life gamification. It's built as a serverless application running on Cloudflare Workers, leveraging Workers AI for intelligence, Cloudflare Vectorize for Retrieval Augmented Generation (RAG) memory, and Supabase (PostgreSQL) as its primary structured data store. 

This MVP (Minimum Viable Product) is tailored for a single user (the developer) for personal use, focusing on core gamification mechanics, intelligent memory, and daily coaching interactions via Telegram.

## Architecture

The system uses a Cloudflare-first, edge-native architecture:
-   **Telegram <PERSON>t Handler:** A Cloudflare Worker (`src/index.ts`, `src/telegramHandler.ts`) processes incoming webhooks from Telegram.
-   **Backend Logic & Services:** Core application logic is implemented in TypeScript within various services (e.g., `questService.ts`, `gameStateService.ts`, `memoryService.ts`) running as part of the Cloudflare Worker.
-   **AI & LLM Calls:** Handled by `src/workersAIClient.ts`, which interfaces with Cloudflare Workers AI (for models like Llam<PERSON> or Mistral) or can route to OpenRouter for other models (e.g., Google Gemini), based on the `AI_MODEL_ID` environment variable.
-   **RAG & Vector Storage:** `src/vectorizeClient.ts` manages interactions with Cloudflare Vectorize. 
       - Embeddings are generated using a model configurable via `CF_EMBEDDING_MODEL_ID` (defaulting to `@cf/baai/bge-m3`, 1024 dimensions).
       - **Metadata Strategy:** Uses `vector_owner_id` (string) as the key in vector metadata for user-specific filtering. An explicit metadata index for `vector_owner_id` on the Vectorize index is required for reliable filtering (see setup below).
       - Stores/queries vectors in the `VECTORIZE_INDEX` binding.
-   **Database:** Supabase (PostgreSQL) is used for all structured data (users, game state, quests, messages, etc.), accessed via `src/supabaseClient.ts`.

## Features (MVP)

Key Telegram commands available in the MVP:
-   `/help`: Brief help and list of commands.
-   `/checkin [energy]`: Daily ritual to log energy (1-10), review routine streaks, and set daily focus.
-   `/summary [period/quest/topic]`: AI generates a summary for the specified scope and asks to save insights to RAG memory.
-   `/stats`: Displays full user status (XP, coins, energy, active quests, routines, Hall of Fame highlights, streaks).
-   `/quests [status/type]`: View quests with optional filters.
-   `/add_quest [description]`: Add a new quest or routine.
-   `/complete_quest [id/desc]`: Manually complete a quest.
-   `/hall_show [filter]`: View Hall of Fame entries with filters.
-   `/hall_add [text]`: Manually add an entry to the Hall of Fame.
-   *(Note: `/language` is optional and may not be fully implemented in the initial MVP).*

## Prerequisites

-   Node.js (latest LTS recommended) and npm (or pnpm/yarn).
-   A Cloudflare account.
-   `wrangler` CLI installed and configured globally (`npm install -g wrangler`).
-   A Supabase account and a project created.
-   A Telegram Bot created via BotFather to get a `TELEGRAM_BOT_TOKEN`.
-   (Optional) An OpenRouter API Key if you plan to use models not available directly on Workers AI.

## Setup Instructions (Local Development)

1.  **Clone the Repository:**
    ```bash
    git clone <repository_url>
    cd LifeQuestAI/lifequest-cf-worker 
    ```
2.  **Install Dependencies:**
    ```bash
    npm install
    ```
3.  **Set up Supabase Database:**
    -   Refer to the SQL schema in the project's PRD (`pdr.txt`, "Дополнение №3: Детальная SQL-схема Supabase PostgreSQL") to create the required tables in your Supabase project: `users`, `messages`, `game_state`, `quests`, `hall_of_fame`, `knowledge_documents`, `pending_confirmations`.
4.  **Configure Environment Variables:**
    -   Create a `.dev.vars` file in the `lifequest-cf-worker/` directory.
    -   Populate it with your credentials and configurations. Use the following template:

        ```ini
        TELEGRAM_BOT_TOKEN="your_telegram_bot_token"
        SUPABASE_URL="your_supabase_project_url" # e.g., https://<project_ref>.supabase.co
        SUPABASE_ANON_KEY="your_supabase_anon_key"
        
        # Optional: For fetching system prompt from a Gist
        SYSTEM_PROMPT_GIST_URL="url_to_raw_gist_file_for_system_prompt"
        GITHUB_GIST_PAT="your_github_pat_if_gist_is_private"
        
        # AI Model Configuration (see src/types/index.ts for Env interface)
        # Example for Cloudflare Llama 3 8B Instruct:
        AI_MODEL_ID="@cf/meta/llama-3-8b-instruct"
        # Example for OpenRouter (e.g., Google Gemini Flash):
        # AI_MODEL_ID="google/gemini-1.5-flash"
        # OPENROUTER_API_KEY="your_openrouter_api_key" # Required if using OpenRouter models

        # Embedding Model for RAG (Cloudflare Default)
        CF_EMBEDDING_MODEL_ID="@cf/baai/bge-m3"

        # Logging Level (DEBUG, INFO, WARN, ERROR)
        LOG_LEVEL="DEBUG"
        ```

5.  **Configure Cloudflare Vectorize Index:**
    -   In your Cloudflare Dashboard, navigate to Workers & Pages -> Vectorize.
    -   Create a new Vectorize Index.
    -   Name: `lifequest-ai-memory-index` (or as configured in `wrangler.toml`).
    -   Dimensions: `1024` (for `@cf/baai/bge-m3` model).
    -   **Create Metadata Index for User Filtering (Crucial for RAG):** After creating the main index, run the following command in your terminal (in the `lifequest-cf-worker/` directory):
        ```bash
        npx wrangler vectorize create-metadata-index lifequest-ai-memory-index --property-name=vector_owner_id --type=string
        ```
    -   Ensure your `wrangler.toml` file has the correct binding for this index:
        ```toml
        [[vectorize]]
        binding = "VECTORIZE_INDEX" # Must match 'VECTORIZE_INDEX' in src/types/index.ts Env interface
        index_name = "lifequest-ai-memory-index"
        ```

6.  **Run Locally:**
    ```bash
    npm run dev
    ```
    This will start a local server, typically on `http://localhost:8787`.

7.  **Set up Webhook Tunnel (for local development):**
    -   Telegram webhooks require an HTTPS URL. Use a tunneling service like `cloudflared` to expose your local server.
        ```bash
        # Install cloudflared if you haven't: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/
        cloudflared tunnel --url http://localhost:8787
        ```
    -   Take the `https` URL provided by `cloudflared` (e.g., `https://your-tunnel-id.cfargotunnel.com`).
    -   Set your Telegram bot's webhook URL to `https://your-tunnel-id.cfargotunnel.com/telegram/${TELEGRAM_BOT_TOKEN}` (replace with your actual token).
        You can do this via a `curl` command or a simple script (ensure your TELEGRAM_BOT_TOKEN is url-encoded if it contains special characters, though typically they don't):
        ```bash
        curl "https://api.telegram.org/bot<YOUR_TELEGRAM_BOT_TOKEN>/setWebhook?url=https://your-tunnel-id.cfargotunnel.com/telegram/<YOUR_TELEGRAM_BOT_TOKEN>"
        ```

## Testing

-   **Unit & Integration Tests:** Run all automated tests using Vitest:
    ```bash
    npm test
    ```
    (Ensure you are in the `lifequest-cf-worker/` directory).
-   **End-to-End (E2E) Testing:** 
    -   Performed manually by interacting with the Telegram bot via a Telegram client.
    -   Refer to `TEST_PLAN_MVP.md` for high-level test cases and scenarios.

## Deployment to Cloudflare

1.  **Set Cloudflare Secrets & Variables:**
    -   **Secrets (API Keys):** Use `wrangler secret put <SECRET_NAME>` or the Cloudflare Dashboard (Worker -> Settings -> Variables -> Add secret variable) to set:
        -   `TELEGRAM_BOT_TOKEN`
        -   `SUPABASE_URL`
        -   `SUPABASE_ANON_KEY`
        -   `OPENROUTER_API_KEY` (if using OpenRouter models)
        -   `GITHUB_GIST_PAT` (if using a private Gist for the system prompt)
    -   **Configuration Variables (via `wrangler.toml`):** Other operational configurations like `AI_MODEL_ID`, `MAIN_MODEL_ID`, `CF_EMBEDDING_MODEL_ID`, `SYSTEM_PROMPT_GIST_URL`, and `LOG_LEVEL` for the `production` environment are now managed directly in your `wrangler.toml` file under the `[env.production.vars]` section. Ensure these are correctly set there before deploying.
    -   Your `wrangler.toml` should specify the target service name for the production environment (e.g., `name = "lifequest-cf-worker"` under `[env.production]`) and have `keep_vars = true` at the top level to ensure these TOML-defined vars are applied correctly while preserving any other dashboard-only secrets/variables.

2.  **Deploy:**
    ```bash
    # Ensure you are in the lifequest-cf-worker directory
    npx wrangler deploy --env production
    ```

3.  **Update Production Webhook:**
    -   After deployment, `wrangler deploy` will output the URL of your deployed worker (e.g., `https://lifequest-cf-worker.<your-account>.workers.dev`).
    -   Update your Telegram bot's webhook to point to this production URL: 
        `https://lifequest-cf-worker.<your-account>.workers.dev/telegram/${TELEGRAM_BOT_TOKEN}`.

## Important Notes on Cloudflare Configuration

- **`wrangler.toml` as Source of Truth for Production Config:**
    - The `production` environment in `wrangler.toml` is configured with `name = "lifequest-cf-worker"`. This ensures that `npx wrangler deploy --env production` targets this specific service.
    - Non-secret configurations for production (e.g., `AI_MODEL_ID`, `LOG_LEVEL`, `SYSTEM_PROMPT_GIST_URL`, `CF_EMBEDDING_MODEL_ID`) are defined under `[env.production.vars]` in `wrangler.toml`.
    - True secrets (API keys like `TELEGRAM_BOT_TOKEN`, `SUPABASE_ANON_KEY`, `OPENROUTER_API_KEY`) should be set using `wrangler secret put` or via the "Secrets" section in the Cloudflare dashboard for the `lifequest-cf-worker` service.
- **`keep_vars = true`:** This top-level setting in `wrangler.toml` is important. When deploying, it tells Wrangler to preserve any existing plaintext variables or secrets already set in the dashboard *that are not defined in the `wrangler.toml` for the target environment*. Since our key operational variables for production are defined in `[env.production.vars]`, they will be applied from the TOML, and `keep_vars` helps avoid accidentally wiping other dashboard-managed settings.
- **Vectorize Metadata Indexing:** For reliable RAG functionality, an explicit metadata index must be created for the field used to filter vectors by user (currently `vector_owner_id`). This is done via the `wrangler vectorize create-metadata-index` command *before* data relying on that filter is written.

## Project Structure

(Refer to "Дополнение №2: Примерная структура папок и файлов проекта (Cloudflare Worker - TypeScript)" in `pdr.txt` for a detailed file structure diagram.)

Key directories within `lifequest-cf-worker/src/`:
-   `services/`: Contains business logic for different domains (gameState, quests, memory, etc.).
-   `types/`: Holds TypeScript interfaces and type definitions (including `Env`, `MemoryRAGChunk`, Supabase DB types).
-   `utils/`: Utility functions (e.g., formatters, error handlers).
-   `index.ts`: Main worker entry point, request router.
-   `telegramHandler.ts`: Handles processing of Telegram updates and commands.
-   `supabaseClient.ts`: Supabase client setup.
-   `vectorizeClient.ts`: Cloudflare Vectorize interactions.
-   `workersAIClient.ts`: Cloudflare Workers AI / OpenRouter interactions.

## Admin & Maintenance

-   **User Data Reset:** For details on how to completely reset or delete data for a specific user (from Supabase and Vectorize), refer to [`docs/UserDataResetProcedure.md`](docs/UserDataResetProcedure.md). 