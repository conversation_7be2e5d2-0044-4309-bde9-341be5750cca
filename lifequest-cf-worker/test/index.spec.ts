import { env as cfEnv, createExecutionContext, waitOnExecutionContext, SELF } from 'cloudflare:test';
import { describe, it, expect, vi } from 'vitest';
import worker from '../src/index';
import type { Env } from '../src/types';

// For now, you'll need to do something like this to get a correctly-typed
// `Request` to pass to `worker.fetch()`.
const IncomingRequest = Request<unknown, IncomingRequestCfProperties>;

describe('Hello World worker', () => {
	it('responds with Hello World! (unit style)', async () => {
		const request = new IncomingRequest('http://example.com');
		// Create an empty context to pass to `worker.fetch()`.
		const ctx = createExecutionContext();
		const mockEnv: Env = {
			TELEGRAM_BOT_TOKEN: 'test-token',
			SUPABASE_URL: 'http://localhost:54321',
			SUPABASE_ANON_KEY: 'test-anon-key',
			AI: { run: vi.fn() },
			VECTORIZE_INDEX: { insert: vi.fn(), query: vi.fn() },
			DB: { prepare: vi.fn().mockReturnThis(), bind: vi.fn().mockReturnThis(), run: vi.fn(), all: vi.fn(), first: vi.fn() },
			// ...add other optional fields from Env if needed for specific tests
		};
		const response = await worker.fetch(request, mockEnv, ctx);
		// Wait for all `Promise`s passed to `ctx.waitUntil()` to settle before running test assertions
		await waitOnExecutionContext(ctx);
		expect(await response.text()).toMatchInlineSnapshot(`"LifeQuest AI Worker is running!"`);
	});

	it('responds with Hello World! (integration style)', async () => {
		// For integration tests, we use the env from 'cloudflare:test' 
		// which should be configured via .dev.vars or wrangler.toml for bindings
		const response = await SELF.fetch('https://example.com');
		expect(await response.text()).toMatchInlineSnapshot(`"LifeQuest AI Worker is running!"`);
	});
});
