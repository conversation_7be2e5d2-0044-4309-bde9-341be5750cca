import { createClient, SupabaseClient } from '@supabase/supabase-js';
// Ensure you have run `supabase gen types typescript --project-id <your-project-id> --schema public > src/types/supabase.ts`
// and that the path below is correct.
// If the types are not generated yet, this import will cause an error.
import { Database } from './types/supabase'; 
import { Env } from './types';

// Singleton instance of the Supabase client.
// It's initialized on the first call to getSupabaseClient.
let supabaseInstance: SupabaseClient<Database>;

/**
 * Returns a singleton instance of the Supabase client.
 * Initializes the client on the first call.
 * 
 * @param env The environment object containing SUPABASE_URL and SUPABASE_ANON_KEY.
 * @returns The Supabase client instance.
 * @throws Error if SUPABASE_URL or SUPABASE_ANON_KEY are not defined in the environment.
 */
export function getSupabaseClient(env: Env): SupabaseClient<Database> {
  if (!supabaseInstance) {
    if (!env.SUPABASE_URL || !env.SUPABASE_ANON_KEY) {
      throw new Error('Supabase URL and Anon Key must be defined in environment variables. Check your .dev.vars file or Cloudflare secrets.');
    }
    supabaseInstance = createClient<Database>(env.SUPABASE_URL, env.SUPABASE_ANON_KEY, {
      auth: {
        persistSession: false, // Recommended for server-side Supabase access like in Workers
        autoRefreshToken: false,
        detectSessionInUrl: false,
      },
      global: {
        // fetch: fetch.bind(globalThis) // Ensure it uses the Worker's fetch if any issues arise with default fetch behavior in CF Workers
      }
    });
  }
  return supabaseInstance;
}

// Optional: A function to reset the client, mainly for testing or specific scenarios
// export function resetSupabaseClientInstance(): void {
//   supabaseInstance = null; // This would cause a type error if supabaseInstance is not nullable
// }
