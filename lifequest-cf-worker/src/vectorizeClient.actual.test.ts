import { vi, describe, it, expect, beforeEach, Mock } from 'vitest';
import { Env } from './types';
import { generateTextEmbedding } from './vectorizeClient'; // Import the actual function
import { log, LogLevel } from './utils/logger';

// Mock utilities early
vi.mock('./utils/logger', async () => {
  const actualLogger = await vi.importActual('./utils/logger');
  return { ...actualLogger, log: vi.fn() };
});

// Mock AI for env.AI.run
const mockAiRun = vi.fn();

const mockEnv = {
  AI: { run: mockAiRun },
  // VECTORIZE_INDEX is not needed for these specific tests
  CF_EMBEDDING_MODEL_ID: '@cf/test-embedding-model',
} as unknown as Env;

const mockEnvNoEmbeddingModel: Partial<Env> = {
  AI: { run: mockAiRun },
  // CF_EMBEDDING_MODEL_ID is undefined
} as unknown as Env;

const mockEnvNoAI = {
  // AI binding is missing
  VECTORIZE_INDEX: { insert: vi.fn(), query: vi.fn() }
} as unknown as Env;

describe('vectorizeClient.generateTextEmbedding (Actual Implementation)', () => {
  beforeEach(() => {
    vi.clearAllMocks(); 
    // Clear mockAiRun specifically as it's the direct dependency here
    mockAiRun.mockClear();
    (log as Mock).mockClear();
  });

  it('should generate embedding successfully with configured model', async () => {
    const mockEmbedding = [0.1, 0.2, 0.3];
    mockAiRun.mockResolvedValueOnce({ data: [mockEmbedding] }); 
    const result = await generateTextEmbedding(mockEnv, 'Test text', 123);
    expect(result).toEqual(mockEmbedding);
    expect(mockAiRun).toHaveBeenCalledWith('@cf/test-embedding-model', { text: ['Test text'] });
    expect(log).toHaveBeenCalledWith(LogLevel.INFO, 'Using configured embedding model: @cf/test-embedding-model', expect.anything());
  });

  it('should use fallback embedding model if CF_EMBEDDING_MODEL_ID is not set', async () => {
    const mockEmbedding = [0.4, 0.5, 0.6];
    mockAiRun.mockResolvedValueOnce({ data: [mockEmbedding] });
    const result = await generateTextEmbedding(mockEnvNoEmbeddingModel as Env, 'Test text no model', 123);
    expect(result).toEqual(mockEmbedding);
    expect(mockAiRun).toHaveBeenCalledWith('@cf/baai/bge-m3', { text: ['Test text no model'] });
    expect(log).toHaveBeenCalledWith(
      LogLevel.WARN, 
      'CF_EMBEDDING_MODEL_ID not set or empty, using fallback embedding model: @cf/baai/bge-m3',
      expect.objectContaining({
        details: expect.objectContaining({
            modelUsed: '@cf/baai/bge-m3',
            source: 'fallback'
        })
      })
    );
  });

  it('should return null for empty text', async () => {
    const result = await generateTextEmbedding(mockEnv, '', 123);
    expect(result).toBeNull();
    expect(mockAiRun).not.toHaveBeenCalled();
    expect(log).toHaveBeenCalledWith(LogLevel.WARN, 'Cannot generate embedding for empty text', expect.anything());
  });

  it('should return null if env.AI is not available', async () => {
    const result = await generateTextEmbedding(mockEnvNoAI, 'Test text', 123);
    expect(result).toBeNull();
    expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'env.AI binding for Workers AI is not available', expect.anything());
  });

  it('should return null if AI run fails or returns unexpected data (e.g. empty data array)', async () => {
    mockAiRun.mockResolvedValueOnce({ data: [] }); 
    const result = await generateTextEmbedding(mockEnv, 'Test text', 123);
    expect(result).toBeNull();
    expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Failed to generate text embedding or response was empty', expect.anything());
  });

  it('should return null if AI run returns no data property', async () => {
    mockAiRun.mockResolvedValueOnce({}); 
    const result = await generateTextEmbedding(mockEnv, 'Test text 2', 123);
    expect(result).toBeNull();
    expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Failed to generate text embedding or response was empty', expect.anything());
  });

  it('should return null if AI run throws an error', async () => {
    mockAiRun.mockRejectedValueOnce(new Error('AI exploded'));
    const result = await generateTextEmbedding(mockEnv, 'Test text', 123);
    expect(result).toBeNull();
    expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Error during text embedding generation', expect.objectContaining({ error: 'AI exploded' }));
  });
}); 