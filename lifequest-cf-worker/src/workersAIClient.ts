import { Env } from './types/index';
import { log, LogLevel, LogContext } from './utils/logger';
import { callOpenRouter } from './clients/openRouterClient';

export interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMResponse {
  response: string;
}

const SERVICE_NAME = 'WorkersAIClient';

async function getRawGistContent(gistUrl: string, pat?: string): Promise<string | null> {
  let rawUrl = gistUrl;
  // Basic conversion: from gist.github.com/user/gistId to gist.githubusercontent.com/user/gistId/raw
  if (gistUrl.includes('gist.github.com')) {
    const parts = gistUrl.split('/');
    const gistId = parts.pop();
    const user = parts.pop();
    if (user && gistId) {
      rawUrl = `https://gist.githubusercontent.com/${user}/${gistId}/raw`;
    } else {
      console.error('Invalid Gist URL format for raw conversion:', gistUrl);
      return null;
    }
  } else if (!gistUrl.includes('/raw')) {
    // If it's already a githubusercontent URL but not raw, try appending /raw
    // This is a heuristic and might need more robust parsing for different URL structures
    if (gistUrl.includes('gist.githubusercontent.com') && !gistUrl.endsWith('/raw')) {
        // Heuristic: try to find a commit hash or branch if present
        // Example: https://gist.githubusercontent.com/username/gistid/raw/commitsha/filename.txt
        // For simplicity, we assume the basic raw URL or that the Gist URL points directly to a raw file if not standard.
        // A more robust solution would inspect the Gist API if direct raw link isn't obvious.
        console.warn('Gist URL does not seem to be a direct raw link, attempting to use as is. Consider direct raw links for reliability:', gistUrl);
    }
  }

  console.log(`Fetching Gist content from: ${rawUrl}`);
  const headers: HeadersInit = {
    'User-Agent': 'LifeQuestAI-Worker',
  };
  if (pat) {
    headers['Authorization'] = `token ${pat}`;
  }

  try {
    const response = await fetch(rawUrl, { headers });
    if (!response.ok) {
      console.error(
        `Failed to fetch Gist: ${response.status} ${response.statusText}`,
        await response.text()
      );
      return null;
    }
    return await response.text();
  } catch (error: any) {
    console.error('Error fetching Gist content:', error.message);
    return null;
  }
}

export async function getSystemPrompt(env: Env): Promise<string | null> {
  const functionName = 'getSystemPrompt';
  const logContext: LogContext = { service: SERVICE_NAME, functionName };
  log(LogLevel.DEBUG, 'Attempting to get system prompt', logContext);

  if (!env.SYSTEM_PROMPT_GIST_URL) {
    log(LogLevel.INFO, 'SYSTEM_PROMPT_GIST_URL not set, no system prompt will be loaded.', logContext);
    return null;
  }

  try {
    // Convert GitHub Gist URL to raw content URL if necessary
    let rawUrl = env.SYSTEM_PROMPT_GIST_URL;
    if (rawUrl.includes('gist.github.com') && !rawUrl.includes('/raw/')) {
      // Basic conversion, might need adjustment for specific Gist structures (e.g., multi-file gists)
      // Assuming it's a URL like https://gist.github.com/username/gist_id
      // and we want the first file's raw URL or a specific named file.
      // This is a simplified approach. A more robust one would fetch the Gist API data first.
      const parts = rawUrl.split('/');
      const gistId = parts[parts.length -1];
      const username = parts[parts.length -2];
      // This will fetch the Gist API and then we need to find the raw_url of the desired file
      const gistApiUrl = `https://api.github.com/gists/${gistId}`;
      const gistApiResponse = await fetch(gistApiUrl, {
        headers: env.GITHUB_GIST_PAT ? { 'Authorization': `token ${env.GITHUB_GIST_PAT}` } : {},
      });
      if (!gistApiResponse.ok) {
        log(LogLevel.ERROR, 'Failed to fetch Gist API data for system prompt', { ...logContext, error: `Status ${gistApiResponse.status}`, payload: { gistApiUrl } });
        return null;
      }
      const gistData = await gistApiResponse.json() as any;
      if (gistData && gistData.files) {
        const firstFileName = Object.keys(gistData.files)[0];
        if (firstFileName && gistData.files[firstFileName] && gistData.files[firstFileName].raw_url) {
          rawUrl = gistData.files[firstFileName].raw_url;
          log(LogLevel.DEBUG, 'Successfully converted Gist URL to raw URL', { ...logContext, payload: { originalUrl: env.SYSTEM_PROMPT_GIST_URL, newRawUrl: rawUrl } });
        } else {
          log(LogLevel.WARN, 'Could not find raw_url in Gist API response for system prompt', { ...logContext, payload: { gistDataFiles: gistData.files } });
          return null;
        }
      } else {
        log(LogLevel.WARN, 'Gist API response did not contain expected files structure for system prompt', { ...logContext, payload: { gistData } });
        return null;
      }
    }

    log(LogLevel.DEBUG, 'Fetching system prompt from raw URL', { ...logContext, payload: { rawUrl } });
    const response = await fetch(rawUrl, {
      headers: env.GITHUB_GIST_PAT && rawUrl.includes('raw.githubusercontent.com') ? { 'Authorization': `token ${env.GITHUB_GIST_PAT}` } : {},
    });

    if (!response.ok) {
      log(LogLevel.ERROR, 'Failed to fetch system prompt content', { ...logContext, error: `Status ${response.status} from ${rawUrl}`});
      return null;
    }
    const promptText = await response.text();
    log(LogLevel.INFO, 'System prompt fetched successfully', { ...logContext, payload: { promptLength: promptText.length } });
    return promptText;
  } catch (error: any) {
    log(LogLevel.ERROR, 'Error fetching system prompt from Gist', { ...logContext, error: error.message, stack: error.stack });
    return null;
  }
}

const DEFAULT_MODEL_ID = '@cf/meta/llama-2-7b-chat-fp16';

export async function runLLM(
  env: Env,
  messages: Message[],
  modelName?: string
): Promise<LLMResponse | null> {
  const functionName = 'runLLM';
  let logContextBase: LogContext = { service: SERVICE_NAME, functionName, payload: { modelOverride: modelName, messageCount: messages.length } }; 
  log(LogLevel.INFO, 'Attempting to run LLM', logContextBase);
  
  if (!env.AI) {
    log(LogLevel.ERROR, 'Workers AI binding (env.AI) is not available.', logContextBase);
    return null;
  }

  let systemPromptText: string | null = null;
  if (!messages.find(m => m.role === 'system')) {
    systemPromptText = await getSystemPrompt(env);
  }

  const messagesToRun: Message[] = systemPromptText
    ? [{ role: 'system' as const, content: systemPromptText }, ...messages]
    : messages;

  // Update log context if system prompt was added
  const logContext = { ...logContextBase, payload: { ...logContextBase.payload, messagesSentCount: messagesToRun.length, systemPromptAdded: !!systemPromptText } };

  const modelIdToUse = env.AI_MODEL_ID || env.MAIN_MODEL_ID || DEFAULT_MODEL_ID;
  log(LogLevel.INFO, `Attempting to use AI model: ${modelIdToUse} (Configured AI_MODEL_ID: ${env.AI_MODEL_ID}, Configured MAIN_MODEL_ID: ${env.MAIN_MODEL_ID}, Default CF Model: ${DEFAULT_MODEL_ID})`, logContextBase);

  if (modelIdToUse.startsWith('@cf/')) {
    log(LogLevel.DEBUG, `Routing to Cloudflare Workers AI with model: ${modelIdToUse}`, logContext);
    try {
      const inputs = {
        messages: messagesToRun,
      };
      const cfResponse: LLMResponse = await env.AI.run(modelIdToUse, inputs);
      log(LogLevel.DEBUG, `Cloudflare AI response received: ${JSON.stringify(cfResponse)}`, logContextBase);

      if (cfResponse && cfResponse.response) {
        log(LogLevel.INFO, 'Cloudflare AI call successful', { ...logContext, payload: { ...logContext.payload, responseLength: cfResponse.response.length } });
        return cfResponse;
      }
      log(LogLevel.WARN, 'Cloudflare AI response does not contain expected text output.', { ...logContext, payload: { cfResponse } });
      return null;
    } catch (error: any) {
      log(LogLevel.ERROR, 'Error running Cloudflare AI model', { ...logContext, error: error.message, stack: error.stack });
      if (error.cause) {
          log(LogLevel.ERROR, 'Cause of Cloudflare AI error:', { ...logContext, payload: { cause: error.cause } });
      }
      return null;
    }
  } else {
    log(LogLevel.DEBUG, `Routing to OpenRouter with model: ${modelIdToUse}`, logContext);
    return await callOpenRouter(env, modelIdToUse, messagesToRun);
  }
}
