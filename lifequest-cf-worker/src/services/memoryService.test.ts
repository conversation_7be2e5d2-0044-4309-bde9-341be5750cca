import { vi, describe, it, expect, beforeEach, Mock } from 'vitest';
import { getSupabaseClient } from '../supabaseClient';
import * as memoryServiceActual from './memoryService';
import { SaveMemoryInsightPayload, Insight, HallOfFameEntry } from './memoryService';
import { Env } from '../types';
import { SupabaseError, AppError } from '../errors/AppError';
import { log, LogLevel } from '../utils/logger';
import * as workersAIClient from '../workersAIClient'; // To mock runLLM
import { User } from './userService';

// Mocks
vi.mock('../supabaseClient', () => ({ getSupabaseClient: vi.fn() }));
vi.mock('../utils/logger', async () => {
  const actual = await vi.importActual('../utils/logger');
  return { ...actual, log: vi.fn() };
});
vi.mock('../workersAIClient', () => ({ runLLM: vi.fn() }));

const mockEnv = { AI_MODEL_ID: 'test-model' } as Env;
const testTelegramUserId = 12345;
const testActualUserId = 1;

const mockUser: User = {
    id: testActualUserId,
    telegram_id: testTelegramUserId,
    created_at: new Date().toISOString(),
    settings: null,
};

describe('Memory Service', () => {
    let mockSupabase: any;
    let mockQueryBuilder: any;

    beforeEach(() => {
        vi.clearAllMocks();
        (log as Mock).mockClear();
        (workersAIClient.runLLM as Mock).mockClear();

        mockQueryBuilder = {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn(),
            insert: vi.fn().mockReturnThis(),
        };
        mockSupabase = { from: vi.fn(() => mockQueryBuilder) };
        (getSupabaseClient as Mock).mockReturnValue(mockSupabase);
    });

    describe('extractInsights', () => {
        it('should extract insights successfully from valid JSON array', async () => {
            const mockLLMResponse = { response: JSON.stringify([{ text: 'Insight 1', importance_score: 8, tags: ['learning'] }]) };
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce(mockLLMResponse);
            const result = await memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv, testTelegramUserId);
            expect(result).toEqual([{ text: 'Insight 1', importance_score: 8, tags: ['learning'] }]);
            expect(workersAIClient.runLLM).toHaveBeenCalled();
        });

        it('should extract insights from JSON with .insights property', async () => {
            const mockLLMResponse = { response: JSON.stringify({ insights: [{ text: 'Insight 2', importance_score: 7, tags: ['decision'] }] }) };
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce(mockLLMResponse);
            const result = await memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv);
            expect(result).toEqual([{ text: 'Insight 2', importance_score: 7, tags: ['decision'] }]);
        });

        it('should return empty array for empty LLM response string after stripping markdown', async () => {
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce({ response: '```json\n```' });
            const result = await memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv);
            expect(result).toEqual([]);
        });
        
        it('should return empty array if both user and AI messages are empty', async () => {
            const result = await memoryServiceActual.extractInsights('', '', mockEnv, testTelegramUserId);
            expect(result).toEqual([]);
            expect(workersAIClient.runLLM).not.toHaveBeenCalled();
        });

        it('should throw AppError if LLM response is null or empty', async () => {
            // First call
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce(null); // Simulates LLMResponse | null where LLMResponse is null
            await expect(memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv))
                .rejects.toThrow(AppError);
            // Second call
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce({ response: '' }); // Simulates LLMResponse with empty response string
            await expect(memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv))
                .rejects.toThrow('LLM did not return a response for insight extraction.');
        });

        it('should throw AppError for invalid JSON structure', async () => {
            // First call
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce({ response: JSON.stringify({ other_prop: [] }) });
            await expect(memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv))
                .rejects.toThrow(AppError);
            // Second call
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce({ response: JSON.stringify({ other_prop: [] }) });
            await expect(memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv))
                .rejects.toThrow('LLM returned JSON in unexpected format for insights.');
        });

        it('should throw AppError if JSON parsing fails', async () => {
            // First call
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce({ response: 'invalid json' });
            await expect(memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv))
                .rejects.toThrow(AppError);
            // Second call
            (workersAIClient.runLLM as Mock).mockResolvedValueOnce({ response: 'invalid json' });
            // The actual error message from JSON.parse('invalid json') in V8/Node is usually:
            // "Unexpected token 'i', "invalid json" is not valid JSON"
            // The service wraps this message.
            await expect(memoryServiceActual.extractInsights('User msg', 'AI resp', mockEnv))
                .rejects.toThrow("Failed to extract insights: Unexpected token 'i', \"invalid json\" is not valid JSON");
        });
    });

    describe('saveMemoryInsight', () => {
        const insightPayload: SaveMemoryInsightPayload = { text: 'Great insight!', title: 'Insight Title', importance_score: 9, tags: ['reflection'] };
        const mockHallOfFameEntry: HallOfFameEntry = {
            id: 1, user_id: testActualUserId, content: 'Great insight!', entry_type: 'general_insight',
            created_at: 'date', metadata: { title: 'Insight Title', importance_score: 9, tags:['reflection'] }, period: null
        };

        it('should save insight successfully', async () => {
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockUser, error: null }); // For user fetch
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockHallOfFameEntry, error: null }); // For HOF insert
            
            const result = await memoryServiceActual.saveMemoryInsight(insightPayload, mockEnv, testTelegramUserId);
            expect(result.success).toBe(true);
            expect(result.message).toBe('Insight saved to Hall of Fame!');
            expect(result.data).toEqual(mockHallOfFameEntry);
            expect(mockSupabase.from).toHaveBeenCalledWith('users');
            expect(mockSupabase.from).toHaveBeenCalledWith('hall_of_fame');
            expect(mockQueryBuilder.insert).toHaveBeenCalledWith(expect.objectContaining({ content: insightPayload.text, user_id: testActualUserId }));
        });

        it('should return error if telegramUserId is not provided', async () => {
            const result = await memoryServiceActual.saveMemoryInsight(insightPayload, mockEnv, 0);
            expect(result.success).toBe(false);
            expect(result.error).toBe('User Telegram ID is required.');
        });

        it('should return error if payload text is empty', async () => {
            const result = await memoryServiceActual.saveMemoryInsight({ ...insightPayload, text: '' }, mockEnv, testTelegramUserId);
            expect(result.success).toBe(false);
            expect(result.error).toBe('Insight text cannot be empty.');
        });

        it('should return error if user fetch fails', async () => {
            const dbError = { message: 'User fetch failed', code: 'DB501', details:'', hint:'' };
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError }); // User fetch fails
            const result = await memoryServiceActual.saveMemoryInsight(insightPayload, mockEnv, testTelegramUserId);
            expect(result.success).toBe(false);
            // The service constructs SupabaseError, so the message will be formatted
            expect(result.error).toBe(`Supabase Error: ${dbError.message} (Code: ${dbError.code})`);
        });

        it('should return error if Hall of Fame insert fails', async () => {
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockUser, error: null }); // User fetch success
            const dbError = { message: 'HOF insert failed', code: 'DB502', details:'', hint:'' };
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError }); // HOF insert fails
            
            const result = await memoryServiceActual.saveMemoryInsight(insightPayload, mockEnv, testTelegramUserId);
            expect(result.success).toBe(false);
            expect(result.error).toBe(`Supabase Error: ${dbError.message} (Code: ${dbError.code})`);
            
            // Expect the log from handleSupabaseError (via processSupabaseResponse)
            expect(log).toHaveBeenCalledWith(
                LogLevel.ERROR, 
                `Supabase Error: ${dbError.message} (Code: ${dbError.code})`,
                expect.objectContaining({
                    details: expect.objectContaining({
                        operation: 'saveInsightToHof.insert',
                        supabaseMessage: dbError.message,
                        supabaseCode: dbError.code
                    })
                })
            );
        });

        it('should return error if HOF insert returns no data unexpectedly', async () => {
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockUser, error: null }); 
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: null }); // HOF insert returns null data

            const result = await memoryServiceActual.saveMemoryInsight(insightPayload, mockEnv, testTelegramUserId);
            expect(result.success).toBe(false);
            expect(result.error).toBe('Failed to save insight (no data returned after DB insert).');
        });
    });
}); 