import { describe, it, expect, vi, beforeEach, afterEach, Mo<PERSON> } from 'vitest';
import {
  GameState,
  GameStateOperations,
  addXP,
  addCoins,
  setEnergy,
  getGameState,
  awardXp,
  updateGameState,
  AwardXpPayload,
} from './gameStateService';
import { getSupabaseClient } from '../supabaseClient';
import { Env } from '../types';
import { Json } from '../types/supabase';
import { SupabaseError } from '../errors/AppError';
import { log, LogLevel } from '../utils/logger';

// Mock Supabase client
vi.mock('../supabaseClient', () => ({
  getSupabaseClient: vi.fn(),
}));

// Mock logger
vi.mock('../utils/logger', async () => {
  const actualLogger = await vi.importActual('../utils/logger');
  return {
    ...actualLogger,
    log: vi.fn(),
  };
});

const mockEnv = {
  // SUPABASE_URL and SUPABASE_ANON_KEY are not directly used by service if client is mocked properly
} as Env;

const createBaseGameState = (overrides: Partial<GameState> = {}): GameState => ({
  id: 1,
  user_id: 123,
  xp: 100,
  coins: 50,
  energy: 75,
  metadata: null,
  last_updated: new Date('2023-01-01T00:00:00.000Z').toISOString(),
  ...overrides,
});

describe('gameStateService', () => {
  let mockSupabaseInstance: any;
  let mockQueryBuilder: any;
  let consoleWarnSpy: any;
  let consoleErrorSpy: any;

  beforeEach(() => {
    vi.clearAllMocks();

    mockQueryBuilder = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      maybeSingle: vi.fn(),
      upsert: vi.fn().mockReturnThis(), // upsert itself returns a builder that can have .select().single()
    };

    // Special handling for upsert().select().single()
    // This setup allows mockQueryBuilder.upsert(...).select().single() to be mocked
    const upsertSelectSingleChain = {
        single: vi.fn()
    };
    const upsertSelectChain = {
        select: vi.fn(() => upsertSelectSingleChain)
    };
    mockQueryBuilder.upsert = vi.fn(() => upsertSelectChain); // upsert returns an object with a select method
    // And make the individual mocks accessible for setting resolved values if needed directly on them
    mockQueryBuilder.single = upsertSelectSingleChain.single; // if single is directly used after eq
    mockQueryBuilder.maybeSingle = vi.fn(); // Default for getGameState
    

    mockSupabaseInstance = {
      from: vi.fn(() => mockQueryBuilder),
    };
    (getSupabaseClient as Mock).mockReturnValue(mockSupabaseInstance);

    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleWarnSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    (log as Mock).mockClear();
    vi.clearAllMocks();
  });

  describe('Pure Helper Functions', () => {
    describe('addXP', () => {
      it('should add positive XP to an existing state', () => {
        const initialState = createBaseGameState({ xp: 100 });
        const newState = addXP(initialState, 50);
        expect(newState.xp).toBe(150);
        expect(newState).not.toBe(initialState); 
        expect(new Date(newState.last_updated!).getTime()).toBeGreaterThan(new Date(initialState.last_updated!).getTime());
      });

      it('should add XP when initial XP is null', () => {
        const initialState = createBaseGameState({ xp: null });
        const newState = addXP(initialState, 50);
        expect(newState.xp).toBe(50);
      });
      
      it('should handle zero amount', () => {
        const initialState = createBaseGameState({ xp: 100 });
        const newState = addXP(initialState, 0);
        expect(newState.xp).toBe(100);
      });
    });

    describe('addCoins', () => {
      it('should add positive coins', () => {
        const initialState = createBaseGameState({ coins: 50 });
        const newState = addCoins(initialState, 20);
        expect(newState.coins).toBe(70);
        expect(newState).not.toBe(initialState);
        expect(new Date(newState.last_updated!).getTime()).toBeGreaterThan(new Date(initialState.last_updated!).getTime());
      });

      it('should add negative coins (spending)', () => {
        const initialState = createBaseGameState({ coins: 50 });
        const newState = addCoins(initialState, -20);
        expect(newState.coins).toBe(30);
      });

      it('should handle initial null coins', () => {
        const initialState = createBaseGameState({ coins: null });
        const newState = addCoins(initialState, 20);
        expect(newState.coins).toBe(20);
      });
    });

    describe('setEnergy', () => {
      it('should set energy to a positive value', () => {
        const initialState = createBaseGameState({ energy: 75 });
        const newState = setEnergy(initialState, 90);
        expect(newState.energy).toBe(90);
        expect(newState).not.toBe(initialState);
        expect(new Date(newState.last_updated!).getTime()).toBeGreaterThan(new Date(initialState.last_updated!).getTime());
      });

      it('should set energy to 0', () => {
        const initialState = createBaseGameState({ energy: 75 });
        const newState = setEnergy(initialState, 0);
        expect(newState.energy).toBe(0);
      });
    });
  });

  describe('Service-Level Functions (with DB Mocks)', () => {
    describe('getGameState', () => {
      it('should return game state if user exists', async () => {
        const mockGameState = createBaseGameState({ id: 1, user_id: 123 });
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: mockGameState, error: null });
        
        const result = await getGameState(123, mockEnv);
        expect(result).toEqual(mockGameState);
        expect(mockSupabaseInstance.from).toHaveBeenCalledWith('game_state');
        expect(mockQueryBuilder.select).toHaveBeenCalledWith('*');
        expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', 123);
        expect(mockQueryBuilder.maybeSingle).toHaveBeenCalled();
      });

      it('should return null if user not found (data is null, error is null)', async () => {
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null });
        const result = await getGameState(404, mockEnv);
        expect(result).toBeNull();
        expect(log).toHaveBeenCalledWith(
          LogLevel.WARN,
          'Supabase response returned null data without an explicit error.',
          expect.objectContaining({ details: expect.objectContaining({ operation: 'fetchGameState' }) })
        );
      });

      it('should throw SupabaseError and log via SupabaseError handler when DB error occurs', async () => {
        const dbError = { message: 'DB read error', code: 'PGRST000', details: 'some details', hint: 'some hint' };
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });
        
        await expect(getGameState(123, mockEnv)).rejects.toThrow(SupabaseError);
        await expect(getGameState(123, mockEnv)).rejects.toThrow(dbError.message);

        expect(log).toHaveBeenCalledWith(
          LogLevel.ERROR,
          `Supabase Error: ${dbError.message} (Code: ${dbError.code})`, 
          expect.objectContaining({
            details: expect.objectContaining({
              operation: 'fetchGameState',
              supabaseMessage: dbError.message,
              supabaseCode: dbError.code
            })
          })
        );
      });

      it('should throw SupabaseError and log if a non-SupabaseError error occurs', async () => {
        const unexpectedError = new Error('Unexpected network issue');
        mockQueryBuilder.maybeSingle.mockRejectedValueOnce(unexpectedError);

        await expect(getGameState(123, mockEnv)).rejects.toThrow(SupabaseError);
        // Check specific message from the wrapped SupabaseError
        await expect(getGameState(123, mockEnv)).rejects.toThrow('Unexpected error fetching game state');
        
        expect(log).toHaveBeenCalledWith(
          LogLevel.ERROR,
          'Unexpected error fetching game state',
          expect.objectContaining({
            error: unexpectedError.message,
            // stack: expect.any(String) // Stack trace can be brittle
          })
        );
      });
    });

    describe('awardXp', () => {
      const telegramIdUnused = 12345; 
      const actualUserId = 789;
      const awardPayload: AwardXpPayload = { amount: 10, reason: 'Test award' };
    
      it('should successfully award XP to a new user (no prior game_state)', async () => {
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: null, error: null })
        };
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        const newGameState = { user_id: actualUserId, xp: 10, coins:0, energy: null, id: 21, last_updated: 'date', metadata: expect.any(Object) };
        const mockUpsertGameStateBuilder = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: newGameState, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined);

        const result = await awardXp(awardPayload, mockEnv, actualUserId);

        expect(result.success).toBe(true);
        expect(result.message).toContain('10 XP awarded');
        const upsertCall = mockUpsertGameStateBuilder.upsert.mock.calls[0][0];
        expect(upsertCall).toEqual(expect.objectContaining({ user_id: actualUserId, xp: 10, coins: 0, energy: null }));
      });

      it('should successfully award XP to an existing user with prior game_state', async () => {
        const existingGameState = { xp: 50, user_id: actualUserId, coins:10, energy:10, id: 1, last_updated: 'old_date', metadata: null };
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: existingGameState, error: null })
        };
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        const mockedReturnedUpsertData = { 
            ...existingGameState,
            xp: existingGameState.xp! + awardPayload.amount,
            last_updated: new Date().toISOString(),
            metadata: {
                last_xp_award: {
                    reason: awardPayload.reason,
                    related_quest_id: awardPayload.related_quest_id,
                    timestamp: new Date().toISOString()
                }
            }
        };
        const mockUpsertGameStateBuilder = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: mockedReturnedUpsertData, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined);
        
        const result = await awardXp(awardPayload, mockEnv, actualUserId);
        
        expect(result.success).toBe(true);
        expect(result.message).toContain('Current XP: 60');

        const upsertCall = mockUpsertGameStateBuilder.upsert.mock.calls[0][0];
        // When only awarding XP, newValues in updateGameState will primarily contain xp, last_updated, and metadata.
        // It should NOT contain coins or energy unless they were also part of operations (which they are not here).
        expect(upsertCall).toEqual(expect.objectContaining({ 
            user_id: actualUserId, 
            xp: 60, 
            // coins: existingGameState.coins, // DO NOT expect these unless they were in operations
            // energy: existingGameState.energy, // DO NOT expect these unless they were in operations
            last_updated: expect.any(String),
            metadata: expect.objectContaining({
                last_xp_award: expect.objectContaining({
                    reason: awardPayload.reason,
                    related_quest_id: awardPayload.related_quest_id, 
                    timestamp: expect.any(String)
                })
            })
        }));
        // Verify that coins and energy were NOT part of the direct upsert payload for an XP-only award
        expect(upsertCall).not.toHaveProperty('coins');
        expect(upsertCall).not.toHaveProperty('energy');
      });

      it('should return error if game_state fetch fails (within updateGameState)', async () => {
        const dbErrorForGet = { message: 'GS fetch failed', code: 'PGRST101', details: '', hint: '' };
        const mockGetGameStateBuilderFailed = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: null, error: dbErrorForGet })
        };
        // User check might not even be reached if getGameState fails and throws
        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilderFailed : undefined);

        const result = await awardXp(awardPayload, mockEnv, actualUserId);
        expect(result.success).toBe(false);
        const expectedErrorMessage = `Supabase Error: ${dbErrorForGet.message} (Code: ${dbErrorForGet.code})`;
        expect(result.error).toBe(`Failed to award XP: ${expectedErrorMessage}`); 
      });

      it('should return error if game_state upsert fails', async () => {
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: { xp: 50, user_id: actualUserId, coins:10, energy:10 }, error: null })
        };
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        const dbErrorUpsert = { message: 'GS upsert failed', code: 'PGRSTXYZ', details: '', hint: '' };
        const mockUpsertGameStateBuilderFailed = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: null, error: dbErrorUpsert })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilderFailed : undefined);

        const result = await awardXp(awardPayload, mockEnv, actualUserId);
        expect(result.success).toBe(false);
        expect(result.error).toContain('GS upsert failed'); // processSupabaseResponse will format this
      });

      it('should return error for invalid XP amount (<=0)', async () => {
        const result = await awardXp({ amount: 0, reason: 'Test' }, mockEnv, actualUserId);
        expect(result.success).toBe(false);
        expect(result.error).toBe('XP amount must be positive.');
      });

      it('should return error if user fetch fails', async () => {
        const resultForFalseyUser = await awardXp(awardPayload, mockEnv, 0 as any);
        expect(resultForFalseyUser.success).toBe(false);
        expect(resultForFalseyUser.error).toBe('User ID is required to award XP.');
      });
    });

    describe('updateGameState', () => {
      const actualUserId = 999;
      const initialMockState = createBaseGameState({ user_id: actualUserId, id: 20, xp: 100, coins: 100, energy: 100 });
      
      it('should initialize and update state for a new user', async () => {
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: null, error: null })
        };
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        const upsertReturnData = {user_id: actualUserId, xp: 10, coins: 0, energy: null, id: 21, last_updated: new Date().toISOString(), metadata: null };
        const mockUpsertGameStateBuilder = {
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValueOnce({ data: upsertReturnData, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined);
        
        const operations: GameStateOperations = { xp_to_add: 10 };
        const result = await updateGameState(actualUserId, operations, mockEnv);

        expect(result.xp).toBe(10);
        expect(result.coins).toBe(0);
        const upsertCallArgs = mockUpsertGameStateBuilder.upsert.mock.calls[0][0];
        expect(upsertCallArgs).toEqual(
          expect.objectContaining({
            user_id: actualUserId,
            xp: 10,
            coins: 0,
            energy: null,
            last_updated: expect.any(String)
          })
        );
      });
      
      it('should update XP for an existing user', async () => {
        // 1. getGameState returns existing state
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: initialMockState, error: null })
        };
        // 2. Pre-upsert user check -> returns user exists
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        // 3. Upsert game_state -> returns updated game state
        const finalState = { ...initialMockState, xp: 110, last_updated: expect.any(String) };
        const mockUpsertGameStateBuilder = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: finalState, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)   // For getGameState
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)        // For user check
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined); // For upsert

        const operations: GameStateOperations = { xp_to_add: 10 };
        const result = await updateGameState(actualUserId, operations, mockEnv);

        expect(result.xp).toBe(110);
        const upsertCallArgs = mockUpsertGameStateBuilder.upsert.mock.calls[0][0];
        expect(upsertCallArgs).toEqual(expect.objectContaining({ user_id: actualUserId, xp: 110 }));
      });

      it('should update coins and energy for an existing user', async () => {
        // 1. getGameState returns existing state
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: initialMockState, error: null })
        };
        // 2. Pre-upsert user check -> returns user exists
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        // 3. Upsert game_state -> returns updated game state
        const finalState = { ...initialMockState, coins: 95, energy: 50, last_updated: expect.any(String) };
        const mockUpsertGameStateBuilder = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: finalState, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined);

        const operations: GameStateOperations = { coins_to_add: -5, energy_to_set: 50 };
        const result = await updateGameState(actualUserId, operations, mockEnv);

        expect(result.coins).toBe(95);
        expect(result.energy).toBe(50);
        const upsertCallArgs = mockUpsertGameStateBuilder.upsert.mock.calls[0][0];
        expect(upsertCallArgs).toEqual(expect.objectContaining({ user_id: actualUserId, coins: 95, energy: 50 }));
      });
      
      it('should update only last_updated if operations object is empty for an existing user', async () => {
        const baseTime = Date.now();
        const slightlyOlderState = { 
            ...initialMockState, 
            last_updated: new Date(baseTime - 10000).toISOString() // 10 seconds ago
        };
        
        // 1. getGameState returns existing state (slightly older)
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: slightlyOlderState, error: null })
        };
        // 2. Pre-upsert user check -> returns user exists
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        
        // 3. Upsert game_state -> This is the data we expect the upsert().select().single() to RETURN
        // It should be the slightlyOlderState but with a brand new last_updated timestamp.
        const finalStateMockReturn = { 
            ...slightlyOlderState, // Carry over all fields from the state that was 'read'
            last_updated: new Date(baseTime + 10000).toISOString() // Mock a distinctly newer timestamp for the DB return
        }; 
        const mockUpsertGameStateBuilder = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: finalStateMockReturn, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined);

        const operations: GameStateOperations = {}; // Empty operations
        const result = await updateGameState(actualUserId, operations, mockEnv);

        // Assertions on the 'result' (which is finalStateMockReturn)
        expect(result.xp).toBe(slightlyOlderState.xp);
        expect(result.last_updated).toBe(finalStateMockReturn.last_updated); // It should be the newer timestamp
        if (result.last_updated && slightlyOlderState.last_updated) { 
            expect(new Date(result.last_updated).getTime()).toBeGreaterThan(new Date(slightlyOlderState.last_updated).getTime());
        }

        // Assertions on what was passed TO the upsert operation
        const upsertCallArgs = mockUpsertGameStateBuilder.upsert.mock.calls[0][0];
        expect(upsertCallArgs).toEqual(expect.objectContaining({
             user_id: actualUserId, 
             xp: slightlyOlderState.xp, 
             coins: slightlyOlderState.coins, 
             energy: slightlyOlderState.energy, 
             last_updated: expect.any(String) // The key is that a new string timestamp was SENT to upsert
        }));
        // Also check that the timestamp sent to upsert is newer than the one read
        if (upsertCallArgs.last_updated && slightlyOlderState.last_updated) {
             expect(new Date(upsertCallArgs.last_updated).getTime()).toBeGreaterThan(new Date(slightlyOlderState.last_updated).getTime());
        }
      });

      it('should throw SupabaseError if user_id is not provided', async () => {
        const operations: GameStateOperations = { xp_to_add: 10 };
        // First call
        await expect(updateGameState(0, operations, mockEnv)).rejects.toThrow(SupabaseError);
        // Second call
        await expect(updateGameState(0, operations, mockEnv)).rejects.toThrow('actualUserId is required to update game state');
         expect(log).toHaveBeenCalledWith(
          LogLevel.ERROR,
          'actualUserId is required to update game state',
          expect.any(Object)
        );
      });
      
      it('should throw SupabaseError if initial getGameState fails with DB error', async () => {
        const dbError = { message: 'Initial GetGameState DB Error', code: 'PGRST111', details: '', hint: '' };
        // Mock for first call to updateGameState -> internal getGameState
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });
        // Mock for second call to updateGameState -> internal getGameState
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });
        // We don't expect upsert to be called if getGameState fails, so no mock needed for upsertSingleMock here for this specific path.

        const operations: GameStateOperations = { xp_to_add: 10 };
        await expect(updateGameState(actualUserId, operations, mockEnv)).rejects.toThrow(SupabaseError);
        await expect(updateGameState(actualUserId, operations, mockEnv)).rejects.toThrow(dbError.message);
        
        expect(log).toHaveBeenCalledWith(
          LogLevel.ERROR,
          `Supabase Error: ${dbError.message} (Code: ${dbError.code})`,
          expect.any(Object)
        );
      });
      
      it('should throw SupabaseError if upsert fails', async () => {
        // Mock for first call to updateGameState -> internal getGameState (succeeds)
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: initialMockState, error: null });
        const dbError = { message: 'Upsert DB Error', code: 'PGRST222', details: '', hint: '' };
        // Mock for first call to updateGameState -> internal upsert (fails)
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });

        // Mock for second call to updateGameState -> internal getGameState (succeeds)
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: initialMockState, error: null });
        // Mock for second call to updateGameState -> internal upsert (fails)
        mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });

        const operations: GameStateOperations = { xp_to_add: 10 };
        await expect(updateGameState(actualUserId, operations, mockEnv)).rejects.toThrow(SupabaseError);
        await expect(updateGameState(actualUserId, operations, mockEnv)).rejects.toThrow(dbError.message);
        expect(log).toHaveBeenCalledWith(
          LogLevel.ERROR,
          `Supabase Error: ${dbError.message} (Code: ${dbError.code})`,
          expect.any(Object)
        );
      });

      it('should throw SupabaseError if upsert succeeds but returns no data', async () => {
        // 1. getGameState returns existing state
        const mockGetGameStateBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), 
            maybeSingle: vi.fn().mockResolvedValueOnce({ data: initialMockState, error: null })
        };
        // 2. Pre-upsert user check -> returns user exists
        const mockUserCheckBuilder = { 
            select: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ count: 1, error: null, data: null })
        };
        // 3. Upsert game_state -> succeeds but returns null data (the scenario to test)
        const mockUpsertGameStateBuilder = { 
            upsert: vi.fn().mockReturnThis(), select: vi.fn().mockReturnThis(), 
            single: vi.fn().mockResolvedValueOnce({ data: null, error: null })
        };

        mockSupabaseInstance.from
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockGetGameStateBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUserCheckBuilder : undefined)
            .mockImplementationOnce((tableName: string) => tableName === 'game_state' ? mockUpsertGameStateBuilder : undefined);

        const operations: GameStateOperations = { xp_to_add: 10 };
        await expect(updateGameState(actualUserId, operations, mockEnv))
            .rejects.toThrow('Upsert succeeded but returned no data unexpectedly.');
      });
    });
  });
}); 