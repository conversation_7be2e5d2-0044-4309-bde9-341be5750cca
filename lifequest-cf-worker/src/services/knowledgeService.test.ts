import { vi, describe, it, expect, beforeEach, Mock, afterEach } from 'vitest';
import { getSupabaseClient } from '../supabaseClient';
import { createKnowledgeDocument, getKnowledgeDocuments, updateKnowledgeDocument, deleteKnowledgeDocument, KnowledgeDocumentInsert, KnowledgeDocumentUpdate, KnowledgeDocument } from './knowledgeService';
import { Env } from '../types';
import { SupabaseError } from '../errors/AppError';
import { LogLevel, log } from '../utils/logger';

// Mock Supabase client
vi.mock('../supabaseClient', () => ({
  getSupabaseClient: vi.fn(),
}));

// Mock logger
vi.mock('../utils/logger', async () => {
  const actual = await vi.importActual('../utils/logger') as any;
  return {
    ...actual,
    log: vi.fn(), 
  };
});

const mockEnv = {} as Env;
const testActualUserId = 123;

describe('Knowledge Service', () => {
  let mockSupabaseInstance: any;
  let mockQueryBuilder: any;
  let deleteInitialEqMock: Mock;
  let deleteSecondEqMock: Mock;

  beforeEach(() => {
    vi.clearAllMocks();
    (log as Mock).mockClear();

    mockQueryBuilder = {
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      order: vi.fn().mockReturnThis(),
    };

    deleteSecondEqMock = vi.fn();
    const firstEqReturnsObjectWithSecondEq = { 
        ...mockQueryBuilder,
        eq: deleteSecondEqMock 
    };
    deleteInitialEqMock = vi.fn().mockReturnValue(firstEqReturnsObjectWithSecondEq);
    mockQueryBuilder.delete = vi.fn(() => ({
        ...mockQueryBuilder,
        eq: deleteInitialEqMock
    }));

    mockSupabaseInstance = {
      from: vi.fn(() => mockQueryBuilder),
    };
    (getSupabaseClient as Mock).mockReturnValue(mockSupabaseInstance);
  });

  describe('createKnowledgeDocument', () => {
    const docData = { title: 'Test Doc', content: 'Test content', document_type: 'TEST_TYPE' };
    const expectedResult: KnowledgeDocument = {
      id: 1,
      user_id: testActualUserId,
      title: 'Test Doc',
      content: 'Test content',
      document_type: 'TEST_TYPE',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    it('should create a knowledge document successfully', async () => {
      mockQueryBuilder.single.mockResolvedValueOnce({ data: expectedResult, error: null });
      const result = await createKnowledgeDocument(testActualUserId, docData, mockEnv);
      expect(result).toEqual(expectedResult);
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(expect.objectContaining({ user_id: testActualUserId, ...docData }));
    });

    it('should throw SupabaseError if actualUserId is not provided', async () => {
        await expect(createKnowledgeDocument(0, docData, mockEnv)).rejects.toThrow(SupabaseError);
        await expect(createKnowledgeDocument(0, docData, mockEnv)).rejects.toThrow('actualUserId is required');
    });

    it('should throw SupabaseError if required docData fields are missing', async () => {
        const invalidData = { ...docData, title: '' };
        await expect(createKnowledgeDocument(testActualUserId, invalidData, mockEnv)).rejects.toThrow(SupabaseError);
        await expect(createKnowledgeDocument(testActualUserId, invalidData, mockEnv)).rejects.toThrow('title, content, and document_type are required');
    });

    it('should throw SupabaseError on error during creation', async () => {
      const dbError = { message: 'Create failed', code: 'DB401', details: '', hint: '' };
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      await expect(createKnowledgeDocument(testActualUserId, docData, mockEnv)).rejects.toThrow(SupabaseError);
      await expect(createKnowledgeDocument(testActualUserId, docData, mockEnv)).rejects.toThrow(dbError.message);
    });
  });

  describe('getKnowledgeDocuments', () => {
    const expectedResults: KnowledgeDocument[] = [
      { id: 1, user_id: testActualUserId, title: 'Doc 1', content: 'Content 1', document_type: 'TYPE_A', created_at: 't', updated_at: 't' },
    ];

    it('should retrieve knowledge documents for a user', async () => {
      mockQueryBuilder.order.mockResolvedValueOnce({ data: expectedResults, error: null });
      const result = await getKnowledgeDocuments(testActualUserId, mockEnv);
      expect(result).toEqual(expectedResults);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', testActualUserId);
    });

    it('should filter by documentType if provided', async () => {
        mockQueryBuilder.order.mockResolvedValueOnce({ data: expectedResults, error: null });
        await getKnowledgeDocuments(testActualUserId, mockEnv, 'TYPE_A');
        expect(mockQueryBuilder.eq).toHaveBeenCalledWith('document_type', 'TYPE_A');
    });
    
    it('should return empty array if no documents found', async () => {
        mockQueryBuilder.order.mockResolvedValueOnce({ data: [], error: null });
        const result = await getKnowledgeDocuments(testActualUserId, mockEnv);
        expect(result).toEqual([]);
    });

    it('should throw SupabaseError on error during retrieval', async () => {
      const dbError = { message: 'Fetch failed', code: 'DB402', details: '', hint: '' };
      mockQueryBuilder.order.mockResolvedValueOnce({data: null, error: dbError});
      mockQueryBuilder.order.mockResolvedValueOnce({data: null, error: dbError});
      await expect(getKnowledgeDocuments(testActualUserId, mockEnv)).rejects.toThrow(SupabaseError);
      await expect(getKnowledgeDocuments(testActualUserId, mockEnv)).rejects.toThrow(dbError.message);
    });
  });

  describe('updateKnowledgeDocument', () => {
    const documentId = 1;
    const updates: KnowledgeDocumentUpdate = { title: 'Updated Title' };
    const fixedDate = new Date();
    const expectedResult: KnowledgeDocument = {
        id: documentId,
        user_id: testActualUserId,
        title: 'Updated Title',
        content: 'Original content',
        document_type: 'TEST_TYPE',
        created_at: new Date().toISOString(), 
        updated_at: fixedDate.toISOString(),
    };

    beforeEach(() => {
        vi.spyOn(global, 'Date').mockImplementation(() => fixedDate);
    });

    afterEach(() => {
        vi.spyOn(global, 'Date').mockRestore();
    });

    it('should update a knowledge document successfully', async () => {
      mockQueryBuilder.single.mockResolvedValueOnce({ data: expectedResult , error: null });
      const result = await updateKnowledgeDocument(documentId, updates, testActualUserId, mockEnv);
      expect(result).toEqual(expectedResult);
      expect(mockQueryBuilder.update).toHaveBeenCalledWith(expect.objectContaining({ ...updates, updated_at: fixedDate.toISOString() }));
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('id', documentId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', testActualUserId);
    });

    it('should throw SupabaseError on error during update', async () => {
      const dbError = { message: 'Update failed', code: 'DB403', details: '', hint: '' };
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      await expect(updateKnowledgeDocument(documentId, updates, testActualUserId, mockEnv)).rejects.toThrow(SupabaseError);
      await expect(updateKnowledgeDocument(documentId, updates, testActualUserId, mockEnv)).rejects.toThrow(dbError.message);
    });

    it('should throw SupabaseError if documentId or actualUserId is not provided', async () => {
        await expect(updateKnowledgeDocument(0, updates, testActualUserId, mockEnv)).rejects.toThrow('documentId and actualUserId are required');
        await expect(updateKnowledgeDocument(documentId, updates, 0, mockEnv)).rejects.toThrow('documentId and actualUserId are required');
    });
  });

  describe('deleteKnowledgeDocument', () => {
    const documentId = 1;

    it('should delete a knowledge document successfully', async () => {
      deleteSecondEqMock.mockResolvedValueOnce({ error: null, data: null });
      await expect(deleteKnowledgeDocument(documentId, testActualUserId, mockEnv)).resolves.toBeUndefined();
      expect(mockQueryBuilder.delete).toHaveBeenCalled();
      expect(deleteInitialEqMock).toHaveBeenCalledWith('id', documentId);
      expect(deleteSecondEqMock).toHaveBeenCalledWith('user_id', testActualUserId);
    });

    it('should throw SupabaseError on error during deletion', async () => {
      const dbError = { message: 'Delete failed', code: 'DB404', details: '', hint: '' };
      deleteSecondEqMock.mockResolvedValueOnce({ error: dbError });
      deleteSecondEqMock.mockResolvedValueOnce({ error: dbError });
      await expect(deleteKnowledgeDocument(documentId, testActualUserId, mockEnv)).rejects.toThrow(SupabaseError);
      await expect(deleteKnowledgeDocument(documentId, testActualUserId, mockEnv)).rejects.toThrow(dbError.message);
    });

    it('should throw SupabaseError if documentId or actualUserId is not provided for delete', async () => {
        await expect(deleteKnowledgeDocument(0, testActualUserId, mockEnv)).rejects.toThrow('documentId and actualUserId are required');
        await expect(deleteKnowledgeDocument(documentId, 0, mockEnv)).rejects.toThrow('documentId and actualUserId are required');
    });
  });
}); 