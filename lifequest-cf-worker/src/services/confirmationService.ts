import { CallbackQuery } from '@grammyjs/types';
import { Env } from '../types/index'; // Corrected Env import
import { getSupabaseClient } from '../supabaseClient'; // Import the Supabase client utility
import { saveMemoryInsight, SaveMemoryInsightPayload, Insight, extractInsights } from './memoryService'; // Added Insight import, added extractInsights
import { awardXp, AwardXpPayload } from './gameStateService'; // Import from gameStateService
import { completeQuest } from './questService'; // Import for quest completion
import { storeMemoryVector } from '../vectorizeClient'; // For ST26.6
import { MemoryRAGChunk } from '../types'; // For ST26.6
import { log, LogLevel, LogContext } from '../utils/logger'; // Import logger
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';
// import { createSupabaseClient } from '../supabaseClient'; // To be created

export interface PendingConfirmation {
  id: string; // UUID
  user_telegram_id: number;
  action_type: string;
  action_payload: any; // JSONB in Supabase
  original_message_id: number;
  created_at: string; // TIMESTAMPTZ
  expires_at: string; // TIMESTAMPTZ
}

export interface ConfirmationResult {
  success: boolean;
  message: string; // User-facing message to update the original Telegram message with
  error?: string; // Optional error details for logging
}

/**
 * Defines the structure for the payload when completing a quest,
 * used in the confirmation flow.
 */
export interface CompleteQuestPayload {
  questId: number;
  questTitle: string;
  xpReward?: number;
  coinReward?: number;
}

// Placeholder for action types - these would typically be defined in a shared types file or enum
const ActionTypes = {
  SAVE_MEMORY_INSIGHT: 'SAVE_MEMORY_INSIGHT',
  AWARD_XP: 'AWARD_XP',
  SAVE_EXTRACTED_INSIGHTS: 'SAVE_EXTRACTED_INSIGHTS',
  COMPLETE_QUEST: 'COMPLETE_QUEST',
  SAVE_SUMMARY_TO_MEMORY: 'SAVE_SUMMARY_TO_MEMORY',
  // Add other action types as needed
};

/**
 * Represents the result of an action handler called by the confirmation service.
 * Contains success status, an optional user-facing message, an optional error message,
 * and optional data returned by the handler.
 */
interface ActionResult {
    success: boolean;
    message?: string; // Message to show user if action was successful
    error?: string; // Error detail if action failed
    data?: any; // Added to hold potential data from action handlers
}

// Placeholder action handlers (these would live in their respective services)
// async function handleSaveMemoryInsight(payload: any, env: Env, userId: number): Promise<ActionResult> { ... }
// async function handleAwardXp(payload: any, env: Env, userId: number): Promise<ActionResult> { ... }

const SERVICE_NAME = 'ConfirmationService';

/**
 * Processes a confirmation callback.
 *
 * @param confirmationUUID The UUID of the confirmation.
 * @param choice The user's choice ('y' or 'n').
 * @param env The Cloudflare Worker environment.
 * @param callbackQuery The original CallbackQuery object from Telegram.
 * @returns ConfirmationResult containing success status and a message for the user.
 * @throws {SupabaseError} or {AppError} for unrecoverable issues.
 */
export async function processConfirmation(
  confirmationUUID: string,
  choice: 'y' | 'n',
  env: Env,
  callbackQuery: CallbackQuery
): Promise<ConfirmationResult> {
  const functionName = 'processConfirmation';
  const telegramUserId = callbackQuery.from.id;
  let logContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId, payload: { confirmationUUID, choice } }; 
  log(LogLevel.INFO, 'Processing confirmation callback', logContext);

  const supabase = getSupabaseClient(env);
  let pendingConfirmation: PendingConfirmation;

  try {
    const fetchResponse = await supabase
        .from('pending_confirmations')
        .select('*')
        .eq('id', confirmationUUID)
        .single<PendingConfirmation>();
    
    pendingConfirmation = processSupabaseResponse(fetchResponse, { ...logContext, operation: 'fetchConfirmation' });
    if (!pendingConfirmation) { // Should be caught by processSupabaseResponse if .single() ensures data or error
        log(LogLevel.ERROR, 'Confirmation not found after successful DB call (unexpected)', { ...logContext });
        throw new SupabaseError('Confirmation not found, though DB call succeeded.', { ...logContext });
    }
  } catch (error: any) {
    const errorMsg = 'Error fetching pending confirmation or not found';
    if (!(error instanceof SupabaseError)) {
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: error.message, stack: error.stack });
    }
    // For the caller, this means the confirmation cannot be processed.
    return { 
      success: false, 
      message: 'Error: Could not find this confirmation. It might have expired or been processed already.', 
      error: (error instanceof SupabaseError) ? error.message : errorMsg 
    };
  }

  logContext = { ...logContext, payload: { ...logContext.payload, actionType: pendingConfirmation.action_type, originalMessageId: pendingConfirmation.original_message_id } };

  if (pendingConfirmation.user_telegram_id !== telegramUserId) {
    log(LogLevel.WARN, 'User ID mismatch for confirmation', { ...logContext, payload: { ...logContext.payload, expectedUserId: pendingConfirmation.user_telegram_id } });
    try { await supabase.from('pending_confirmations').delete().eq('id', confirmationUUID); } catch (delErr) { log(LogLevel.ERROR, 'Failed to delete mismatched confirmation', { ...logContext, error: (delErr as Error).message });}
    return { success: false, message: 'Error: This confirmation is not for you.', error: 'User ID mismatch' };
  }

  const now = new Date();
  const expiresAt = new Date(pendingConfirmation.expires_at);
  if (now > expiresAt) {
    log(LogLevel.WARN, 'Confirmation has expired', { ...logContext, payload: { ...logContext.payload, expiresAt: pendingConfirmation.expires_at } });
    try { await supabase.from('pending_confirmations').delete().eq('id', confirmationUUID); } catch (delErr) { log(LogLevel.ERROR, 'Failed to delete expired confirmation', { ...logContext, error: (delErr as Error).message });}
    return { success: false, message: 'Error: This confirmation has expired.', error: 'Confirmation expired' };
  }

  if (choice === 'n') {
    log(LogLevel.INFO, 'Confirmation declined by user', logContext);
    try {
        const deleteResponse = await supabase.from('pending_confirmations').delete().eq('id', confirmationUUID);
        if (deleteResponse.error) handleSupabaseError(deleteResponse.error, { ...logContext, operation: 'deleteDeclinedConfirmation' });
    } catch (delErr: any) { 
        log(LogLevel.ERROR, 'Error deleting declined confirmation from DB', { ...logContext, error: delErr.message });
        // Non-critical if delete fails, but log it. The primary path (declined) is handled.
    }
    return { success: true, message: 'Action cancelled as per your choice.' };
  }

  log(LogLevel.INFO, 'Confirmation accepted by user, processing action', logContext);
  let actionResult: ActionResult = { success: false, error: 'Action handler not implemented or failed.' };
  let userFriendlyMessage = 'Action processed.';

  try {
    // Fetch the user's database ID (users.id) using the telegramUserId from the callback.
    // This is crucial because action handlers expect the database users.id, not the telegram_id.
    let actualUserIdForActionHandler: number | undefined;
    try {
      const userForActionResponse = await supabase
        .from('users')
        .select('id')
        .eq('telegram_id', telegramUserId) // Use telegramUserId from callback
        .single();
      const userRecord = processSupabaseResponse(userForActionResponse, { ...logContext, operation: 'fetchUserForActionHandler'});
      if (userRecord && userRecord.id) {
        actualUserIdForActionHandler = userRecord.id;
        log(LogLevel.INFO, `Found user.id ${actualUserIdForActionHandler} for telegram_id ${telegramUserId} for action handler.`, logContext);
      } else {
        throw new Error(`User record or id not found for telegram_id ${telegramUserId} before calling action handler.`);
      }
    } catch (userFetchError: any) {
      log(LogLevel.ERROR, 'Failed to fetch user.id for action handler', { ...logContext, error: userFetchError.message });
      actionResult = { success: false, error: 'Could not identify user for the action.' };
      userFriendlyMessage = 'Error: Could not verify your user identity to perform this action.';
      // Skip to deletion of pending_confirmation as action cannot be performed
      // Set a flag or directly jump to the deletion logic if possible, or handle in the main flow
      // For now, we will let it fall through, and it might be caught by actionResult.success check later
      // but ideally, we'd have a cleaner way to signal this specific failure before switch.
      // However, the switch block needs to be executed to set actionResult.success properly for the final deletion check.
      // So, we ensure actionResult indicates failure and has an error message.
    }

    if (!actualUserIdForActionHandler && choice === 'y') {
      // If user ID could not be fetched, and user chose 'yes', we can't proceed with most actions.
      // Ensure actionResult reflects this failure before switch if not already set.
      if (!actionResult.error) { // If not already set by the catch block above
        actionResult = { success: false, error: 'User ID for action handler is missing.' };
        userFriendlyMessage = 'Error: User identity missing, cannot perform action.';
      }
      log(LogLevel.ERROR, 'actualUserIdForActionHandler is missing, cannot proceed with action type execution.', logContext);
    } else {
      // Only proceed if actualUserIdForActionHandler is available OR if choice is 'n' (which is handled earlier)
      // The check for `choice === 'y'` within specific handlers like SAVE_SUMMARY_TO_MEMORY remains relevant if they need to re-evaluate.

      switch (pendingConfirmation.action_type) {
        case ActionTypes.SAVE_MEMORY_INSIGHT:
          // Ensure saveMemoryInsight expects the database users.id
          actionResult = await saveMemoryInsight(pendingConfirmation.action_payload as SaveMemoryInsightPayload, env, actualUserIdForActionHandler!); // Pass actualUserIdForActionHandler
          userFriendlyMessage = actionResult.success ? actionResult.message || 'Insight saved!' : (actionResult.error || 'Failed to save insight.');
          break;
        case ActionTypes.AWARD_XP:
          // Ensure awardXp expects the database users.id
          actionResult = await awardXp(pendingConfirmation.action_payload as AwardXpPayload, env, actualUserIdForActionHandler!); // Pass actualUserIdForActionHandler
          userFriendlyMessage = actionResult.success ? actionResult.message || 'XP awarded!' : (actionResult.error || 'Failed to award XP.');
          break;
        case ActionTypes.SAVE_EXTRACTED_INSIGHTS:
          log(LogLevel.DEBUG, 'Handling SAVE_EXTRACTED_INSIGHTS action', logContext);
          const rawPayloadForExtractedInsights = pendingConfirmation.action_payload as { insights?: Insight[] };

          if (!rawPayloadForExtractedInsights || typeof rawPayloadForExtractedInsights !== 'object' || !Array.isArray(rawPayloadForExtractedInsights.insights)) {
            actionResult = { success: false, error: 'Payload for SAVE_EXTRACTED_INSIGHTS was not an object with an insights array or insights array is missing.' };
            userFriendlyMessage = 'Error: Could not process insights (invalid data structure).';
            log(LogLevel.ERROR, 'Invalid payload structure for SAVE_EXTRACTED_INSIGHTS', { ...logContext, payload: { ...logContext.payload, receivedActionPayload: pendingConfirmation.action_payload }, error: actionResult.error });
            break;
          }
          const insightsToSave: Insight[] = rawPayloadForExtractedInsights.insights;

          if (!Array.isArray(insightsToSave)) {
            actionResult = { success: false, error: 'Insights data within payload is not an array (after accessing .insights).' };
            userFriendlyMessage = 'Error: Could not process insights (invalid data structure).';
            log(LogLevel.ERROR, 'Insights data within payload is not an array for SAVE_EXTRACTED_INSIGHTS', { ...logContext, payload: { ...logContext.payload, receivedActionPayload: pendingConfirmation.action_payload }, error: actionResult.error });
            break;
          }
          let allSavedSuccessfully = true;
          let individualMessages: string[] = [];
          for (const insight of insightsToSave) {
            const singleSavePayload: SaveMemoryInsightPayload = {
              text: insight.text,
              tags: insight.tags,
              importance_score: insight.importance_score, 
              title: insight.text.substring(0, 70) + (insight.text.length > 70 ? '...' : ''),
            };
            const singleSaveResult = await saveMemoryInsight(singleSavePayload, env, actualUserIdForActionHandler!); // Pass actualUserIdForActionHandler
            if (!singleSaveResult.success) {
              allSavedSuccessfully = false;
              individualMessages.push(`Failed to save insight: \"${insight.text.substring(0,30)}...\"
`);
              log(LogLevel.ERROR,`Failed to save one of the extracted insights: ${insight.text}`, { ...logContext, error: singleSaveResult.error });
            } else {
              individualMessages.push(singleSaveResult.message || `Insight \"${insight.text.substring(0,30)}...\" saved.
`);
            }
          }
          actionResult = { success: allSavedSuccessfully };
          if (allSavedSuccessfully) {
              userFriendlyMessage = insightsToSave.length > 1 ? 'All insights saved successfully!' : 'Insight saved successfully!';
              if (insightsToSave.length > 1 && individualMessages.length > 0) {
                   userFriendlyMessage += '\n\nDetails:\n' + individualMessages.join('\n');
              } else if (insightsToSave.length === 1 && individualMessages[0]) {
                  userFriendlyMessage = individualMessages[0];
              }
          } else {
              userFriendlyMessage = 'Some insights could not be saved.\n\nDetails:\n' + individualMessages.join('\n');
          }
          break;
        case ActionTypes.COMPLETE_QUEST:
          // handleCompleteQuest already takes telegramUserId and fetches users.id internally.
          // No need to pass actualUserIdForActionHandler directly if its internal logic is correct.
          actionResult = await handleCompleteQuest(pendingConfirmation.action_payload as CompleteQuestPayload, env, telegramUserId);
          userFriendlyMessage = actionResult.success ? actionResult.message || 'Quest completed!' : (actionResult.error || 'Failed to complete quest.');
          break;
        case ActionTypes.SAVE_SUMMARY_TO_MEMORY:
          const summaryPayload = pendingConfirmation.action_payload as { summaryText: string, contextDescription: string }; // Define or import SaveSummaryPayload if available
          if (choice === 'y') {
            try {
              log(LogLevel.INFO, 'User confirmed to save summary. Extracting insights...', logContext);
              // extractInsights needs the database users.id if it does any user-specific operations or logging that requires it.
              const insights: Insight[] = await extractInsights(summaryPayload.contextDescription, summaryPayload.summaryText, env, actualUserIdForActionHandler!); // Pass actualUserIdForActionHandler
              if (insights && insights.length > 0) {
                log(LogLevel.INFO, `Extracted ${insights.length} insights from summary.`, { ...logContext, payload: { ...logContext.payload, insightCount: insights.length, insightsPreview: insights.slice(0,2).map((i: Insight) => i.text.substring(0,50)) }});
                actionResult = { success: true, message: `Summary insights extracted (${insights.length}). Ready for RAG indexing.`, data: insights }; 
              } else {
                log(LogLevel.INFO, 'No insights extracted from summary.', logContext);
                actionResult = { success: true, message: 'Summary processed, but no specific insights were extracted from it.' };
              }
              userFriendlyMessage = actionResult.message || 'Summary processing complete.';

              // ST26.6: If insights were extracted successfully, store them in RAG
              if (actionResult.success && actionResult.data && Array.isArray(actionResult.data)) {
                const insightsToStore: Insight[] = actionResult.data;
                if (insightsToStore.length > 0) {
                  log(LogLevel.INFO, `Attempting to store ${insightsToStore.length} insights for RAG.`, logContext);
                  let allStoredSuccessfully = true;
                  let storeErrorMessages: string[] = [];

                  // Fetch actual user ID first
                  let actualUserId: number | undefined;
                  try {
                    const userResponse = await supabase.from('users').select('id').eq('telegram_id', telegramUserId).single();
                    const user = processSupabaseResponse(userResponse, { ...logContext, operation: 'fetchUserForVectorStore' });
                    actualUserId = user?.id;
                    if (!actualUserId) {
                      throw new Error('User not found for vector storage.');
                    }
                  } catch (userError: any) {
                    log(LogLevel.ERROR, 'Failed to fetch actual user ID for vector storage', { ...logContext, error: userError.message });
                    allStoredSuccessfully = false;
                    storeErrorMessages.push('Could not identify user for saving insights.');
                  }

                  if (actualUserId) {
                    for (const insight of insightsToStore) {
                      try {
                        const memoryChunk: MemoryRAGChunk = {
                          id: crypto.randomUUID(),
                          user_id: actualUserId, // Use actual user ID
                          text: insight.text,
                          source_type: 'summary_insight_auto',
                          source_id: confirmationUUID, // Link to the confirmation batch
                          importance_score: insight.importance_score,
                          timestamp: new Date().toISOString(),
                          tags: insight.tags,
                          metadata: {
                            originalSummaryContext: summaryPayload.contextDescription,
                            // Add original scope details if needed from summaryPayload.scopeType, scopeValue, scopeQuestId
                          }
                        };
                        await storeMemoryVector(env, memoryChunk, telegramUserId);
                        log(LogLevel.DEBUG, `Stored insight vector: ${memoryChunk.id}`, { ...logContext, payload: { ...logContext.payload, chunkId: memoryChunk.id } });
                      } catch (vectorError: any) {
                        log(LogLevel.ERROR, `Failed to store insight vector for text: ${insight.text.substring(0, 50)}...`, { ...logContext, error: vectorError.message });
                        allStoredSuccessfully = false;
                        storeErrorMessages.push(`Failed to store insight: "${insight.text.substring(0,30)}...".`);
                      }
                    }
                  }

                  if (allStoredSuccessfully && storeErrorMessages.length === 0) {
                    userFriendlyMessage = actionResult.message + ' All insights stored for RAG!';
                  } else {
                    userFriendlyMessage = actionResult.message + ` Some insights failed to store for RAG. Details: ${storeErrorMessages.join('; ')}`;
                    actionResult.success = false; // Mark overall action as failed if any vector store fails
                    actionResult.error = (actionResult.error ? actionResult.error + "; " : "") + storeErrorMessages.join('; ');
                  }
                }
              }
            } catch (e: any) {
              log(LogLevel.ERROR, 'Error extracting insights from summary', { ...logContext, error: e.message, stack: e.stack });
              actionResult = { success: false, error: 'Failed to extract insights from summary.' };
              userFriendlyMessage = 'Error: Could not process the summary for insights.';
            }
          } else { // choice === 'n' was already handled by prior logic, this case is for explicit 'n' if logic changes
            actionResult = { success: true, message: 'Okay, summary will not be saved to memory.' };
            userFriendlyMessage = actionResult.message ?? 'Summary not saved as per your choice.';
          }
          break;
        default:
          log(LogLevel.ERROR, 'Unknown action_type', { ...logContext, payload: { ...logContext.payload, actionType: pendingConfirmation.action_type }});
          actionResult = { success: false, error: `Unknown action_type: ${pendingConfirmation.action_type}` };
          userFriendlyMessage = 'Error: Unknown action for this confirmation.';
      }
    }
  } catch (e: any) {
    // This catch is for errors during the action handlers (saveMemoryInsight, awardXp)
    log(LogLevel.ERROR, `Error executing action ${pendingConfirmation.action_type}`, { ...logContext, error: e.message, stack: e.stack });
    actionResult = { success: false, error: e.message };
    userFriendlyMessage = 'An error occurred while processing your confirmed action.';
  }

  try {
    const deleteConfirmedResponse = await supabase.from('pending_confirmations').delete().eq('id', confirmationUUID);
    if (deleteConfirmedResponse.error) {
        // This is a critical error if the action succeeded but we can't delete the confirmation
        const dbError = deleteConfirmedResponse.error; // Assign to a non-null typed const
        handleSupabaseError(dbError, { ...logContext, operation: 'deleteProcessedConfirmation' });
        log(LogLevel.ERROR, `CRITICAL: Failed to delete processed confirmation ${confirmationUUID}`, logContext); 
        if (actionResult.success) {
            return { 
                success: true, 
                message: `${userFriendlyMessage} (Note: Finalization issue, please report if action seems to repeat.)`,
                error: `Action succeeded but failed to delete confirmation record: ${dbError.message}`
            };
        }
    }
  } catch (delErr: any) {
      log(LogLevel.ERROR, `CRITICAL: Exception deleting processed confirmation ${confirmationUUID}`, { ...logContext, error: delErr.message, stack: delErr.stack });
      if (actionResult.success) {
        return { 
            success: true, 
            message: `${userFriendlyMessage} (Note: Finalization issue, please report if action seems to repeat.)`,
            error: `Action succeeded but failed to delete confirmation record (exception): ${delErr.message}`
        };
     }
     // If action also failed, and delete failed, the actionResult error is more primary.
  }

  log(LogLevel.INFO, `Successfully processed confirmation ${confirmationUUID}. Action success: ${actionResult.success}`, logContext);
  return { 
    success: actionResult.success,
    message: userFriendlyMessage,
    error: actionResult.success ? undefined : actionResult.error 
  };
}

/**
 * Handles the logic for completing a quest when a user confirms the action.
 * This includes marking the quest as complete, awarding XP and coins (if defined in the payload),
 * and constructing a user-facing success message that includes streak and milestone bonus information.
 *
 * @param payload The {@link CompleteQuestPayload} containing quest ID, title, and potential rewards.
 * @param env The Cloudflare Worker environment variables and bindings.
 * @param telegramUserId The Telegram ID of the user completing the quest.
 * @returns A Promise resolving to an {@link ActionResult} indicating success or failure, and a user-facing message.
 */
async function handleCompleteQuest(payload: CompleteQuestPayload, env: Env, telegramUserId: number): Promise<ActionResult> {
    const functionName = 'handleCompleteQuest';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId, payload };
    log(LogLevel.INFO, 'Handling quest completion', logContext);
    // Add log to see the received telegramUserId for this specific handler
    log(LogLevel.DEBUG, `handleCompleteQuest received telegramUserId: ${telegramUserId}`, logContext);

    try {
        // Get user ID from telegram ID (similar to other handlers)
        const supabase = getSupabaseClient(env);
        const userResponse = await supabase
            .from('users')
            .select('id')
            .eq('telegram_id', telegramUserId)
            .single();
        
        const user = processSupabaseResponse(userResponse, { ...logContext, operation: 'fetchUser' });
        if (!user || !user.id) { // Added !user.id check
            log(LogLevel.ERROR, 'User not found or user.id is missing for quest completion', logContext);
            return { success: false, error: 'User not found or user ID missing' };
        }
        // Log the fetched user.id
        log(LogLevel.DEBUG, `Fetched user.id ${user.id} for telegram_id ${telegramUserId} in handleCompleteQuest`, logContext);

        // Complete the quest (now returns quest and optional streak bonus info)
        const completionResult = await completeQuest(payload.questId, env, user.id);
        const completedQuest = completionResult.quest;
        
        // Award XP and coins if specified
        let rewardMessage = '';
        if (payload.xpReward && payload.xpReward > 0) {
            const xpPayload: AwardXpPayload = { amount: payload.xpReward };
            // awardXp needs the database users.id, which is user.id here.
            const xpResult = await awardXp(xpPayload, env, user.id); // Pass user.id (database PK)
            if (!xpResult.success) {
                log(LogLevel.WARN, 'Failed to award XP after quest completion', { ...logContext, error: xpResult.error });
                rewardMessage += ` (XP award failed: ${xpResult.error})`;
            } else {
                rewardMessage += ` +${payload.xpReward} XP!`;
            }
        }

        if (payload.coinReward && payload.coinReward > 0) {
            // Note: We don't have addCoins action yet, but we can use updateGameState
            // For now, just include it in the message
            rewardMessage += ` +${payload.coinReward} coins!`;
        }

        // Add streak information if this was a routine quest
        let streakMessage = '';
        if (completionResult.streakIncremented && completedQuest.streak) {
            streakMessage = `\n\n🔥 Streak: ${completedQuest.streak} day${completedQuest.streak > 1 ? 's' : ''}!`;
        }

        // Add milestone bonus information if achieved
        let milestoneMessage = '';
        if (completionResult.streakBonus) {
            const bonus = completionResult.streakBonus;
            milestoneMessage = `\n\n⭐ ${bonus.description} Bonus: +${bonus.xp_bonus} XP, +${bonus.coin_bonus} coins!`;
            
            // Award milestone bonus XP
            if (bonus.xp_bonus > 0) {
                const bonusXpPayload: AwardXpPayload = { amount: bonus.xp_bonus };
                // awardXp needs the database users.id
                const bonusXpResult = await awardXp(bonusXpPayload, env, user.id); // Pass user.id (database PK)
                if (!bonusXpResult.success) {
                    log(LogLevel.WARN, 'Failed to award milestone bonus XP', { ...logContext, error: bonusXpResult.error });
                    milestoneMessage += ` (Bonus XP award failed)`;
                }
            }
        }

        const successMessage = `Quest "${completedQuest.title}" completed!${rewardMessage}${streakMessage}${milestoneMessage}

🎉 Congratulations on your achievement! 

Consider adding this accomplishment to your Hall of Fame with:
/hall_add Completed quest: ${completedQuest.title}`;
        log(LogLevel.INFO, 'Quest completed successfully', { 
            ...logContext, 
            payload: { 
                ...logContext.payload, 
                questId: completedQuest.id,
                hadStreakBonus: !!completionResult.streakBonus,
                newStreak: completedQuest.streak
            } 
        });
        
        return { success: true, message: successMessage };
    } catch (error: any) {
        log(LogLevel.ERROR, 'Error completing quest', { ...logContext, error: error.message, stack: error.stack });
        return { success: false, error: error.message || 'Failed to complete quest' };
    }
} 