import { Env } from '../types';
import { getSupabaseClient } from '../supabaseClient';
import { Database, Json } from '../types/supabase';
import { log, LogLevel, LogContext } from '../utils/logger'; // Import logger
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';
import { 
    QuestSchedule, 
    RoutineMetadata, 
    StreakMilestone, 
    DEFAULT_STREAK_MILESTONES, 
    StreakCheckResult 
} from '../types';

// Re-export for testing purposes
export { DEFAULT_STREAK_MILESTONES };

// Define Quest type based on your Supabase schema (quests table)
export type Quest = Database['public']['Tables']['quests']['Row'];
export type QuestInsert = Database['public']['Tables']['quests']['Insert'];
export type QuestUpdate = Database['public']['Tables']['quests']['Update'];

// TODO: Define more specific types for filters, quest data, etc.

/**
 * Interface for the data required to create a new quest.
 * Ensures that essential fields like user_id, title, and type are provided.
 */
export interface QuestCreationData {
    user_id: number; // This should be the actual user ID from the users table, not telegram_id
    title: string;
    description?: string | null;
    type: 'main' | 'side' | 'routine';
    xp_reward?: number | null;
    coin_reward?: number | null;
    routine_schedule?: string | null; // e.g., cron expression or simple daily/weekly
    metadata?: Record<string, any> | null;
    parent_quest_id?: number | null;
}

/**
 * Defines the available filters for querying quests.
 * Allows filtering by status, type, and limiting the number of results.
 */
export interface QuestFilters {
    status?: string;
    type?: string;
    limit?: number; // Added limit filter
    // Add other filterable fields if needed, e.g., priority, tags from metadata
}

const SERVICE_NAME = 'QuestService';

/**
 * Routine Quest Helper Functions
 */

/**
 * Checks if a quest completion is on schedule based on the quest's schedule configuration.
 * @param quest - The quest object with schedule information
 * @param completionDate - The date when the quest was completed
 * @returns StreakCheckResult indicating if completion is on schedule
 */
export function checkQuestSchedule(quest: Quest, completionDate: Date = new Date()): StreakCheckResult {
    if (quest.type !== 'routine' || !quest.schedule) {
        return {
            isOnSchedule: false,
            streakShouldReset: false,
            daysSinceLastCompletion: 0
        };
    }

    const schedule = quest.schedule as unknown as QuestSchedule;
    const lastCompleted = quest.completed_at ? new Date(quest.completed_at) : null;
    
    if (!lastCompleted) {
        // First time completion - always on schedule
        return {
            isOnSchedule: true,
            streakShouldReset: false,
            daysSinceLastCompletion: 0
        };
    }

    const daysSinceLastCompletion = Math.floor(
        (completionDate.getTime() - lastCompleted.getTime()) / (1000 * 60 * 60 * 24)
    );

    switch (schedule.type) {
        case 'daily':
            return {
                isOnSchedule: daysSinceLastCompletion >= 1 && daysSinceLastCompletion <= 2,
                streakShouldReset: daysSinceLastCompletion > 2,
                daysSinceLastCompletion,
                nextScheduledDate: new Date(lastCompleted.getTime() + 24 * 60 * 60 * 1000)
            };
            
        case 'weekly':
            return {
                isOnSchedule: daysSinceLastCompletion >= 6 && daysSinceLastCompletion <= 8,
                streakShouldReset: daysSinceLastCompletion > 8,
                daysSinceLastCompletion,
                nextScheduledDate: new Date(lastCompleted.getTime() + 7 * 24 * 60 * 60 * 1000)
            };
            
        default:
            // For custom schedules, use daily logic as fallback
            return {
                isOnSchedule: daysSinceLastCompletion >= 1 && daysSinceLastCompletion <= 2,
                streakShouldReset: daysSinceLastCompletion > 2,
                daysSinceLastCompletion
            };
    }
}

/**
 * Calculates streak bonus rewards when a milestone is reached.
 * @param currentStreak - The current streak count
 * @param lastMilestone - The last milestone reached (to avoid duplicate bonuses)
 * @returns StreakMilestone object if a new milestone is reached, null otherwise
 */
export function calculateStreakBonus(currentStreak: number, lastMilestone: number = 0): StreakMilestone | null {
    const availableMilestones = DEFAULT_STREAK_MILESTONES.filter(
        milestone => milestone.days > lastMilestone && milestone.days <= currentStreak
    );
    
    // Return the highest milestone reached
    return availableMilestones.length > 0 
        ? availableMilestones[availableMilestones.length - 1] 
        : null;
}

/**
 * Updates a routine quest upon completion, handling streak logic and milestone bonuses.
 * It checks if the completion is on schedule, resets or increments the streak accordingly,
 * calculates any milestone bonuses, and then updates the quest record in the database.
 *
 * @param quest The routine quest object to be updated.
 * @param env The Cloudflare Worker environment, containing database clients and configurations.
 * @returns A Promise resolving to an object containing the updated quest, any streak bonus achieved, and whether the streak was incremented.
 * @throws {SupabaseError} if there's an issue with database operations.
 */
async function updateRoutineQuestCompletion(quest: Quest, env: Env): Promise<{ 
    updatedQuest: Quest, 
    streakBonus: StreakMilestone | null,
    streakIncremented: boolean 
}> {
    const functionName = 'updateRoutineQuestCompletion';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName, 
        payload: { questId: quest.id, questType: quest.type } 
    };

    const completionDate = new Date();
    const scheduleCheck = checkQuestSchedule(quest, completionDate);
    
    log(LogLevel.DEBUG, 'Checking routine quest schedule', { 
        ...logContext, 
        payload: { ...logContext.payload, scheduleCheck } 
    });

    let newStreak = quest.streak || 0;
    let streakIncremented = false;
    let streakBonus: StreakMilestone | null = null;

    const currentMetadata = (quest.metadata as RoutineMetadata) || {};

    if (scheduleCheck.streakShouldReset) {
        log(LogLevel.INFO, 'Resetting streak due to missed schedule', { 
            ...logContext, 
            payload: { ...logContext.payload, daysSinceLastCompletion: scheduleCheck.daysSinceLastCompletion } 
        });
        newStreak = 1; // Reset to 1 for current completion
        streakIncremented = true;
        currentMetadata.last_streak_milestone = 0; // Reset milestone tracking
    } else if (scheduleCheck.isOnSchedule) {
        log(LogLevel.INFO, 'Quest completed on schedule, incrementing streak', { 
            ...logContext, 
            payload: { ...logContext.payload, currentStreak: newStreak } 
        });
        newStreak += 1;
        streakIncremented = true;
        
        // Check for milestone bonus
        const lastMilestone = currentMetadata.last_streak_milestone || 0;
        streakBonus = calculateStreakBonus(newStreak, lastMilestone);
        
        if (streakBonus) {
            log(LogLevel.INFO, 'Streak milestone reached!', { 
                ...logContext, 
                payload: { ...logContext.payload, milestone: streakBonus } 
            });
            currentMetadata.last_streak_milestone = streakBonus.days;
        }
    } else {
        log(LogLevel.WARN, 'Quest completed but not on schedule - no streak increment', { 
            ...logContext, 
            payload: { ...logContext.payload, scheduleCheck } 
        });
    }

    // Update the quest with new streak and metadata
    const updates: QuestUpdate = {
        status: 'done',
        completed_at: completionDate.toISOString(),
        streak: newStreak,
        metadata: currentMetadata as Json
    };

    const supabase = getSupabaseClient(env);
    try {
        const response = await supabase
            .from('quests')
            .update(updates)
            .eq('id', quest.id)
            .select()
            .single();

        const updatedQuest = processSupabaseResponse(response, { 
            ...logContext, 
            operation: 'updateRoutineQuestCompletion.update' 
        });

        log(LogLevel.INFO, 'Routine quest completion updated successfully', { 
            ...logContext, 
            payload: { 
                ...logContext.payload, 
                newStreak, 
                streakIncremented,
                hasMilestoneBonus: !!streakBonus 
            } 
        });

        return { updatedQuest, streakBonus, streakIncremented };
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'updateRoutineQuestCompletion: Unexpected error during DB operation', { 
                ...logContext, 
                error: e.message, 
                stack: e.stack 
            });
            throw new SupabaseError(e.message || 'Unexpected database error in updateRoutineQuestCompletion', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Checks all active routine quests for a user and resets streaks for any that have missed their schedule.
 * This function should be called during key user interactions like /checkin to ensure streaks stay accurate.
 * @param actualUserId - The user's primary key ID from the users table
 * @param env - Environment object
 * @returns Array of quest IDs that had their streaks reset
 */
export async function resetMissedStreaks(actualUserId: number, env: Env): Promise<number[]> {
    const functionName = 'resetMissedStreaks';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName, 
        payload: { actualUserId } 
    };

    log(LogLevel.INFO, 'Checking for missed routine quest streaks', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to reset missed streaks';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }

    try {
        // Get all active routine quests for the user
        const routineQuests = await getQuests(actualUserId, { 
            status: 'pending', // Only check active quests
            type: 'routine' 
        }, env);

        const resetsNeeded: Quest[] = [];
        const now = new Date();

        // Check each routine quest for missed schedules
        for (const quest of routineQuests) {
            if (!quest.schedule || quest.streak === 0 || quest.streak === null) {
                continue; // Skip quests without schedule, or already at 0 streak
            }

            const scheduleCheck = checkQuestSchedule(quest, now);
            
            if (scheduleCheck.streakShouldReset) {
                log(LogLevel.INFO, 'Quest streak needs reset due to missed schedule', { 
                    ...logContext, 
                    payload: { 
                        ...logContext.payload, 
                        questId: quest.id, 
                        questTitle: quest.title,
                        daysSinceLastCompletion: scheduleCheck.daysSinceLastCompletion,
                        currentStreak: quest.streak
                    } 
                });
                resetsNeeded.push(quest);
            }
        }

        const resetQuestIds: number[] = [];
        
        // Reset streaks for quests that missed their schedule
        for (const quest of resetsNeeded) {
            try {
                const currentMetadata = (quest.metadata as RoutineMetadata) || {};
                currentMetadata.last_streak_milestone = 0; // Reset milestone tracking too

                const updates: QuestUpdate = {
                    streak: 0,
                    metadata: currentMetadata as Json
                };

                await updateQuest(quest.id, updates, env, actualUserId);
                resetQuestIds.push(quest.id);
                
                log(LogLevel.INFO, 'Reset streak for missed routine quest', { 
                    ...logContext, 
                    payload: { 
                        ...logContext.payload, 
                        questId: quest.id,
                        questTitle: quest.title,
                        previousStreak: quest.streak
                    } 
                });
            } catch (error: any) {
                log(LogLevel.ERROR, 'Failed to reset streak for quest', { 
                    ...logContext, 
                    payload: { 
                        ...logContext.payload, 
                        questId: quest.id,
                        questTitle: quest.title 
                    },
                    error: error.message 
                });
                // Continue processing other quests even if one fails
            }
        }

        log(LogLevel.INFO, `Completed streak reset check - reset ${resetQuestIds.length} quests`, { 
            ...logContext, 
            payload: { 
                ...logContext.payload, 
                totalRoutineQuests: routineQuests.length,
                resetsPerformed: resetQuestIds.length,
                resetQuestIds 
            } 
        });

        return resetQuestIds;
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'resetMissedStreaks: Unexpected error during operation', { 
                ...logContext, 
                error: e.message, 
                stack: e.stack 
            });
            throw new SupabaseError(e.message || 'Unexpected error in resetMissedStreaks', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Quest Service
 * Handles all operations related to quests.
 */

// Placeholder for future functions
export const exampleQuestFunction = async (env: Env) => {
  const supabase = getSupabaseClient(env);
  // Example usage:
  // const { data, error } = await supabase.from('quests').select('*').limit(1);
  // if (error) console.error('Error fetching quest:', error);
  // return data;
  return { message: "Quest service is active" };
};

/**
 * Creates a new quest for a user.
 * @param questData - The data for the new quest.
 * @param env - The Cloudflare environment object.
 * @returns The created quest object or null if an error occurred.
 */
export async function createQuest(actualUserId: number, questData: QuestInsert, env: Env): Promise<Quest> {
    const functionName = 'createQuest';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, questTitle: questData.title } }; 
    log(LogLevel.INFO, 'Attempting to create quest', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to create quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }
    if (!questData.title) {
        const errorMsg = 'Quest title is required';
        log(LogLevel.WARN, errorMsg, { ...logContext, error: 'Quest title missing' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'Quest title missing' });
    }

    const supabase = getSupabaseClient(env);
    const questToInsert: QuestInsert = {
        ...questData,
        user_id: actualUserId,
        status: questData.status || 'pending', 
    };

    try {
        log(LogLevel.DEBUG, 'Inserting new quest into DB', { ...logContext, payload: { ...logContext.payload, questToInsert }});
        const response = await supabase
            .from('quests')
            .insert(questToInsert)
            .select()
            .single();

        const createdQuest = processSupabaseResponse(response, { ...logContext, operation: 'createQuest.insert' });
        if (!createdQuest) { // Should be caught by processSupabaseResponse if .single() ensures data or error
            log(LogLevel.ERROR, 'Quest creation insert succeeded but returned no data', { ...logContext });
            throw new SupabaseError('Quest creation insert did not return data as expected', { ...logContext, operation: 'createQuest.insert' });
        }
        log(LogLevel.INFO, 'Quest created successfully', { ...logContext, payload: { ...logContext.payload, questId: createdQuest.id }});
        return createdQuest;
    } catch (e: any) {
        // Catch errors from processSupabaseResponse or other unexpected errors
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'createQuest: Unexpected error during DB operation', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected database error in createQuest', { originalError: e.message }, e);
        }
        throw e; // Re-throw SupabaseError
    }
}

/**
 * Retrieves quests for a user based on specified filters.
 * @param actualUserId - The user's primary key ID from the users table.
 * @param filters - Optional filters to apply (status, type, etc.).
 * @param env - The Cloudflare environment object.
 * @returns An array of quest objects.
 * @throws {SupabaseError} if the database operation fails or actualUserId is not provided.
 */
export async function getQuests(actualUserId: number, filters: QuestFilters | undefined, env: Env): Promise<Quest[]> {
    const functionName = 'getQuests';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, filters } };
    log(LogLevel.INFO, 'Attempting to get quests', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to get quests';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }

    const supabase = getSupabaseClient(env);
    let query = supabase.from('quests').select('*').eq('user_id', actualUserId);

    if (filters?.status) {
        query = query.eq('status', filters.status);
    }
    if (filters?.type) {
        query = query.eq('type', filters.type);
    }
    query = query.order('created_at', { ascending: false }); 

    if (filters?.limit) {
        query = query.limit(filters.limit);
    }

    try {
        log(LogLevel.DEBUG, 'Fetching quests from DB with filters', logContext);
        const response = await query;
        // For a select that can return multiple rows, data can be an empty array [] if nothing matches.
        // processSupabaseResponse handles the error if response.error exists.
        // If response.data is null (and no error), it logs a warning.
        // An empty array is a valid result for getQuests.
        const quests = processSupabaseResponse(response, { ...logContext, operation: 'getQuests.select' });
        
        log(LogLevel.INFO, `Fetched ${quests?.length || 0} quests successfully`, { ...logContext, payload: { ...logContext.payload, count: quests?.length }});
        return quests || []; // Ensure it returns an array, even if processSupabaseResponse somehow yields null for a list op
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'getQuests: Unexpected error during DB operation', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected database error in getQuests', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Updates an existing quest.
 * @param questId - The ID of the quest to update.
 * @param updates - An object containing the fields to update.
 * @param env - The Cloudflare environment object.
 * @param actualUserId - The ID of the user performing the update for authorization.
 * @returns The updated quest object.
 * @throws {SupabaseError} if the database operation fails, actualUserId is not provided, or quest not found/authorized.
 */
export async function updateQuest(questId: number, updates: QuestUpdate, env: Env, actualUserId: number): Promise<Quest> {
    const functionName = 'updateQuest';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, questId, updates } };
    log(LogLevel.INFO, 'Attempting to update quest', logContext);
    
    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to update quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }
    if (!questId) {
        const errorMsg = 'questId is required to update quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'questId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'questId not provided' });
    }

    const supabase = getSupabaseClient(env);

    try {
        log(LogLevel.DEBUG, 'Updating quest in DB', { ...logContext, payload: { ...logContext.payload, updates }});
        const response = await supabase
            .from('quests')
            .update(updates)
            .eq('id', questId)
            .eq('user_id', actualUserId) // Authorization check
            .select()
            .single();

        const updatedQuest = processSupabaseResponse(response, { ...logContext, operation: 'updateQuest.update' });
        
        // .single() should throw if no rows (or more than 1) are returned after the update.
        // processSupabaseResponse will catch PostgrestError P0002 (no_data_found from .single()) or P0003 (too_many_rows)
        // and rethrow as SupabaseError. So, if updatedQuest is null here without an error from processSupabaseResponse,
        // it's an unexpected state, but normally processSupabaseResponse handles it.
        // The `error: 'Quest not found or not authorized to update.'` logic is implicitly handled by .single()
        // and processSupabaseResponse throwing an error if the quest doesn't match the criteria (id and user_id).
        if (!updatedQuest) { // This check might be redundant if .single() and processSupabaseResponse are robust.
            log(LogLevel.WARN, 'Quest not found or user mismatch during update, or .single() returned null unexpectedly', { ...logContext });
            throw new SupabaseError('Quest not found, not authorized to update, or update operation yielded no data.', { ...logContext, operation: 'updateQuest.update' });
        }

        log(LogLevel.INFO, 'Quest updated successfully', { ...logContext, payload: { ...logContext.payload, questId: updatedQuest.id }});
        return updatedQuest;
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'updateQuest: Unexpected error during DB operation', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected database error in updateQuest', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Marks a quest as completed.
 * For routine quests, handles streak logic and milestone bonuses.
 * For regular quests, simply updates status and completed_at timestamp.
 * @param questId - The ID of the quest to complete.
 * @param actualUserId - The user's primary key ID.
 * @param env - The Cloudflare environment object.
 * @returns Object containing the updated quest and optional streak bonus information.
 * @throws {SupabaseError} if the update fails.
 */
export async function completeQuest(questId: number, env: Env, actualUserId: number): Promise<{
    quest: Quest,
    streakBonus?: StreakMilestone | null,
    streakIncremented?: boolean
}> {
    const functionName = 'completeQuest';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, questId } };
    log(LogLevel.INFO, 'Attempting to complete quest', logContext);

    // First, get the current quest to check if it's a routine quest
    const supabase = getSupabaseClient(env);
    try {
        const response = await supabase
            .from('quests')
            .select('*')
            .eq('id', questId)
            .eq('user_id', actualUserId)
            .single();

        const quest = processSupabaseResponse(response, { 
            ...logContext, 
            operation: 'completeQuest.getQuest' 
        });

        if (!quest) {
            throw new SupabaseError('Quest not found or not authorized to complete', { 
                ...logContext, 
                operation: 'completeQuest.getQuest' 
            });
        }

        // Check if quest is already completed
        if (quest.status === 'done') {
            log(LogLevel.WARN, 'Quest is already completed', { 
                ...logContext, 
                payload: { ...logContext.payload, questStatus: quest.status } 
            });
            return { quest };
        }

        // Handle routine quests with streak logic
        if (quest.type === 'routine') {
            log(LogLevel.INFO, 'Processing routine quest completion with streak logic', logContext);
            const result = await updateRoutineQuestCompletion(quest, env);
            return {
                quest: result.updatedQuest,
                streakBonus: result.streakBonus,
                streakIncremented: result.streakIncremented
            };
        } else {
            // Handle regular quests
            log(LogLevel.INFO, 'Processing regular quest completion', logContext);
            const updates: QuestUpdate = {
                status: 'done',
                completed_at: new Date().toISOString(),
            };
            const updatedQuest = await updateQuest(questId, updates, env, actualUserId);
            return { quest: updatedQuest };
        }
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'completeQuest: Unexpected error during operation', { 
                ...logContext, 
                error: e.message, 
                stack: e.stack 
            });
            throw new SupabaseError(e.message || 'Unexpected error in completeQuest', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Deletes a quest.
 * Note: This is a hard delete. Consider an 'archive' status for soft deletes.
 * @param questId - The ID of the quest to delete.
 * @param actualUserId - The user's primary key ID (for verification).
 * @param env - The Cloudflare environment object.
 * @throws {SupabaseError} if the deletion fails or actualUserId is not provided.
 */
export async function deleteQuest(questId: number, env: Env, actualUserId: number): Promise<void> {
    const functionName = 'deleteQuest';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, questId } };
    log(LogLevel.INFO, 'Attempting to delete quest', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to delete quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }
    if (!questId) {
        const errorMsg = 'questId is required to delete quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'questId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'questId not provided' });
    }

    const supabase = getSupabaseClient(env);
    
    try {
        log(LogLevel.DEBUG, 'Deleting quest from DB', logContext);
        const response = await supabase
            .from('quests')
            .delete()
            .eq('id', questId)
            .eq('user_id', actualUserId); 

        // For delete, PostgREST usually returns an error or success with potentially no data in `response.data`.
        // processSupabaseResponse will throw if response.error exists.
        // If no error, the operation is considered successful for delete, even if data is null.
        processSupabaseResponse(response, { ...logContext, operation: 'deleteQuest.delete' });

        log(LogLevel.INFO, 'Quest deleted successfully (or did not exist for user to delete)', logContext);
        // No specific data to return for delete, void indicates success.
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'deleteQuest: Unexpected error during DB operation', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected database error in deleteQuest', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Finds a quest by ID or by searching title/description.
 * @param actualUserId - The user's primary key ID from the users table.
 * @param searchTerm - Either a quest ID (numeric) or text to search in title/description.
 * @param env - The Cloudflare environment object.
 * @returns The matching quest or null if not found.
 * @throws {SupabaseError} if the database operation fails or actualUserId is not provided.
 */
export async function findQuestByIdOrDescription(actualUserId: number, searchTerm: string, env: Env): Promise<Quest | null> {
    const functionName = 'findQuestByIdOrDescription';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, searchTerm } };
    log(LogLevel.INFO, 'Attempting to find quest by ID or description', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to find quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }
    if (!searchTerm?.trim()) {
        const errorMsg = 'searchTerm is required to find quest';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'searchTerm not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'searchTerm not provided' });
    }

    const supabase = getSupabaseClient(env);
    const trimmedSearchTerm = searchTerm.trim();

    try {
        // Check if searchTerm is numeric (quest ID)
        const questId = parseInt(trimmedSearchTerm, 10);
        if (!isNaN(questId) && questId.toString() === trimmedSearchTerm) {
            log(LogLevel.DEBUG, 'Searching for quest by ID', { ...logContext, payload: { ...logContext.payload, questId } });
            const response = await supabase
                .from('quests')
                .select('*')
                .eq('id', questId)
                .eq('user_id', actualUserId)
                .maybeSingle();

            const quest = processSupabaseResponse(response, { ...logContext, operation: 'findQuestById' });
            if (quest) {
                log(LogLevel.INFO, 'Found quest by ID', { ...logContext, payload: { ...logContext.payload, questId: quest.id, questTitle: quest.title } });
            } else {
                log(LogLevel.INFO, 'No quest found with the specified ID', { ...logContext, payload: { ...logContext.payload, questId } });
            }
            return quest;
        } else {
            // Search by title or description using case-insensitive partial matching
            log(LogLevel.DEBUG, 'Searching for quest by title/description', { ...logContext });
            const response = await supabase
                .from('quests')
                .select('*')
                .eq('user_id', actualUserId)
                .or(`title.ilike.%${trimmedSearchTerm}%, description.ilike.%${trimmedSearchTerm}%`)
                .order('created_at', { ascending: false })
                .limit(1)
                .maybeSingle();

            const quest = processSupabaseResponse(response, { ...logContext, operation: 'findQuestByDescription' });
            if (quest) {
                log(LogLevel.INFO, 'Found quest by title/description', { ...logContext, payload: { ...logContext.payload, questId: quest.id, questTitle: quest.title } });
            } else {
                log(LogLevel.INFO, 'No quest found matching the search term', { ...logContext });
            }
            return quest;
        }
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'findQuestByIdOrDescription: Unexpected error during DB operation', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected database error in findQuestByIdOrDescription', { originalError: e.message }, e);
        }
        throw e;
    }
}
