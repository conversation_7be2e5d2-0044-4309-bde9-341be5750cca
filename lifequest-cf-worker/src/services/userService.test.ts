import { vi, describe, it, expect, beforeEach, afterEach, Mock } from 'vitest';
import { getSupabaseClient } from '../supabaseClient';
import * as userServiceActual from './userService';
import { UserProfile, UserInsert } from './userService';
import { Env } from '../types';
import { SupabaseError } from '../errors/AppError';
import { log, LogLevel } from '../utils/logger';

// Mock Supabase client
vi.mock('../supabaseClient', () => ({
    getSupabaseClient: vi.fn(),
}));

// Mock logger
vi.mock('../utils/logger', async () => {
  const actual = await vi.importActual('../utils/logger');
  return {
    ...actual,
    log: vi.fn(),
  };
});

const mockEnv = {} as Env;
const testTelegramId = 12345;
const testActualUserId = 1;

const mockUser: UserProfile = {
    id: testActualUserId,
    telegram_id: testTelegramId,
    created_at: new Date().toISOString(),
    settings: null,
};

describe('User Service', () => {
    let mockSupabase: any;
    let mockQueryBuilder: any;

    beforeEach(() => {
        vi.clearAllMocks();
        (log as Mock).mockClear();

        mockQueryBuilder = {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn(),
            single: vi.fn(),
            insert: vi.fn().mockReturnThis(),
        };
        mockSupabase = {
            from: vi.fn(() => mockQueryBuilder),
        };
        (getSupabaseClient as Mock).mockReturnValue(mockSupabase);
    });

    describe('checkUserExists', () => {
        it('should return true if user exists (count > 0)', async () => {
            mockQueryBuilder.eq.mockResolvedValueOnce({ count: 1, error: null, data: null, status: 200, statusText: 'OK' });
            const result = await userServiceActual.checkUserExists(testTelegramId, mockEnv);
            expect(result).toBe(true);
            expect(mockQueryBuilder.select).toHaveBeenCalledWith('id', { count: 'exact', head: true });
            expect(mockQueryBuilder.eq).toHaveBeenCalledWith('telegram_id', testTelegramId);
        });

        it('should return false if user does not exist (count = 0)', async () => {
            mockQueryBuilder.eq.mockResolvedValueOnce({ count: 0, error: null, data: null, status: 200, statusText: 'OK' });
            const result = await userServiceActual.checkUserExists(testTelegramId, mockEnv);
            expect(result).toBe(false);
        });

        it('should throw SupabaseError if telegramId is not provided', async () => {
            await expect(userServiceActual.checkUserExists(0, mockEnv)).rejects.toThrow(SupabaseError);
            await expect(userServiceActual.checkUserExists(0, mockEnv)).rejects.toThrow('telegramId is required');
        });

        it('should throw SupabaseError if Supabase count query fails', async () => {
            const dbError = { message: 'Count query failed', code: 'DB101', details: '', hint: '' };
            // Mock for first call
            mockQueryBuilder.eq.mockResolvedValueOnce({ count: null, error: dbError, data: null, status: 500, statusText: 'Error' });
            // Mock for second call
            mockQueryBuilder.eq.mockResolvedValueOnce({ count: null, error: dbError, data: null, status: 500, statusText: 'Error' });
            await expect(userServiceActual.checkUserExists(testTelegramId, mockEnv)).rejects.toThrow(SupabaseError);
            await expect(userServiceActual.checkUserExists(testTelegramId, mockEnv)).rejects.toThrow(dbError.message);
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, expect.stringContaining(dbError.message), expect.any(Object));
        });

        it('should throw SupabaseError on unexpected error during count', async () => {
            const unexpectedError = new Error('Network issue');
            // Mock for first call
            mockQueryBuilder.eq.mockRejectedValueOnce(unexpectedError);
            // Mock for second call
            mockQueryBuilder.eq.mockRejectedValueOnce(unexpectedError);
            await expect(userServiceActual.checkUserExists(testTelegramId, mockEnv)).rejects.toThrow(SupabaseError);
            await expect(userServiceActual.checkUserExists(testTelegramId, mockEnv)).rejects.toThrow(unexpectedError.message);
        });
    });

    describe('getOrCreateUser', () => {
        it('should return existing user if found', async () => {
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: mockUser, error: null });
            const result = await userServiceActual.getOrCreateUser(testTelegramId, mockEnv);
            expect(result).toEqual(mockUser);
            expect(mockQueryBuilder.select).toHaveBeenCalledWith('*');
            expect(mockQueryBuilder.eq).toHaveBeenCalledWith('telegram_id', testTelegramId);
        });

        it('should create and return new user if not found', async () => {
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null }); // User not found
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockUser, error: null }); // Mock insert success
            
            const result = await userServiceActual.getOrCreateUser(testTelegramId, mockEnv);
            expect(result).toEqual(mockUser);
            expect(mockQueryBuilder.insert).toHaveBeenCalledWith({ telegram_id: testTelegramId });
        });

        it('should throw SupabaseError if telegramId is not provided', async () => {
            await expect(userServiceActual.getOrCreateUser(0, mockEnv)).rejects.toThrow(SupabaseError);
            await expect(userServiceActual.getOrCreateUser(0, mockEnv)).rejects.toThrow('telegramId is required');
        });

        it('should throw SupabaseError if fetch fails', async () => {
            const dbError = { message: 'Fetch failed', code: 'DB102', details: '', hint: '' };
            // Mock for first call
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });
            // Mock for second call
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: dbError });
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(SupabaseError);
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(dbError.message);
        });

        it('should throw SupabaseError if insert fails', async () => {
            const dbError = { message: 'Insert failed', code: 'DB103', details: '', hint: '' };
            // Mock for first call
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null }); 
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError }); 
            // Mock for second call
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null }); 
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError }); 
            
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(SupabaseError);
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(dbError.message);
        });

        it('should throw SupabaseError if insert returns no data unexpectedly', async () => {
            mockSupabase.from.mockReturnValueOnce({ // Initial fetch (user not found)
                select: vi.fn().mockReturnThis(),
                eq: vi.fn().mockReturnThis(),
                maybeSingle: vi.fn().mockResolvedValueOnce({ data: null, error: null })
            });
            mockSupabase.from.mockReturnValueOnce({ // Insert (succeeds with no immediate data return or error)
                insert: vi.fn().mockResolvedValueOnce({ error: null }) 
            });
            mockSupabase.from.mockReturnValueOnce({ // Re-fetch (succeeds but finds no user, data is null, no error object)
                select: vi.fn().mockReturnThis(),
                eq: vi.fn().mockReturnThis(),
                single: vi.fn().mockResolvedValueOnce({ data: null, error: null })
            });

            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv))
                .rejects.toThrow('Could not retrieve user record immediately after creation attempt.');
        });

        it('should throw SupabaseError on unexpected error during fetch', async () => {
            const unexpectedError = new Error('Network issue during fetch');
            // Mock for first call
            mockQueryBuilder.maybeSingle.mockRejectedValueOnce(unexpectedError);
            // Mock for second call
            mockQueryBuilder.maybeSingle.mockRejectedValueOnce(unexpectedError);
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(SupabaseError);
            // The service wraps this, so the message will be based on the wrapper
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow('Unexpected error fetching user in getOrCreateUser');
        });

        it('should throw SupabaseError on unexpected error during insert', async () => {
            const unexpectedError = new Error('Network issue during insert');
            // Mock for first call
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null }); 
            mockQueryBuilder.single.mockRejectedValueOnce(unexpectedError); 
            // Mock for second call
            mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null }); 
            mockQueryBuilder.single.mockRejectedValueOnce(unexpectedError); 

            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(SupabaseError);
            // The service wraps this error, and the SupabaseError will use the original error's message.
            await expect(userServiceActual.getOrCreateUser(testTelegramId, mockEnv)).rejects.toThrow(unexpectedError.message);
        });
    });
}); 