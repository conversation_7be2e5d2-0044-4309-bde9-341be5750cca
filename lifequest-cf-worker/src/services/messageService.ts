import { Env } from '../types';
import { getSupabaseClient } from '../supabaseClient';
import { Database } from '../types/supabase';
import { log, LogLevel, LogContext } from '../utils/logger';
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';

export type MessageInsert = Database['public']['Tables']['messages']['Insert'];

const SERVICE_NAME = 'MessageService';
const EXTENDED_HISTORY_BATCH_SIZE = 100; // Configurable: messages per batch
const TARGET_TOKEN_ESTIMATE_DIVISOR = 3.5; // Heuristic for char_length / divisor = tokens
const DEFAULT_EXTENDED_HISTORY_TOKEN_TARGET = 200000;
const EXTENDED_HISTORY_TOKEN_BUFFER = 20000; // Fetch up to target + buffer
const MAX_FETCH_ITERATIONS = 500; // Safety break for the fetch loop (e.g., 500*100 = 50,000 messages max)

/**
 * Represents a message item formatted for chat history, including its database ID for pagination.
 */
export interface ChatHistoryMessage {
  id: number; // Message ID from DB, essential for keyset pagination
  role: 'user' | 'assistant' | string; // DB role is string, be more specific if possible
  content: string;
  timestamp: string; // ISO timestamp string
}

/**
 * Logs a message to the Supabase database.
 * @param actualUserId The actual primary key ID of the user from the 'users' table.
 * @param role The role of the message sender ('user' or 'assistant').
 * @param content The content of the message.
 * @param env The Cloudflare Worker environment.
 * @throws {SupabaseError} if actualUserId is not provided or if the database operation fails.
 */
export async function logMessage(
  actualUserId: number, 
  role: 'user' | 'assistant',
  content: string,
  env: Env
): Promise<void> { // Returns void on success, throws on error
  const functionName = 'logMessage';
  const logContext: LogContext = { 
    service: SERVICE_NAME, 
    functionName, 
    payload: { actualUserId, role, contentLength: content.length }
  };
  log(LogLevel.INFO, 'Attempting to log message to DB', logContext);

  if (!actualUserId) {
    const errorMsg = 'actualUserId is required to log a message';
    log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
    throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
  }
  if (!content) {
    const errorMsg = 'content is required to log a message';
    log(LogLevel.WARN, errorMsg, { ...logContext, error: 'content not provided' });
    // Depending on strictness, could throw or just not log an empty message.
    // For now, let's prevent logging empty messages and return, or throw if it's an error.
    // Throwing an error might be too strict if an empty message is possible but should be ignored.
    // For consistency with other input validations, let's throw.
    throw new SupabaseError(errorMsg, { ...logContext, error: 'content not provided' });
  }

  const supabase = getSupabaseClient(env);
  const messageToInsert: MessageInsert = {
    user_id: actualUserId,
    role,
    content,
  };

  try {
    const response = await supabase.from('messages').insert(messageToInsert);
    // For insert without a .select(), Supabase client might return { data: null, error: null } on success,
    // or { data: null, error: PostgrestError } on failure.
    // processSupabaseResponse handles the error part.
    // If data is null and no error, it logs a warning but doesn't throw by default for list operations.
    // For insert, if no error, it's considered a success. We are not expecting data back here.
    if (response.error) {
        handleSupabaseError(response.error, { ...logContext, operation: 'logMessage.insert' });
    }
    // If no error, the insert is successful.
    log(LogLevel.INFO, 'Message logged successfully to DB', logContext);
  } catch (e: any) {
    if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'logMessage: Unexpected error during DB operation', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected database error in logMessage', { originalError: e.message }, e);
    }
    throw e; 
  }
} 

/**
 * Represents a row from the 'messages' table in the database.
 * Includes all fields of a message record such as ID, user ID, role, content, and timestamps.
 */
export type MessageRow = Database['public']['Tables']['messages']['Row'];

/**
 * Retrieves recent messages for a given user.
 * @param actualUserId The user's actual ID from the 'users' table.
 * @param env The Cloudflare Worker environment.
 * @param limit The maximum number of messages to retrieve (default: 10).
 * @returns An array of MessageRow objects or an empty array if none found or error.
 * @throws {SupabaseError} if actualUserId is not provided or if the database operation fails.
 */
export async function getRecentMessages(
  actualUserId: number,
  env: Env,
  limit: number = 10
): Promise<MessageRow[]> {
  const functionName = 'getRecentMessages';
  const logContext: LogContext = { 
    service: SERVICE_NAME, 
    functionName, 
    payload: { actualUserId, limit }
  };
  log(LogLevel.INFO, 'Attempting to get recent messages for user', logContext);

  if (!actualUserId) {
    const errorMsg = 'actualUserId is required to get recent messages';
    log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
    throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
  }

  const supabase = getSupabaseClient(env);
  try {
    const response = await supabase
      .from('messages')
      .select('*')
      .eq('user_id', actualUserId)
      .order('timestamp', { ascending: false })
      .limit(limit);

    const data = processSupabaseResponse(response, { ...logContext, operation: 'fetchRecentMessages' });

    if (!data) {
      log(LogLevel.INFO, 'No recent messages found for user', logContext);
      return [];
    }
    log(LogLevel.INFO, `Recent messages fetched successfully: ${data.length} messages.`, logContext);
    return data.reverse(); // Return in chronological order (oldest first)
  } catch (error) {
    if (error instanceof SupabaseError) {
        throw error;
    }
    log(LogLevel.ERROR, 'Unexpected error fetching recent messages', { ...logContext, error: (error as Error).message, stack: (error as Error).stack });
    throw new SupabaseError('Unexpected error fetching recent messages', { originalError: (error as Error).message }, error as Error);
  }
} 

/**
 * Retrieves an extended chat history for a given user, aiming for a target token count.
 * Uses batched iterative retrieval with keyset pagination.
 *
 * @param actualUserId The user's actual ID from the 'users' table.
 * @param env The Cloudflare Worker environment.
 * @param options Optional parameters for fetching history.
 * @param options.tokenTarget Approximate number of tokens to retrieve. Defaults to 200,000.
 * @param options.batchSize Number of messages to fetch per database call. Defaults to 100.
 * @param options.maxIterations Safety limit for the number of fetch iterations. Defaults to 500.
 * @returns An array of ChatHistoryMessage objects, ordered chronologically (oldest first).
 * @throws {SupabaseError} if actualUserId is not provided or if a database operation fails.
 */
export async function getExtendedChatHistory(
  actualUserId: number,
  env: Env,
  options?: {
    tokenTarget?: number;
    batchSize?: number;
    maxIterations?: number;
  }
): Promise<ChatHistoryMessage[]> {
  const functionName = 'getExtendedChatHistory';
  const batchSize = options?.batchSize ?? EXTENDED_HISTORY_BATCH_SIZE;
  const tokenTarget = options?.tokenTarget ?? DEFAULT_EXTENDED_HISTORY_TOKEN_TARGET;
  const maxIterations = options?.maxIterations ?? MAX_FETCH_ITERATIONS;
  const effectiveTokenTargetWithBuffer = tokenTarget + EXTENDED_HISTORY_TOKEN_BUFFER;

  const logContext: LogContext = {
    service: SERVICE_NAME,
    functionName,
    actualUserId,
    payload: { tokenTarget, batchSize, maxIterations },
  };
  log(LogLevel.INFO, 'Attempting to get extended chat history for user', logContext);

  if (!actualUserId) {
    const errorMsg = 'actualUserId is required to get extended chat history';
    log(LogLevel.ERROR, errorMsg, { ...logContext, error: errorMsg });
    throw new SupabaseError(errorMsg, { ...logContext, error: errorMsg });
  }

  const supabase = getSupabaseClient(env);
  const retrievedHistory: ChatHistoryMessage[] = [];
  let totalApproxTokens = 0;
  let iterationCount = 0;

  let lastTimestamp: string | null = null;
  let lastId: number | null = null;

  try {
    while (iterationCount < maxIterations && totalApproxTokens < effectiveTokenTargetWithBuffer) {
      iterationCount++;
      let query = supabase
        .from('messages')
        .select('id, role, content, timestamp')
        .eq('user_id', actualUserId)
        .order('timestamp', { ascending: false })
        .order('id', { ascending: false }) // Secondary sort for stable pagination
        .limit(batchSize);

      if (lastTimestamp && lastId !== null) {
        query = query.or(`timestamp.lt.${lastTimestamp},and(timestamp.eq.${lastTimestamp},id.lt.${lastId})`);
      }
      
      const logFetchContext: LogContext = { 
        ...logContext, 
        operation: 'fetchBatch', 
        payload: { ...logContext.payload, iteration: iterationCount, lastTimestamp, lastId } 
      };
      log(LogLevel.DEBUG, 'Fetching batch of messages', logFetchContext);
      
      const response = await query;
      const batchMessages = processSupabaseResponse(response, { ...logFetchContext, operation: 'processBatchResponse' });

      if (!batchMessages || batchMessages.length === 0) {
        log(LogLevel.INFO, 'No more messages found for user, stopping.', logFetchContext);
        break; // No more messages
      }

      for (const msg of batchMessages) {
        if (!msg.timestamp || !msg.content || !msg.role || typeof msg.id !== 'number') {
          log(LogLevel.WARN, 'Skipping message with missing data in batch.', { ...logFetchContext, messageId: msg.id, operation: 'processMessageInBatch' });
          continue;
        }

        const messageApproxTokens = Math.ceil(msg.content.length / TARGET_TOKEN_ESTIMATE_DIVISOR);
        
        if (totalApproxTokens + messageApproxTokens <= effectiveTokenTargetWithBuffer) {
          totalApproxTokens += messageApproxTokens;
          retrievedHistory.unshift({
            id: msg.id,
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp,
          });
        } else {
          log(LogLevel.INFO, `Token limit reached (${totalApproxTokens} + ${messageApproxTokens} > ${effectiveTokenTargetWithBuffer}), stopping.`, { ...logFetchContext, operation: 'tokenLimitCheck' });
          iterationCount = maxIterations; 
          break; 
        }
      }

      if (batchMessages.length > 0) {
        const lastMessageInBatch = batchMessages[batchMessages.length - 1];
        lastTimestamp = lastMessageInBatch.timestamp;
        lastId = lastMessageInBatch.id;

        if (!lastTimestamp || lastId === null || typeof lastId !== 'number') { // Check type of lastId as well
            log(LogLevel.ERROR, 'Last message in batch has null or invalid timestamp or id, cannot continue pagination.', { ...logFetchContext, operation: 'updatePaginationCursors' });
            throw new SupabaseError('Invalid pagination cursor from last message in batch.', { ...logFetchContext, operation: 'updatePaginationCursors' });
        }
      }
       log(LogLevel.DEBUG, `Batch fetched. Total approx tokens: ${totalApproxTokens}. History length: ${retrievedHistory.length}`, { ...logFetchContext, operation: 'batchSummary' });
    }
  } catch (error) {
    if (error instanceof SupabaseError) {
      throw error;
    }
    log(LogLevel.ERROR, 'Unexpected error fetching extended chat history', { ...logContext, error: (error as Error).message, stack: (error as Error).stack });
    throw new SupabaseError('Unexpected error fetching extended chat history', { originalError: (error as Error).message }, error as Error);
  }

  if (iterationCount >= maxIterations && totalApproxTokens < tokenTarget) {
    log(LogLevel.WARN, `Max iterations (${maxIterations}) reached but token target (${tokenTarget}) not met. Returning ${retrievedHistory.length} messages with ~${totalApproxTokens} tokens.`, logContext);
  } else {
    log(LogLevel.INFO, `Extended chat history fetched successfully: ${retrievedHistory.length} messages, ~${totalApproxTokens} tokens.`, logContext);
  }
  
  return retrievedHistory;
} 