import { vi, describe, it, expect, beforeEach, afterEach, Mock } from 'vitest';
import { getSupabaseClient } from '../supabaseClient';
import * as questServiceActual from './questService'; 
import { Quest, QuestFilters, QuestUpdate, QuestInsert } from './questService'; 
import { Env, DEFAULT_STREAK_MILESTONES, StreakMilestone } from '../types';
import { SupabaseError } from '../errors/AppError';
import { log, LogLevel } from '../utils/logger';

// Mock Supabase client
vi.mock('../supabaseClient', () => ({
    getSupabaseClient: vi.fn(),
}));

// Mock logger (Added)
vi.mock('../utils/logger', async () => {
  const actual = await vi.importActual('../utils/logger');
  return {
    ...actual,
    log: vi.fn(),
  };
});

const mockEnv = {} as Env;
const testUserId = 1; 

describe('Quest Service', () => {
    let mockSupabase: any;
    let mockQueryBuilder: any;
    let consoleErrorSpy: any; 
    let consoleWarnSpy: any;  
    let deleteFirstEqSpy: Mock;
    let deleteSecondEqSpy: Mock;

    beforeEach(() => {
        vi.clearAllMocks();
        (log as Mock).mockClear(); // Clear logger mock

        mockQueryBuilder = {
            insert: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            in: vi.fn().mockReturnThis(),
            order: vi.fn(), // Removed mockResolvedValue, will be set per test
            update: vi.fn().mockReturnThis(),
            delete: vi.fn().mockReturnThis(),
            single: vi.fn(), // Removed mockResolvedValue, will be set per test
        };
        
        // Setup for specific delete chain: .delete().eq().eq()
        deleteFirstEqSpy = vi.fn();
        deleteSecondEqSpy = vi.fn().mockResolvedValue({ error: null }); 
        const firstEqReturnsObjectWithSecondEq = { 
            ...mockQueryBuilder, 
            eq: deleteSecondEqSpy 
        };
        deleteFirstEqSpy.mockReturnValue(firstEqReturnsObjectWithSecondEq);
        // Ensure mockQueryBuilder.delete returns an object that has .eq method which is deleteFirstEqSpy
        mockQueryBuilder.delete = vi.fn(() => ({
            ...mockQueryBuilder, // Spread other builder methods if needed after .delete()
            eq: deleteFirstEqSpy
        }));
        
        mockSupabase = {
            from: vi.fn(() => mockQueryBuilder),
        };
        (getSupabaseClient as Mock).mockReturnValue(mockSupabase);
        
        consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
        if (consoleErrorSpy) consoleErrorSpy.mockRestore();
        if (consoleWarnSpy) consoleWarnSpy.mockRestore();
        vi.clearAllMocks(); 
    });

    describe('createQuest', () => {
        const questInsertData: QuestInsert = {
            title: 'Test Quest',
            type: 'main',
            xp_reward: 100,
            description: 'A test quest description',
        };
        const mockCreatedQuest: Quest = { 
            id: 1, user_id: testUserId, title: 'Test Quest', type: 'main', 
            status: 'pending', xp_reward: 100, description: 'A test quest description',
            coin_reward: null, schedule: null, metadata: null, 
            completed_at: null, created_at: new Date().toISOString(), streak: null,
        };

        it('should create a new quest successfully', async () => {
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockCreatedQuest, error: null });
            const result = await questServiceActual.createQuest(testUserId, questInsertData, mockEnv);
            expect(result).toEqual(mockCreatedQuest);
        });
        
        it('should include metadata if provided in QuestInsert', async () => {
            const questDataWithMeta: QuestInsert = { ...questInsertData, metadata: { parent_quest_id: 10 } };
            mockQueryBuilder.single.mockResolvedValueOnce({ data: {...mockCreatedQuest, metadata: {parent_quest_id: 10}}, error: null });
            await questServiceActual.createQuest(testUserId, questDataWithMeta, mockEnv);
            expect(mockQueryBuilder.insert).toHaveBeenCalledWith(expect.objectContaining({
                metadata: { parent_quest_id: 10 },
            }));
        });
        
        it('should throw SupabaseError if Supabase insert fails', async () => {
            const dbError = { message: 'Insert failed', code: 'DB001', details: '', hint: '' };
            // Mock for the first call to createQuest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
            // Mock for the second call to createQuest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });

            await expect(questServiceActual.createQuest(testUserId, questInsertData, mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(questServiceActual.createQuest(testUserId, questInsertData, mockEnv))
                .rejects.toThrow(dbError.message);
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, expect.stringContaining(dbError.message), expect.any(Object));
        });

        it('should throw SupabaseError on unexpected exception', async () => {
            const unexpectedError = new Error('Test insert error');
            // Mock for the first call to createQuest
            mockQueryBuilder.single.mockRejectedValueOnce(unexpectedError);
            // Mock for the second call to createQuest
            mockQueryBuilder.single.mockRejectedValueOnce(unexpectedError);
            await expect(questServiceActual.createQuest(testUserId, questInsertData, mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(questServiceActual.createQuest(testUserId, questInsertData, mockEnv))
                .rejects.toThrow(unexpectedError.message);
        });

        it('should throw SupabaseError if title is missing', async () => {
            const invalidData = { ...questInsertData, title: '' };
            // No DB call for the first assertion, but createQuest is called twice
            await expect(questServiceActual.createQuest(testUserId, invalidData, mockEnv))
                .rejects.toThrow(SupabaseError);
            // No DB call for the second assertion
            await expect(questServiceActual.createQuest(testUserId, invalidData, mockEnv))
                .rejects.toThrow('Quest title is required');
        });
    });

    describe('getQuests', () => {
        const mockQuests: Quest[] = [
            { id: 1, user_id: testUserId, title: 'Quest 1', type: 'main', status: 'active', created_at: new Date().toISOString(), description: null, xp_reward: null, coin_reward: null, schedule: null, metadata: null, completed_at: null, streak: null },
            { id: 2, user_id: testUserId, title: 'Quest 2', type: 'side', status: 'done', created_at: new Date().toISOString(), description: null, xp_reward: null, coin_reward: null, schedule: null, metadata: null, completed_at: null, streak: null },
        ];

        it('should retrieve quests for a user successfully', async () => {
            mockQueryBuilder.order.mockResolvedValueOnce({ data: mockQuests, error: null });
            const result = await questServiceActual.getQuests(testUserId, {}, mockEnv);
            expect(result).toEqual(mockQuests);
        });
        
        it('should apply status filter (single string)', async () => {
            const filters: QuestFilters = { status: 'active' };
            mockQueryBuilder.order.mockResolvedValueOnce({ data: [mockQuests[0]], error: null });
            await questServiceActual.getQuests(testUserId, filters, mockEnv);
            expect(mockQueryBuilder.eq).toHaveBeenCalledWith('status', 'active');
        });
        
        it('should apply type filter (single string)', async () => {
            const filters: QuestFilters = { type: 'main' };
            mockQueryBuilder.order.mockResolvedValueOnce({ data: [mockQuests[0]], error: null });
            await questServiceActual.getQuests(testUserId, filters, mockEnv);
            expect(mockQueryBuilder.eq).toHaveBeenCalledWith('type', 'main');
        });

        it('should return empty array if no quests found', async () => {
            mockQueryBuilder.order.mockResolvedValueOnce({ data: [], error: null });
            const result = await questServiceActual.getQuests(testUserId, {}, mockEnv);
            expect(result).toEqual([]);
        });

        it('should log warning and return empty array if data is null without error from Supabase', async () => {
            mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: null });
            const result = await questServiceActual.getQuests(testUserId, {}, mockEnv);
            expect(result).toEqual([]);
            expect(log).toHaveBeenCalledWith(LogLevel.WARN, expect.stringContaining('null data without an explicit error'), expect.any(Object));
        });

        it('should throw SupabaseError if Supabase select fails', async () => {
            const dbError = { message: 'Select failed', code: 'DB002', details: '', hint: '' };
            // Mock for the first call to getQuests
            mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: dbError });
            // Mock for the second call to getQuests
            mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: dbError });
            await expect(questServiceActual.getQuests(testUserId, {}, mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(questServiceActual.getQuests(testUserId, {}, mockEnv))
                .rejects.toThrow(dbError.message);
        });
    });

    describe('updateQuest', () => {
        const questId = 1;
        const updates: QuestUpdate = { title: 'Updated Quest Title', status: 'done' };
        const mockUpdatedQuest: Quest = { 
            id: questId, user_id: testUserId, title: 'Updated Quest Title', status: 'done', 
            created_at: 'some-date', type: 'main', description: null, xp_reward: null, 
            coin_reward: null, schedule: null, metadata: null, completed_at: null, streak: null
        };

        it('should update a quest successfully', async () => {
            mockQueryBuilder.single.mockResolvedValueOnce({ data: mockUpdatedQuest, error: null });
            const result = await questServiceActual.updateQuest(questId, updates, mockEnv, testUserId);
            expect(result).toEqual(mockUpdatedQuest);
        });

        it('should throw SupabaseError if Supabase update fails', async () => {
            const dbError = { message: 'Update failed', code: 'DB003', details: '', hint: '' };
            // Mock for the first call to updateQuest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
            // Mock for the second call to updateQuest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
            await expect(questServiceActual.updateQuest(questId, updates, mockEnv, testUserId))
                .rejects.toThrow(SupabaseError);
            await expect(questServiceActual.updateQuest(questId, updates, mockEnv, testUserId))
                .rejects.toThrow(dbError.message);
        });
        
        it('should throw SupabaseError if quest not found or user mismatch (data null from .single())', async () => {
            const noDataError = { message: 'No row found for update/select .single()', code: 'PGRST116', details:'', hint:'' };
            // Mock for the first call to updateQuest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: noDataError }); 
            // Mock for the second call to updateQuest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: noDataError }); 
            await expect(questServiceActual.updateQuest(questId, updates, mockEnv, testUserId))
                .rejects.toThrow(SupabaseError);
             await expect(questServiceActual.updateQuest(questId, updates, mockEnv, testUserId))
                .rejects.toThrow(noDataError.message);
        });
    });

    describe('completeQuest', () => {
        const questId = 1;
        const completedQuestData: Quest = { 
            id: questId, user_id: testUserId, title: 'Original Title', status: 'done', 
            created_at: 'some-date', type: 'main', description: null, xp_reward: null, 
            coin_reward: null, schedule: null, metadata: null, 
            completed_at: new Date().toISOString(), // This will be close, not exact
            streak: null
        };

        it('should complete a regular quest successfully', async () => {
            // For regular (non-routine) quests, the original questData should have status: 'pending'
            const originalQuestData: Quest = { 
                ...completedQuestData, 
                status: 'pending' // Original status before completion
            };
            
            // Mock the initial select to get the quest (needed for new completeQuest logic)
            mockQueryBuilder.single.mockResolvedValueOnce({ data: originalQuestData, error: null });
            // Mock the update call (which happens in updateQuest for regular quests)
            mockQueryBuilder.single.mockResolvedValueOnce({ data: completedQuestData, error: null });
            
            const result = await questServiceActual.completeQuest(questId, mockEnv, testUserId);
            
            // New return type: { quest, streakBonus?, streakIncremented? }
            expect(result.quest.status).toBe('done');
            expect(result.quest.id).toBe(questId);
            expect(result.streakBonus).toBeUndefined(); // No streak bonus for regular quests
            expect(result.streakIncremented).toBeUndefined(); // No streak increment for regular quests
            expect(mockQueryBuilder.update).toHaveBeenCalledWith(expect.objectContaining({ 
                status: 'done', 
                completed_at: expect.any(String) 
            }));
        });

        it('should complete a routine quest with streak logic', async () => {
            // Set up a routine quest
            const routineQuestData: Quest = { 
                id: questId, user_id: testUserId, title: 'Daily Exercise', status: 'pending', 
                created_at: 'some-date', type: 'routine', description: 'Daily workout routine', 
                xp_reward: 10, coin_reward: 5, 
                schedule: { type: 'daily' },
                metadata: {}, completed_at: null, streak: 0
            };
            
            const completedRoutineQuest: Quest = {
                ...routineQuestData,
                status: 'done',
                completed_at: new Date().toISOString(),
                streak: 1, // First completion
                metadata: { last_streak_milestone: 0 }
            };
            
            // Mock the initial select to get the routine quest
            mockQueryBuilder.single.mockResolvedValueOnce({ data: routineQuestData, error: null });
            // Mock the update call for routine quest completion (updateRoutineQuestCompletion)
            mockQueryBuilder.single.mockResolvedValueOnce({ data: completedRoutineQuest, error: null });
            
            const result = await questServiceActual.completeQuest(questId, mockEnv, testUserId);
            
            // Check result structure for routine quest
            expect(result.quest.status).toBe('done');
            expect(result.quest.id).toBe(questId);
            expect(result.quest.streak).toBe(1);
            expect(result.streakIncremented).toBe(true);
            expect(result.streakBonus).toBeNull(); // No milestone reached at streak 1
            expect(mockQueryBuilder.update).toHaveBeenCalledWith(expect.objectContaining({ 
                status: 'done', 
                completed_at: expect.any(String),
                streak: 1,
                metadata: expect.any(Object)
            }));
        });

        it('should throw SupabaseError if quest fetch fails', async () => {
            const dbError = { message: 'Quest not found', code: 'DB004', details: '', hint: '' };
            // Mock the initial select to fail (quest not found)
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
            mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
             await expect(questServiceActual.completeQuest(questId, mockEnv, testUserId))
                .rejects.toThrow(SupabaseError);
            await expect(questServiceActual.completeQuest(questId, mockEnv, testUserId))
                .rejects.toThrow(dbError.message);
        });
    });

    describe('deleteQuest', () => {
        const questId = 1;

        it('should delete a quest successfully', async () => {
            deleteSecondEqSpy.mockResolvedValueOnce({ error: null, data: null }); // delete might not return data
            await questServiceActual.deleteQuest(questId, mockEnv, testUserId);
            expect(mockSupabase.from).toHaveBeenCalledWith('quests');
            expect(mockQueryBuilder.delete).toHaveBeenCalled();
            expect(deleteFirstEqSpy).toHaveBeenCalledWith('id', questId);
            expect(deleteSecondEqSpy).toHaveBeenCalledWith('user_id', testUserId);
        });

        it('should throw SupabaseError if Supabase delete operation fails', async () => {
            const dbError = { message: 'Delete failed', code: 'DB005', details: '', hint: '' };
            // Mock for the first call to deleteQuest
            deleteSecondEqSpy.mockResolvedValueOnce({ error: dbError });
            // Mock for the second call to deleteQuest
            deleteSecondEqSpy.mockResolvedValueOnce({ error: dbError });
            await expect(questServiceActual.deleteQuest(questId, mockEnv, testUserId))
                .rejects.toThrow(SupabaseError);
            await expect(questServiceActual.deleteQuest(questId, mockEnv, testUserId))
                .rejects.toThrow(dbError.message);
        });
    });
}); 

// New describe block for checkQuestSchedule (non-exported function)
// To test non-exported functions, we can expose them via a test-only export
// or test them indirectly through exported functions. For now, let's assume
// we might need to refactor questService.ts slightly to export it for testing OR
// acknowledge these tests might be for planning/demonstration if direct import isn't feasible without refactor.
// For actual execution, questServiceActual.checkQuestSchedule would be used if exported.
// If not, these tests demonstrate the logic that needs verification.

// Assuming questService.ts is refactored to export checkQuestSchedule for testing:
// import { checkQuestSchedule, Quest } from './questService'; // Adjust if Quest type is already imported

describe('checkQuestSchedule (internal)', () => {
    const mockBaseQuest: Omit<Quest, 'id' | 'user_id' | 'title' | 'type' | 'status' | 'created_at'> = {
        description: null,
        xp_reward: null,
        coin_reward: null,
        schedule: null,
        metadata: null,
        completed_at: null,
        streak: null,
    };

    const createMockQuest = (
        type: 'routine' | 'main' | 'side', 
        schedule: Quest['schedule'], 
        completedAt?: string | null,
        id = 1
    ): Quest => ({
        id,
        user_id: testUserId,
        title: 'Test Quest',
        status: 'pending',
        created_at: new Date().toISOString(),
        ...mockBaseQuest,
        type,
        schedule,
        completed_at: completedAt === undefined ? null : completedAt,
    });

    // Test cases for daily quests
    describe('Daily Quests', () => {
        const dailySchedule = { type: 'daily' } as unknown as Quest['schedule'];

        it('should be on schedule for first completion', () => {
            const quest = createMockQuest('routine', dailySchedule, null);
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
            expect(result.daysSinceLastCompletion).toBe(0);
        });

        it('should be on schedule if completed 1 day after last completion', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 1);
            const quest = createMockQuest('routine', dailySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
            expect(result.daysSinceLastCompletion).toBe(1);
        });

        it('should be on schedule if completed 2 days after last completion (grace period)', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 2);
            const quest = createMockQuest('routine', dailySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
            expect(result.daysSinceLastCompletion).toBe(2);
        });

        it('should reset streak if completed more than 2 days after last completion', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 3); // 3 days ago
            const quest = createMockQuest('routine', dailySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(false); // Not on schedule for increment
            expect(result.streakShouldReset).toBe(true);
            expect(result.daysSinceLastCompletion).toBe(3);
        });
    });

    // Test cases for weekly quests
    describe('Weekly Quests', () => {
        const weeklySchedule = { type: 'weekly' } as unknown as Quest['schedule'];

        it('should be on schedule for first completion', () => {
            const quest = createMockQuest('routine', weeklySchedule, null);
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
        });

        it('should be on schedule if completed 6 days after last completion', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 6);
            const quest = createMockQuest('routine', weeklySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
        });
        
        it('should be on schedule if completed 7 days after last completion', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 7);
            const quest = createMockQuest('routine', weeklySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
        });

        it('should be on schedule if completed 8 days after last completion (grace period)', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 8);
            const quest = createMockQuest('routine', weeklySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);
        });

        it('should reset streak if completed more than 8 days after last completion', () => {
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 9); // 9 days ago
            const quest = createMockQuest('routine', weeklySchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(false); // Not on schedule for increment
            expect(result.streakShouldReset).toBe(true);
        });
    });

    // Test cases for other quest types or invalid schedules
    describe('Other Quest Types / Invalid Schedules', () => {
        it('should not be on schedule for non-routine quests', () => {
            const quest = createMockQuest('main', null, new Date().toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(false);
            expect(result.streakShouldReset).toBe(false);
        });

        it('should not be on schedule if routine quest has no schedule defined', () => {
            const quest = createMockQuest('routine', null, new Date().toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(false);
            expect(result.streakShouldReset).toBe(false);
        });
        
        it('should use daily logic as fallback for unknown custom schedule type', () => {
            const customSchedule = { type: 'custom_unknown' } as unknown as Quest['schedule'];
            const lastCompleted = new Date();
            lastCompleted.setDate(lastCompleted.getDate() - 1); // 1 day ago, should be on schedule
            const quest = createMockQuest('routine', customSchedule, lastCompleted.toISOString());
            const result = questServiceActual.checkQuestSchedule(quest, new Date());
            expect(result.isOnSchedule).toBe(true);
            expect(result.streakShouldReset).toBe(false);

            const lastCompletedLate = new Date();
            lastCompletedLate.setDate(lastCompletedLate.getDate() - 3); // 3 days ago, should reset
            const questLate = createMockQuest('routine', customSchedule, lastCompletedLate.toISOString());
            const resultLate = questServiceActual.checkQuestSchedule(questLate, new Date());
            expect(resultLate.isOnSchedule).toBe(false);
            expect(resultLate.streakShouldReset).toBe(true);
        });
    });
}); 

describe('calculateStreakBonus (internal)', () => {
    it('should return null if no milestone is reached', () => {
        expect(questServiceActual.calculateStreakBonus(2, 0)).toBeNull();
        expect(questServiceActual.calculateStreakBonus(1, 0)).toBeNull();
    });

    it('should return the correct milestone when reached (3 days)', () => {
        const expectedMilestone = DEFAULT_STREAK_MILESTONES.find((m: StreakMilestone) => m.days === 3);
        expect(questServiceActual.calculateStreakBonus(3, 0)).toEqual(expectedMilestone);
        expect(questServiceActual.calculateStreakBonus(3, 1)).toEqual(expectedMilestone); // lastMilestone less than current
    });

    it('should return the correct milestone when reached (7 days)', () => {
        const expectedMilestone = DEFAULT_STREAK_MILESTONES.find((m: StreakMilestone) => m.days === 7);
        expect(questServiceActual.calculateStreakBonus(7, 0)).toEqual(expectedMilestone);
        expect(questServiceActual.calculateStreakBonus(7, 3)).toEqual(expectedMilestone);
        expect(questServiceActual.calculateStreakBonus(8, 3)).toEqual(expectedMilestone); // Streak is 8, last was 3, so 7 is newest
    });

    it('should return the correct milestone when reached (100 days)', () => {
        const expectedMilestone = DEFAULT_STREAK_MILESTONES.find((m: StreakMilestone) => m.days === 100);
        expect(questServiceActual.calculateStreakBonus(100, 60)).toEqual(expectedMilestone);
        expect(questServiceActual.calculateStreakBonus(105, 60)).toEqual(expectedMilestone);
    });

    it('should return null if current streak has not passed the last milestone achieved', () => {
        expect(questServiceActual.calculateStreakBonus(7, 7)).toBeNull(); // Current streak is 7, last milestone was 7
        expect(questServiceActual.calculateStreakBonus(6, 7)).toBeNull(); // Current streak is 6, last milestone was 7
    });

    it('should return the highest applicable milestone if multiple are passed since last_milestone', () => {
        // e.g., streak is 15, last milestone was 3. Should get 14-day bonus, not 7-day.
        const expectedMilestone = DEFAULT_STREAK_MILESTONES.find((m: StreakMilestone) => m.days === 14);
        expect(questServiceActual.calculateStreakBonus(15, 3)).toEqual(expectedMilestone);
        expect(questServiceActual.calculateStreakBonus(14, 3)).toEqual(expectedMilestone);
    });

    it('should handle currentStreak being less than any milestone', () => {
        expect(questServiceActual.calculateStreakBonus(2, 0)).toBeNull();
    });

    it('should handle lastMilestone being higher than any defined milestone (e.g. custom future milestone)', () => {
        expect(questServiceActual.calculateStreakBonus(120, 110)).toBeNull(); // Assuming 100 is highest defined
    });
}); 

describe('resetMissedStreaks', () => {
    let mockSupabase: any;
    let mockQueryBuilder: any;

    beforeEach(() => {
        vi.clearAllMocks();

        mockQueryBuilder = {
            insert: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            in: vi.fn().mockReturnThis(),
            order: vi.fn(),
            update: vi.fn().mockReturnThis(),
            delete: vi.fn().mockReturnThis(),
            single: vi.fn(),
        };
        
        mockSupabase = {
            from: vi.fn(() => mockQueryBuilder),
        };
        (getSupabaseClient as Mock).mockReturnValue(mockSupabase);
    });
        const createMockRoutineQuest = (id: number, streak: number | null, lastCompletedDaysAgo?: number, scheduleType: 'daily' | 'weekly' = 'daily'): Quest => {
            let completed_at: string | null = null;
            if (lastCompletedDaysAgo !== undefined) {
                const d = new Date();
                d.setDate(d.getDate() - lastCompletedDaysAgo);
                completed_at = d.toISOString();
            }
            return {
                id,
                user_id: testUserId,
                title: `Routine Quest ${id}`,
                type: 'routine',
                status: 'pending',
                created_at: new Date().toISOString(),
                description: null,
                xp_reward: 10,
                coin_reward: 5,
                schedule: { type: scheduleType },
                metadata: { last_streak_milestone: 0 },
                completed_at,
                streak,
            };
        };

        it('should return an empty array if no quests need reset', async () => {
            const mockQuests: Quest[] = [
                createMockRoutineQuest(1, 5, 1), // Daily, completed 1 day ago, on schedule
                createMockRoutineQuest(2, 0),    // 0 streak
                { ...createMockRoutineQuest(3, 3, 1), type: 'main' }, // Not routine
                { ...createMockRoutineQuest(4, 2, 1), schedule: null }, // No schedule
            ];
            // Mock the getQuests call (for routine quests)
            mockQueryBuilder.order.mockResolvedValueOnce({ data: mockQuests, error: null });

            const result = await questServiceActual.resetMissedStreaks(testUserId, mockEnv);
            expect(result).toEqual([]);
            expect(log).toHaveBeenCalledWith(LogLevel.INFO, expect.stringContaining('Completed streak reset check - reset 0 quests'), expect.any(Object));
        });

        it('should reset one quest that missed its daily schedule', async () => {
            const questToReset = createMockRoutineQuest(1, 5, 3); // Daily, completed 3 days ago (missed)
            const mockQuests: Quest[] = [
                questToReset,
                createMockRoutineQuest(2, 3, 1), // Daily, on schedule
            ];
            // Mock getQuests call
            mockQueryBuilder.order.mockResolvedValueOnce({ data: mockQuests, error: null });
            // Mock updateQuest call
            mockQueryBuilder.single.mockResolvedValueOnce({ data: { ...questToReset, streak: 0 }, error: null });

            const result = await questServiceActual.resetMissedStreaks(testUserId, mockEnv);
            expect(result).toEqual([questToReset.id]);
            expect(log).toHaveBeenCalledWith(LogLevel.INFO, expect.stringContaining('Reset streak for missed routine quest'), expect.objectContaining({ payload: expect.objectContaining({ questId: questToReset.id }) }));
        });

        it('should reset multiple quests that missed their schedules', async () => {
            const quest1Reset = createMockRoutineQuest(1, 5, 4); // Daily, missed
            const quest2NoReset = createMockRoutineQuest(2, 3, 1); // Daily, on schedule
            const quest3Reset = createMockRoutineQuest(3, 7, 10, 'weekly'); // Weekly, missed (completed 10 days ago)
            const mockQuests: Quest[] = [quest1Reset, quest2NoReset, quest3Reset];
            
            // Mock getQuests call
            mockQueryBuilder.order.mockResolvedValueOnce({ data: mockQuests, error: null });
            // Mock updateQuest calls for the two resets
            mockQueryBuilder.single
                .mockResolvedValueOnce({ data: { ...quest1Reset, streak: 0 }, error: null })
                .mockResolvedValueOnce({ data: { ...quest3Reset, streak: 0 }, error: null });

            const result = await questServiceActual.resetMissedStreaks(testUserId, mockEnv);
            expect(result).toEqual(expect.arrayContaining([quest1Reset.id, quest3Reset.id]));
            expect(result.length).toBe(2);
        });

        it('should not reset quests with null streak or already 0 streak', async () => {
            const mockQuests: Quest[] = [
                createMockRoutineQuest(1, null, 3), // Null streak
                createMockRoutineQuest(2, 0, 4),    // Already 0 streak
            ];
            // Mock getQuests call
            mockQueryBuilder.order.mockResolvedValueOnce({ data: mockQuests, error: null });

            const result = await questServiceActual.resetMissedStreaks(testUserId, mockEnv);
            expect(result).toEqual([]);
        });

        it('should continue processing if one quest update fails', async () => {
            const quest1Reset = createMockRoutineQuest(1, 5, 3);
            const quest2Reset = createMockRoutineQuest(2, 4, 4);
            const mockQuests: Quest[] = [quest1Reset, quest2Reset];
            const updateError = { message: 'Update failed for quest 1', code: 'DB006', details: '', hint: '' };

            // Mock getQuests call
            mockQueryBuilder.order.mockResolvedValueOnce({ data: mockQuests, error: null });
            // Mock updateQuest calls - first fails, second succeeds
            mockQueryBuilder.single
                .mockResolvedValueOnce({ data: null, error: updateError }) // Fail for quest1
                .mockResolvedValueOnce({ data: { ...quest2Reset, streak: 0 }, error: null }); // Succeed for quest2

            const result = await questServiceActual.resetMissedStreaks(testUserId, mockEnv);
            expect(result).toEqual([quest2Reset.id]); // Only quest2 was successfully reset
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Failed to reset streak for quest', expect.objectContaining({ payload: expect.objectContaining({ questId: quest1Reset.id }), error: expect.stringContaining(updateError.message) }));
        });

        it('should throw SupabaseError if getQuests fails', async () => {
            const getQuestsError = { message: 'Failed to fetch quests', code: 'DB007', details: '', hint: '' };
            // Mock getQuests call to fail
            mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: getQuestsError });

            await expect(questServiceActual.resetMissedStreaks(testUserId, mockEnv))
                .rejects.toThrow(SupabaseError);
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, expect.stringContaining(getQuestsError.message), expect.any(Object));
        });

         it('should throw SupabaseError if actualUserId is not provided', async () => {
            // @ts-expect-error: Testing invalid input
            await expect(questServiceActual.resetMissedStreaks(null, mockEnv))
                .rejects.toThrow(SupabaseError);
            // @ts-expect-error: Testing invalid input
            await expect(questServiceActual.resetMissedStreaks(null, mockEnv))
                .rejects.toThrow('actualUserId is required to reset missed streaks');
        });
});