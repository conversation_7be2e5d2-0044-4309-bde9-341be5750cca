import { SupabaseClient } from '@supabase/supabase-js';
import { Env } from '../types';
import { Database } from '../types/supabase';
import { getSupabaseClient } from '../supabaseClient';
import { log, LogLevel, LogContext } from '../utils/logger';
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';

// Types based on supabase.ts for knowledge_documents table
export type KnowledgeDocument = Database['public']['Tables']['knowledge_documents']['Row'];
export type KnowledgeDocumentInsert = Database['public']['Tables']['knowledge_documents']['Insert'];
export type KnowledgeDocumentUpdate = Database['public']['Tables']['knowledge_documents']['Update'];

const SERVICE_NAME = 'KnowledgeService';

/**
 * Creates a new knowledge document for a user.
 * @param actualUserId The internal ID of the user.
 * @param docData Data for the new knowledge document.
 * @param env Environment variables.
 * @returns The created knowledge document.
 * @throws {SupabaseError} if input is invalid or if the database operation fails.
 */
export async function createKnowledgeDocument(
  actualUserId: number,
  docData: { title: string; content: string; document_type: string },
  env: Env,
): Promise<KnowledgeDocument> {
  const functionName = 'createKnowledgeDocument';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, title: docData.title, type: docData.document_type } }; 
  log(LogLevel.INFO, 'Attempting to create knowledge document', logContext);

  if (!actualUserId) {
    const errorMsg = 'actualUserId is required';
    log(LogLevel.ERROR, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext);
  }
  if (!docData.title || !docData.content || !docData.document_type) {
    const errorMsg = 'title, content, and document_type are required';
    log(LogLevel.WARN, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext);
  }
  
  const supabase = getSupabaseClient(env);
  const insertData: KnowledgeDocumentInsert = {
    user_id: actualUserId,
    title: docData.title,
    content: docData.content,
    document_type: docData.document_type,
  };

  try {
    log(LogLevel.DEBUG, 'Inserting knowledge document into DB', { ...logContext, payload: { ...logContext.payload, insertData }});
    const response = await supabase
        .from('knowledge_documents')
        .insert(insertData)
        .select()
        .single();

    const createdDoc = processSupabaseResponse(response, { ...logContext, operation: 'createKnowledgeDoc.insert' });
    if (!createdDoc) {
        log(LogLevel.ERROR, 'Knowledge document insert succeeded but returned no data', { ...logContext });
        throw new SupabaseError('Knowledge document insert did not return data as expected', { ...logContext, operation: 'createKnowledgeDoc.insert' });
    }
    log(LogLevel.INFO, 'Successfully created knowledge document', { ...logContext, payload: { ...logContext.payload, docId: createdDoc.id }});
    return createdDoc;
  } catch (e: any) {
    if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'createKnowledgeDocument: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected error in createKnowledgeDocument', { originalError: e.message }, e);
    }
    throw e;
  }
}

/**
 * Retrieves knowledge documents for a user, optionally filtered by type.
 * @param actualUserId The internal ID of the user.
 * @param env Environment variables.
 * @param documentType Optional document type to filter by.
 * @returns An array of knowledge documents.
 * @throws {SupabaseError} if input is invalid or if the database operation fails.
 */
export async function getKnowledgeDocuments(
  actualUserId: number,
  env: Env,
  documentType?: string,
): Promise<KnowledgeDocument[]> {
  const functionName = 'getKnowledgeDocuments';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, documentType } };
  log(LogLevel.INFO, 'Attempting to retrieve knowledge documents', logContext);

  if (!actualUserId) {
    const errorMsg = 'actualUserId is required';
    log(LogLevel.ERROR, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext);
  }

  const supabase = getSupabaseClient(env);
  let query = supabase
    .from('knowledge_documents')
    .select('*')
    .eq('user_id', actualUserId);

  if (documentType) {
    query = query.eq('document_type', documentType);
  }
  query = query.order('created_at', { ascending: false });

  try {
    log(LogLevel.DEBUG, 'Fetching knowledge documents from DB', logContext);
    const response = await query;
    const documents = processSupabaseResponse(response, { ...logContext, operation: 'getKnowledgeDocs.select' });
    
    log(LogLevel.INFO, `Successfully retrieved ${documents?.length || 0} knowledge documents`, { ...logContext, count: documents?.length || 0 });
    return documents || [];
  } catch (e: any) {
    if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'getKnowledgeDocuments: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected error in getKnowledgeDocuments', { originalError: e.message }, e);
    }
    throw e;
  }
}

/**
 * Updates an existing knowledge document.
 * @param documentId The ID of the knowledge document to update.
 * @param updates Data to update the knowledge document with.
 * @param actualUserId The ID of the user performing the update for authorization.
 * @param env Environment variables.
 * @returns The updated knowledge document.
 * @throws {SupabaseError} if input is invalid or if the database operation fails.
 */
export async function updateKnowledgeDocument(
  documentId: number,
  updates: KnowledgeDocumentUpdate,
  actualUserId: number, // Added for authorization
  env: Env,
): Promise<KnowledgeDocument> {
  const functionName = 'updateKnowledgeDocument';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { documentId, updates, actualUserId } };
  log(LogLevel.INFO, 'Attempting to update knowledge document', logContext);

  if (!documentId || !actualUserId) {
    const errorMsg = 'documentId and actualUserId are required';
    log(LogLevel.ERROR, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext);
  }

  const supabase = getSupabaseClient(env);
  const updateData: KnowledgeDocumentUpdate = {
    ...updates,
    updated_at: new Date().toISOString(), 
  };

  try {
    log(LogLevel.DEBUG, 'Updating knowledge document in DB', { ...logContext, payload: { ...logContext.payload, updateData }});
    const response = await supabase
        .from('knowledge_documents')
        .update(updateData)
        .eq('id', documentId)
        .eq('user_id', actualUserId) // Authorization check
        .select()
        .single();

    const updatedDoc = processSupabaseResponse(response, { ...logContext, operation: 'updateKnowledgeDoc.update' });
    if (!updatedDoc) {
        log(LogLevel.WARN, 'Knowledge document not found or user mismatch during update, or .single() returned null unexpectedly', { ...logContext });
        throw new SupabaseError('Knowledge document not found, not authorized, or update yielded no data.', { ...logContext, operation: 'updateKnowledgeDoc.update' });
    }

    log(LogLevel.INFO, 'Successfully updated knowledge document', { ...logContext, payload: { ...logContext.payload, docId: updatedDoc.id }});
    return updatedDoc;
  } catch (e: any) {
    if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'updateKnowledgeDocument: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected error in updateKnowledgeDocument', { originalError: e.message }, e);
    }
    throw e;
  }
}

/**
 * Deletes a knowledge document.
 * @param documentId The ID of the knowledge document to delete.
 * @param actualUserId The ID of the user performing the delete for authorization.
 * @param env Environment variables.
 * @throws {SupabaseError} if input is invalid or if the database operation fails.
 */
export async function deleteKnowledgeDocument(
  documentId: number,
  actualUserId: number, // Added for authorization
  env: Env,
): Promise<void> {
  const functionName = 'deleteKnowledgeDocument';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { documentId, actualUserId } };
  log(LogLevel.INFO, 'Attempting to delete knowledge document', logContext);

  if (!documentId || !actualUserId) {
    const errorMsg = 'documentId and actualUserId are required';
    log(LogLevel.ERROR, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext);
  }

  const supabase = getSupabaseClient(env);
  try {
    log(LogLevel.DEBUG, 'Deleting knowledge document from DB', logContext);
    const response = await supabase
        .from('knowledge_documents')
        .delete()
        .eq('id', documentId)
        .eq('user_id', actualUserId); // Authorization check

    if (response.error) {
        handleSupabaseError(response.error, { ...logContext, operation: 'deleteKnowledgeDoc.delete' });
    }
    // Check if any row was actually deleted (optional, PostgREST might not error if 0 rows match)
    // if (response.count === 0) { log(LogLevel.WARN, 'No document found to delete or user mismatch', logContext); }

    log(LogLevel.INFO, 'Successfully deleted knowledge document (or no matching document found for user)', { ...logContext, success: true });
  } catch (e: any) {
    if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'deleteKnowledgeDocument: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected error in deleteKnowledgeDocument', { originalError: e.message }, e);
    }
    throw e;
  }
} 