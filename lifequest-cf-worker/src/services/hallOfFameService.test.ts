import { describe, it, expect, vi, beforeEach, Mock, afterEach } from 'vitest';
import { Env } from '../types';
import { HallOfFameEntry, HallOfFameEntryInsert, HallOfFameFilters, HallOfFameEntryUpdate, addHallOfFameEntry, getHallOfFameEntries, updateHallOfFameEntry, deleteHallOfFameEntry } from './hallOfFameService';
import { getSupabaseClient } from '../supabaseClient';
import { LogLevel, log } from '../utils/logger'; 
import { SupabaseError } from '../errors/AppError';

// Mock Supabase client
vi.mock('../supabaseClient', () => ({
  getSupabaseClient: vi.fn(),
}));

// Mock logger
vi.mock('../utils/logger', async () => {
    const actualLogger = await vi.importActual('../utils/logger') as any;
    return {
        ...actualLogger,
        log: vi.fn(),
    };
});

const mockEnv = {} as Env;
const actualUserId = 1;

describe('HallOfFameService', () => {
  let mockSupabaseInstance: any;
  let mockQueryBuilder: any;
  let deleteInitialEqMock: Mock;
  let deleteSecondEqMock: Mock;

  beforeEach(() => {
    vi.clearAllMocks();
    (log as Mock).mockClear();

    mockQueryBuilder = {
      insert: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      // delete will be specially mocked
      eq: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      contains: vi.fn().mockReturnThis(),
      order: vi.fn(), // Will be resolved per test for getHallOfFameEntries
      single: vi.fn(),
    };

    // Setup for specific delete chain: .delete().eq().eq()
    deleteSecondEqMock = vi.fn(); // This will be resolved per test for delete
    const firstEqReturnsObjectWithSecondEq = { 
        ...mockQueryBuilder, 
        eq: deleteSecondEqMock 
    };
    deleteInitialEqMock = vi.fn().mockReturnValue(firstEqReturnsObjectWithSecondEq);
    mockQueryBuilder.delete = vi.fn(() => ({ 
        ...mockQueryBuilder,
        eq: deleteInitialEqMock 
    }));

    mockSupabaseInstance = {
      from: vi.fn(() => mockQueryBuilder),
    };
    (getSupabaseClient as Mock).mockReturnValue(mockSupabaseInstance);
  });

  describe('addHallOfFameEntry', () => {
    const entryData: HallOfFameEntryInsert = {
      entry_type: 'victory',
      content: 'Conquered a fear',
      metadata: { tags: ['personal'] }
    };
    const expectedEntry: HallOfFameEntry = { id: 1, user_id: actualUserId, ...entryData, period: null, created_at: new Date().toISOString() } as HallOfFameEntry;

    it('should add an entry and return it', async () => {
      mockQueryBuilder.single.mockResolvedValueOnce({ data: expectedEntry, error: null });
      
      const result = await addHallOfFameEntry(actualUserId, entryData, mockEnv);

      expect(mockSupabaseInstance.from).toHaveBeenCalledWith('hall_of_fame');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith({ ...entryData, user_id: actualUserId });
      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.single).toHaveBeenCalled();
      expect(result).toEqual(expectedEntry);
    });

    it('should throw SupabaseError if Supabase insert fails', async () => {
      const dbError = { message: 'Insert failed', code: 'DB701', details: '', hint: '' };
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      // Test calls addHallOfFameEntry once
      await expect(addHallOfFameEntry(actualUserId, entryData, mockEnv)).rejects.toThrow(SupabaseError);
      // Call again to check the message (mock needs to be reset or set again for this specific call pattern)
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      await expect(addHallOfFameEntry(actualUserId, entryData, mockEnv)).rejects.toThrow(dbError.message);
    });
  });

  describe('getHallOfFameEntries', () => {
    const mockEntries: HallOfFameEntry[] = [
      { id: 1, user_id: actualUserId, entry_type: 'lesson', content: 'Learned JS', period: null, created_at: new Date().toISOString(), metadata: {tags: ['coding']} },
      { id: 2, user_id: actualUserId, entry_type: 'victory', content: 'Won a game', period: null, created_at: new Date().toISOString(), metadata: {tags: ['gaming']} }
    ] as HallOfFameEntry[];

    it('should retrieve entries for a user', async () => {
      mockQueryBuilder.order.mockResolvedValueOnce({ data: mockEntries, error: null });

      const result = await getHallOfFameEntries(actualUserId, {}, mockEnv);
      
      expect(mockSupabaseInstance.from).toHaveBeenCalledWith('hall_of_fame');
      expect(mockQueryBuilder.select).toHaveBeenCalledWith('*');
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', actualUserId);
      expect(mockQueryBuilder.order).toHaveBeenCalledWith('created_at', { ascending: false });
      expect(result).toEqual(mockEntries);
    });

    it('should apply type filter', async () => {
      mockQueryBuilder.order.mockResolvedValueOnce({ data: [mockEntries[0]], error: null });

      const filters: HallOfFameFilters = { type: 'lesson' };
      await getHallOfFameEntries(actualUserId, filters, mockEnv);

      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', actualUserId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('entry_type', 'lesson');
      expect(mockQueryBuilder.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });
    
    it('should apply period filter (day)', async () => {
        mockQueryBuilder.order.mockResolvedValueOnce({ data: [], error: null });
    
        const filters: HallOfFameFilters = { period: 'day' };
        await getHallOfFameEntries(actualUserId, filters, mockEnv);
    
        expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', actualUserId);
        expect(mockQueryBuilder.gte).toHaveBeenCalledWith('created_at', expect.any(String));
        expect(mockQueryBuilder.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });

    it('should apply tags filter', async () => {
        mockQueryBuilder.order.mockResolvedValueOnce({ data: [], error: null });

        const filters: HallOfFameFilters = { tags: ['coding'] };
        await getHallOfFameEntries(actualUserId, filters, mockEnv);

        expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', actualUserId);
        expect(mockQueryBuilder.contains).toHaveBeenCalledWith('metadata->tags', ['coding']);
        expect(mockQueryBuilder.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });

    it('should return empty array if Supabase fetch fails and throw SupabaseError', async () => {
      const dbError = { message: 'Fetch failed', code: 'DB702', details: '', hint: '' };
      mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: dbError });
      
      await expect(getHallOfFameEntries(actualUserId, {}, mockEnv)).rejects.toThrow(SupabaseError);
      // Call again to check message
      mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: dbError });
      await expect(getHallOfFameEntries(actualUserId, {}, mockEnv)).rejects.toThrow(dbError.message);
    });
  });

  describe('updateHallOfFameEntry', () => {
    const entryId = 1;
    const updates: HallOfFameEntryUpdate = { content: 'Updated content' };
    const updatedEntry: HallOfFameEntry = { id: entryId, user_id:actualUserId, entry_type: 'victory', period: null, content: 'Updated content', created_at: new Date().toISOString(), metadata: null };

    it('should update an entry and return it', async () => {
      mockQueryBuilder.single.mockResolvedValueOnce({ data: updatedEntry, error: null });

      const result = await updateHallOfFameEntry(entryId, updates, actualUserId, mockEnv);

      expect(mockSupabaseInstance.from).toHaveBeenCalledWith('hall_of_fame');
      expect(mockQueryBuilder.update).toHaveBeenCalledWith(updates);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('id', entryId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', actualUserId);
      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.single).toHaveBeenCalled();
      expect(result).toEqual(updatedEntry);
    });

    it('should throw SupabaseError if Supabase update fails', async () => {
      const dbError = { message: 'Update failed', code: 'DB703', details: '', hint: '' };
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });

      await expect(updateHallOfFameEntry(entryId, updates, actualUserId, mockEnv)).rejects.toThrow(SupabaseError);
      // Call again for message check
      mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: dbError });
      await expect(updateHallOfFameEntry(entryId, updates, actualUserId, mockEnv)).rejects.toThrow(dbError.message);
    });
  });

  describe('deleteHallOfFameEntry', () => {
    const entryId = 1;

    it('should delete an entry and call resolves.toBeUndefined', async () => {
      deleteSecondEqMock.mockResolvedValueOnce({ error: null }); 

      await expect(deleteHallOfFameEntry(entryId, actualUserId, mockEnv)).resolves.toBeUndefined();

      expect(mockSupabaseInstance.from).toHaveBeenCalledWith('hall_of_fame');
      expect(mockQueryBuilder.delete).toHaveBeenCalled();
      expect(deleteInitialEqMock).toHaveBeenCalledWith('id', entryId);
      expect(deleteSecondEqMock).toHaveBeenCalledWith('user_id', actualUserId);
    });

    it('should throw SupabaseError if Supabase delete fails', async () => {
      const dbError = { message: 'Delete failed', code: 'DB704', details: '', hint: '' };
      deleteSecondEqMock.mockResolvedValueOnce({ error: dbError });

      await expect(deleteHallOfFameEntry(entryId, actualUserId, mockEnv)).rejects.toThrow(SupabaseError);
      // Call again for message check
      deleteSecondEqMock.mockResolvedValueOnce({ error: dbError });
      await expect(deleteHallOfFameEntry(entryId, actualUserId, mockEnv)).rejects.toThrow(dbError.message);
    });
  });
});
