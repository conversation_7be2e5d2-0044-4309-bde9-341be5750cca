import { Env } from '../types/index'; // Centralized Env type
import { getSupabaseClient } from '../supabaseClient';
import { runLLM, Message, LLMResponse, getSystemPrompt } from '../workersAIClient'; // Import runLLM, Message, LLMResponse, getSystemPrompt
import { Database } from '../types/supabase';
import { User } from './userService'; // Assuming User type is exported from userService
import { log, LogLevel, LogContext } from '../utils/logger'; // Import logger
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError, AppError } from '../errors/AppError'; // Added AppError
import { getGameState, GameState } from './gameStateService'; // Import GameState and getGameState
import { getRecentMessages, MessageRow, getExtendedChatHistory, ChatHistoryMessage } from './messageService'; // Import getRecentMessages
import { generateTextEmbedding, querySimilarMemories, VectorMatch } from '../vectorizeClient'; // Import vectorizeClient functions
import { getKnowledgeDocuments, KnowledgeDocument } from './knowledgeService'; // Import knowledgeService functions
import { countPromptTokens, countTokens, LLMMessage } from '../utils/tokenizerUtil'; // For budgeting and final count
import { UserProfile, getOrCreateUser } from './userService';
import { escapeMarkdown } from '../utils/formatter'; // Import escapeMarkdown
// import { getVectorizeClient } from '../vectorizeClient'; // For when Vectorize interaction is needed

const INSIGHT_EXTRACTION_PROMPT = `
Your task is to act as a meticulous assistant helping a user gamify their life and reflect on their progress. 
Based on the provided CONVERSATION (last user message and AI response), identify and extract key insights, learnings, decisions, or actionable items. 
Format these as a JSON array of objects. Each object in the array should represent a single, distinct insight and adhere to the following structure:
{
  "text": "<The concise text of the insight, phrased clearly>",
  "importance": <A numerical score from 1 to 10, where 10 is most important>,
  "tags": ["<relevant_tag_1>", "<relevant_tag_2>"] // Include 1-3 relevant lowercase tags (e.g., "decision", "learning", "action_item", "reflection", "goal_setting")
}

If no significant insights, learnings, decisions, or actionable items are found in the conversation, return an empty JSON array [].
Do not include any explanations or conversational text outside of the JSON array itself. Only output the JSON.

CONVERSATION:
User: "{{USER_MESSAGE}}"
AI: "{{AI_RESPONSE}}"

EXTRACTED_INSIGHTS_JSON:
`;

// Re-using ActionResult from confirmationService or define locally/sharedly if preferred
interface ActionResult {
    success: boolean;
    message?: string; 
    error?: string; 
    data?: any; // Added data to match existing saveMemoryInsight return
}

export type HallOfFameEntry = Database['public']['Tables']['hall_of_fame']['Row']; // Added export
export type HallOfFameInsert = Database['public']['Tables']['hall_of_fame']['Insert'];

/**
 * Defines the payload structure for saving a memory insight.
 * This is typically used when a user confirms an insight or when an event
 * (like quest completion) generates a memory to be saved to the Hall of Fame.
 */
export interface SaveMemoryInsightPayload {
    text: string;
    title?: string;
    importance_score?: number;
    tags?: string[];
    related_quest_id?: number;
    source_type?: string; // e.g., 'user_reflection', 'quest_completion', 'ai_suggestion'
    metadata?: Record<string, any>; 
}

/**
 * Represents a single extracted insight, typically generated by an LLM
 * from a conversation or text. It includes the insight's text,
 * an importance score, and relevant tags.
 */
export interface Insight {
    text: string;
    importance_score: number;
    tags: string[];
}

const SERVICE_NAME = 'MemoryService';

const DEFAULT_LLM_MAX_TOKENS = 500000; // Revised to 0.5M based on new user requirement
const FINAL_PROMPT_SAFETY_BUFFER = 500; // Tokens to leave for AI's response and any minor miscounts
const APPROX_TOKEN_DIVISOR = 3.5; // For rough pre-calculation

interface FormattedContextParts {
  systemPrompt: string | null;
  gameStateContent: string | null;
  ragResultsContent: string | null;
  knowledgeDocsContent: string | null;
}

/**
 * Formats the game state for inclusion in the LLM prompt.
 */
function formatGameStateForPrompt(gameState: GameState | null, logContext: LogContext): string | null {
  if (!gameState) { log(LogLevel.DEBUG, 'No game state to format.', logContext); return null; }
  const content = [
    `XP: ${gameState.xp ?? 0}`,
    `Coins: ${gameState.coins ?? 0}`,
    `Energy: ${gameState.energy ?? 'N/A'}`,
  ];
  return `### Current Game State\n- ${content.join('\n- ')}`;
}

/**
 * Formats RAG results for inclusion in the LLM prompt.
 */
function formatRAGResultsForPrompt(
  ragResults: VectorMatch[] | null, 
  logContext: LogContext,
  maxSnippets: number = 3, // Default max snippets
  maxSnippetLength: number = 300 // Default max length per snippet
): string | null {
  if (!ragResults || ragResults.length === 0) {
    log(LogLevel.DEBUG, 'No RAG results to format.', logContext);
    return null;
  }
  const snippetsToFormat = ragResults.slice(0, maxSnippets);
  if (snippetsToFormat.length === 0) return null;

  const formatted = snippetsToFormat
    .map(mem => {
      let text = mem.metadata?.text || 'Memory entry.';
      if (text.length > maxSnippetLength) {
        text = text.substring(0, maxSnippetLength) + '...';
      }
      return `- ${escapeMarkdown(text)} (Score: ${mem.score.toFixed(2)})`;
    })
    .join('\n');
  return `### Relevant Past Memories (Recalled)\n${formatted}`;
}

// Knowledge documents formatting can be similar or more detailed
function formatKnowledgeDocsForPrompt(
  docs: KnowledgeDocument[] | null, 
  logContext: LogContext,
  maxDocs: number = 1, // Default max docs
  maxDocLength: number = 500 // Default max length per doc
): string | null {
    if (!docs || docs.length === 0) {
        log(LogLevel.DEBUG, 'No knowledge documents to format.', logContext);
        return null;
    }
    const docsToFormat = docs.slice(0, maxDocs);
    if (docsToFormat.length === 0) return null;

    const formattedDocs = docsToFormat.map(doc => {
        let content = doc.content || '';
        if (content.length > maxDocLength) {
            content = content.substring(0, maxDocLength) + '...';
        }
        return `#### ${escapeMarkdown(doc.title || 'Knowledge Document')}\n${escapeMarkdown(content)}`; // Changed to H4 for KD title
    }).join('\n\n---\n\n');
    return `### Supporting Knowledge\n${formattedDocs}`; // Added main header
}

/**
 * Extracts key insights from a conversation snippet using an LLM.
 *
 * @param userMessageText The text of the user's message.
 * @param aiResponseText The text of the AI's response.
 * @param env The Cloudflare Worker environment.
 * @param telegramUserId The Telegram ID of the user for logging context.
 * @returns Promise<Insight[]> An array of extracted insights.
 * @throws {AppError} if LLM call fails or JSON parsing fails.
 */
export async function extractInsights(
  userMessageText: string,
  aiResponseText: string,
  env: Env,
  telegramUserId?: number
): Promise<Insight[]> { // Returns Insight[] or throws AppError
  const functionName = 'extractInsights';
  const logContext: LogContext = { 
    service: SERVICE_NAME, 
    functionName, 
    telegramUserId,
    payload: { userMsgLen: userMessageText.length, aiRespLen: aiResponseText.length } 
  }; 
  log(LogLevel.INFO, 'Attempting to extract insights via LLM', logContext);

  if (!userMessageText && !aiResponseText) {
    log(LogLevel.WARN, 'Both user and AI messages are empty, cannot extract insights.', logContext);
    return []; // Return empty if nothing to process
  }

  const systemPrompt = `You are an AI assistant helping a user reflect on their thoughts and progress. Your task is to extract key insights, lessons learned, or actionable ideas from the provided user message and AI response. 
    Respond ONLY with a JSON array of insight objects. Each object should have the following structure: { "text": "The extracted insight...", "importance_score": <1-10 score>, "tags": ["relevant", "keywords"] }. 
    If no clear insights are found, respond with an empty JSON array []. Do not add any extra text, explanations, or markdown formatting outside the JSON. Be concise and focus on meaningful takeaways.`;
  
  const messagesForLLM: Message[] = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: `User message: "${userMessageText}"\n\nAI response: "${aiResponseText}"` }
  ];

  try {
    log(LogLevel.DEBUG, 'Running LLM for insight extraction', logContext);
    const llmOutput: LLMResponse | null = await runLLM(env, messagesForLLM, env.AI_MODEL_ID || '@cf/meta/llama-3-8b-instruct');

    if (!llmOutput?.response) {
      log(LogLevel.WARN, 'LLM response was empty or invalid for insight extraction', { ...logContext, payload: { ...logContext.payload, llmOutput }});
      throw new AppError(SERVICE_NAME, 'LLM did not return a response for insight extraction.', 500, true, { llmOutput });
    }
    
    const llmResponseString = llmOutput.response;
    log(LogLevel.DEBUG, 'LLM response received', { ...logContext, payload: { ...logContext.payload, llmResponseLength: llmResponseString.length }});
    
    let jsonString = llmResponseString.trim();
    if (jsonString.startsWith('```json')) jsonString = jsonString.substring(7).trim();
    if (jsonString.endsWith('```')) jsonString = jsonString.substring(0, jsonString.length - 3).trim();
    
    if (!jsonString) {
      log(LogLevel.WARN, 'LLM returned an empty string after stripping markdown', logContext );
      return [];
    }
      
    const parsedJson = JSON.parse(jsonString);

    if (Array.isArray(parsedJson)) {
      log(LogLevel.INFO, 'Insights (array) extracted successfully', { ...logContext, count: parsedJson.length });
      return parsedJson as Insight[];
    } else if (parsedJson && typeof parsedJson === 'object' && Array.isArray(parsedJson.insights)) {
      log(LogLevel.INFO, 'Insights (from .insights property) extracted successfully', { ...logContext, count: parsedJson.insights.length });
      return parsedJson.insights as Insight[];
    } else {
      log(LogLevel.WARN, 'Parsed JSON is not an array and has no .insights property.', { ...logContext, details: { parsedJson }});
      // Consider this an error if a non-empty JSON was returned but not in expected format
      throw new AppError(SERVICE_NAME, 'LLM returned JSON in unexpected format for insights.', 500, true, { parsedJson });
    }
  } catch (error: any) {
    if (error instanceof AppError || error instanceof SupabaseError) throw error;
    log(LogLevel.ERROR, 'Error during insight extraction LLM call or JSON parsing', { ...logContext, error: error.message, stack: error.stack });
    throw new AppError(SERVICE_NAME, `Failed to extract insights: ${error.message}`, 500, true, { originalError: error.message });
  }
}

/**
 * Saves a memory insight to the Hall of Fame in Supabase.
 * @param payload The insight data.
 * @param env The Cloudflare Worker environment.
 * @param actualUserId The actual ID of the user (from users table).
 * @returns Promise<ActionResult> (Kept for compatibility with confirmationService, consider refactoring to throw/return HallOfFameEntry).
 */
export async function saveMemoryInsight(
    payload: SaveMemoryInsightPayload,
    env: Env,
    actualUserId: number
): Promise<ActionResult> { // TODO: Refactor to return Promise<HallOfFameEntry> and throw SupabaseError
    const functionName = 'saveMemoryInsight';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName, 
        actualUserId,
        payload: { insightTextLength: payload.text?.length, title: payload.title }
    }; 
    log(LogLevel.INFO, 'Attempting to save memory insight to Hall of Fame', logContext);

    if (!actualUserId) {
        log(LogLevel.ERROR, 'actualUserId is required', { ...logContext });
        return { success: false, error: 'User actual ID is required.', message: 'User not identified for saving insight.' };
    }
    if (!payload.text) {
        log(LogLevel.WARN, 'Insight text is empty', logContext);
        return { success: false, error: 'Insight text cannot be empty.', message: 'Cannot save an empty insight.' };
    }

    const supabase = getSupabaseClient(env);
    
    const hallOfFameEntry: HallOfFameInsert = {
        user_id: actualUserId,
        content: payload.text,
        entry_type: payload.source_type || 'general_insight',
        metadata: {
            title: payload.title,
            importance: payload.importance_score,
            tags: payload.tags,
            related_quest_id: payload.related_quest_id,
            ...(payload.metadata || {}),
        }
    };

    try {
        log(LogLevel.DEBUG, 'Inserting Hall of Fame entry', { ...logContext, payload: { ...logContext.payload, entryDetails: hallOfFameEntry } });
        const insertResponse = await supabase
            .from('hall_of_fame')
            .insert(hallOfFameEntry)
            .select()
            .single(); // Expecting a single row back after insert with select

        const insertedData = processSupabaseResponse(insertResponse, { ...logContext, operation: 'saveInsightToHof.insert' });
        if (!insertedData) {
             log(LogLevel.ERROR, 'Insight insert into Hall of Fame returned no data unexpectedly', { ...logContext });
             return { success: false, error: 'Failed to save insight (no data returned after DB insert).' };
        }

        log(LogLevel.INFO, 'Memory insight saved successfully to Hall of Fame', { ...logContext, payload: { ...logContext.payload, insertedId: insertedData.id } });
        return { success: true, message: 'Insight saved to Hall of Fame!', data: insertedData };
    } catch (error: any) {
        if (!(error instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'Error inserting insight into Hall of Fame', { ...logContext, error: error.message, stack: error.stack });
        }
        const errorMsg = (error instanceof SupabaseError) ? error.message : 'Database error while saving insight.';
        return { success: false, error: errorMsg, message: `Failed to save insight: ${errorMsg}` };
    }
}

/**
 * Builds the comprehensive context for an AI prompt, including dynamic budgeting for chat history
 * and truncation strategies for oversized prompts.
 *
 * @param userTelegramId The Telegram ID of the user.
 * @param currentUserQuery The current user query or interaction context.
 * @param env The Cloudflare Worker environment.
 * @param isStartCommand Flag to indicate if this is for the /start command (minimal context).
 * @returns Promise<Message[]> An array of messages formatted for the LLM.
 * @throws {AppError} if any critical part of context building fails.
 */
export async function buildContext(
    userTelegramId: number,
    currentUserQuery: string,
    env: Env,
    isStartCommand: boolean = false
): Promise<LLMMessage[]> { 
    const functionName = 'buildContext';
    const baseLogContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId: userTelegramId, payload: { queryLength: currentUserQuery?.length, isStartCommand } };
    let user: UserProfile; try { user = await getOrCreateUser(userTelegramId, env); } catch (e:any) { log(LogLevel.ERROR, 'User fetch/create failed', baseLogContext); throw new AppError(SERVICE_NAME, 'User init failed',500,true,e); }
    const actualUserId = user.id;
    const logContext = { ...baseLogContext, actualUserId };

    const systemPrompt = await getSystemPrompt(env);
    if (!systemPrompt) { log(LogLevel.ERROR, 'System prompt missing', logContext); throw new AppError(SERVICE_NAME, 'System prompt load failed', 500, true);}

    let llmMessages: LLMMessage[] = [{ role: 'system', content: systemPrompt }];
    const modelIdForCounting = env.AI_MODEL_ID || 'gpt-4';
    const maxTokensForModel = parseInt(env.AI_MODEL_MAX_TOKENS || '', 10) || DEFAULT_LLM_MAX_TOKENS;
    let targetMaxPromptTokens = maxTokensForModel - FINAL_PROMPT_SAFETY_BUFFER;
    if (targetMaxPromptTokens < 0) targetMaxPromptTokens = 0;

    let gameStateContent: string | null = null;
    let ragResultsToFormat: VectorMatch[] | null = null;
    let knowledgeDocsToFormat: KnowledgeDocument[] | null = null;
    let extendedChatHistory: ChatHistoryMessage[] = [];

    if (isStartCommand) {
        log(LogLevel.INFO, '/start: minimal context', logContext);
        llmMessages.push({ role: 'user', content: currentUserQuery });
    } else {
        gameStateContent = formatGameStateForPrompt(await getGameState(actualUserId, env).catch(() => null), logContext);
        if (currentUserQuery && currentUserQuery.trim() !== '') {
            try {
                const queryVector = await generateTextEmbedding(env, currentUserQuery, userTelegramId);
                if (queryVector) ragResultsToFormat = await querySimilarMemories(env, queryVector, String(actualUserId), 5, userTelegramId); // Fetch initial top 5 RAG
            } catch (e) { log(LogLevel.WARN, 'RAG query failed', {...logContext, error:(e as Error).message});}
        }
        try { knowledgeDocsToFormat = await getKnowledgeDocuments(actualUserId, env); } 
        catch (e) { log(LogLevel.WARN, 'Knowledge doc fetch failed', {...logContext, error:(e as Error).message}); }
        
        // --- Dynamic Budget for History (using initial RAG/Knowledge format for estimation) ---
        const initialRagContentForBudget = formatRAGResultsForPrompt(ragResultsToFormat, logContext, 3, 300);
        const initialKnowledgeContentForBudget = formatKnowledgeDocsForPrompt(knowledgeDocsToFormat, logContext, 1, 500);
        const tempMessagesForBudgeting: LLMMessage[] = [{ role: 'system', content: systemPrompt }];
        const preamblePartsForBudgeting: string[] = [];
        if (gameStateContent) preamblePartsForBudgeting.push(gameStateContent);
        if (initialRagContentForBudget) preamblePartsForBudgeting.push(initialRagContentForBudget); 
        if (initialKnowledgeContentForBudget) preamblePartsForBudgeting.push(initialKnowledgeContentForBudget);
        let preambleStringForBudgeting = "";
        if (preamblePartsForBudgeting.length > 0) {
            preambleStringForBudgeting = `Contextual Information:\n\n${preamblePartsForBudgeting.join('\n\n---\n\n')}`;
            tempMessagesForBudgeting.push({ role: 'user', content: preambleStringForBudgeting });
        }
        tempMessagesForBudgeting.push({ role: 'user', content: currentUserQuery });
        let preliminaryTokens = 0;
        try { preliminaryTokens = await countPromptTokens(tempMessagesForBudgeting, modelIdForCounting, env); }
        catch (e) { preliminaryTokens = Math.ceil((systemPrompt.length + preambleStringForBudgeting.length + currentUserQuery.length) / APPROX_TOKEN_DIVISOR); /* log */ }
        let dynamicTokenTargetForHistory = targetMaxPromptTokens - preliminaryTokens;
        if (dynamicTokenTargetForHistory < 0) dynamicTokenTargetForHistory = 0;
        log(LogLevel.INFO, `Dynamic target for history: ${dynamicTokenTargetForHistory}`, logContext);
        if (dynamicTokenTargetForHistory > 100) {
            try { extendedChatHistory = await getExtendedChatHistory(actualUserId, env, { tokenTarget: dynamicTokenTargetForHistory }); }
            catch (error) { /* log */ }
        }
        
        // --- Initial Assembly (before truncation) ---
        // System prompt is llmMessages[0]
        const currentPreambleParts: string[] = [];
        if (gameStateContent) currentPreambleParts.push(gameStateContent);
        // For this assembly, use default RAG/Knowledge formatting counts initially
        const currentRagContent = formatRAGResultsForPrompt(ragResultsToFormat, logContext, 3, 300);
        if (currentRagContent) currentPreambleParts.push(currentRagContent);
        const currentKnowledgeContent = formatKnowledgeDocsForPrompt(knowledgeDocsToFormat, logContext, 1, 500);
        if (currentKnowledgeContent) currentPreambleParts.push(currentKnowledgeContent);

        if (currentPreambleParts.length > 0) {
            llmMessages.push({ role: 'user', content: `Contextual Information:\n\n${currentPreambleParts.join('\n\n---\n\n')}` });
        }
        extendedChatHistory.forEach(msg => {
            llmMessages.push({ role: msg.role as 'user' | 'assistant', content: msg.content });
        });
        llmMessages.push({ role: 'user', content: currentUserQuery });
    }

    // --- Accurate Token Count & Truncation Loop --- 
    let currentTotalTokens = 0;
    try { currentTotalTokens = await countPromptTokens(llmMessages, modelIdForCounting, env); }
    catch (e) { throw new AppError(SERVICE_NAME, "Failed to count initial prompt tokens for truncation", 500, true, {e});}
    log(LogLevel.INFO, `Pre-truncation prompt. Tokens: ${currentTotalTokens}, Target: ${targetMaxPromptTokens}`, logContext);

    // 1. Truncate Chat History (if not /start and over budget)
    if (!isStartCommand && currentTotalTokens > targetMaxPromptTokens) {
        log(LogLevel.DEBUG, "Attempting chat history truncation.", logContext);
        let systemMsgEndIdx = (llmMessages.length > 0 && llmMessages[0].role === 'system') ? 1 : 0;
        let preambleMsgPresent = llmMessages.length > systemMsgEndIdx && llmMessages[systemMsgEndIdx].role === 'user' && llmMessages[systemMsgEndIdx].content.startsWith('Contextual Information:');
        let historyStartIndex = preambleMsgPresent ? systemMsgEndIdx + 1 : systemMsgEndIdx;
        let queryMsgIndex = llmMessages.length - 1; // Current user query is always last
        
        if (queryMsgIndex > historyStartIndex) { 
            let actualHistoryMessages = llmMessages.slice(historyStartIndex, queryMsgIndex);
            while (actualHistoryMessages.length > 0 && currentTotalTokens > targetMaxPromptTokens) {
                actualHistoryMessages.shift(); // Remove oldest
                const tempPrompt = [
                    ...llmMessages.slice(0, historyStartIndex), 
                    ...actualHistoryMessages, 
                    llmMessages[queryMsgIndex]
                ];
                currentTotalTokens = await countPromptTokens(tempPrompt, modelIdForCounting, env);
                log(LogLevel.DEBUG, `History truncated. Tokens: ${currentTotalTokens}, History msgs: ${actualHistoryMessages.length}`, logContext);
            }
            llmMessages = [
                ...llmMessages.slice(0, historyStartIndex),
                ...actualHistoryMessages,
                llmMessages[queryMsgIndex]
            ];
        } else { log(LogLevel.DEBUG, "No distinct chat history block to truncate.", logContext);}
        log(LogLevel.INFO, `After history truncation. Tokens: ${currentTotalTokens}`, logContext);
    }

    // 2. Adjust RAG/Knowledge in Preamble if still over budget (and not /start)
    if (!isStartCommand && currentTotalTokens > targetMaxPromptTokens) {
        log(LogLevel.INFO, `Still over budget (${currentTotalTokens} > ${targetMaxPromptTokens}). Attempting RAG/Knowledge truncation.`, logContext);
        let preambleMessageIdx = llmMessages.findIndex(m => m.role === 'user' && m.content.startsWith('Contextual Information:'));

        if (preambleMessageIdx !== -1) {
            let currentDynamicRagCount = 3;     let minRagCount = 0;
            let currentDynamicRagLength = 300;  let minRagLength = 50; let ragLengthStep = 50;
            let currentDynamicKnowledgeCount = 1; let minKnowledgeCount = 0;
            let currentDynamicKnowledgeLength = 500; let minKnowledgeLength = 100; let knowledgeLengthStep = 100;
            let preambleWasModifiedInLoop = true; // Force first iteration

            while (currentTotalTokens > targetMaxPromptTokens && preambleWasModifiedInLoop) {
                preambleWasModifiedInLoop = false; // Reset for this iteration
                if (currentDynamicRagCount > minRagCount) { currentDynamicRagCount--; preambleWasModifiedInLoop = true; }
                else if (currentDynamicRagLength > minRagLength) { currentDynamicRagLength -= ragLengthStep; preambleWasModifiedInLoop = true; }
                else if (currentDynamicKnowledgeCount > minKnowledgeCount && knowledgeDocsToFormat && knowledgeDocsToFormat.length > 1) { currentDynamicKnowledgeCount--; preambleWasModifiedInLoop = true; }
                else if (currentDynamicKnowledgeLength > minKnowledgeLength) { currentDynamicKnowledgeLength -= knowledgeLengthStep; preambleWasModifiedInLoop = true; }
                
                if (preambleWasModifiedInLoop) {
                    const newRagContent = formatRAGResultsForPrompt(ragResultsToFormat, logContext, currentDynamicRagCount, currentDynamicRagLength);
                    const newKnowledgeContent = formatKnowledgeDocsForPrompt(knowledgeDocsToFormat, logContext, currentDynamicKnowledgeCount, currentDynamicKnowledgeLength);
                    const newPreambleParts: string[] = [];
                    if (gameStateContent) newPreambleParts.push(gameStateContent);
                    if (newRagContent) newPreambleParts.push(newRagContent);
                    if (newKnowledgeContent) newPreambleParts.push(newKnowledgeContent);
                    
                    let newPreambleString = "";
                    if (newPreambleParts.length > 0) newPreambleString = `Contextual Information:\n\n${newPreambleParts.join('\n\n---\n\n')}`;
                    
                    llmMessages[preambleMessageIdx].content = newPreambleString; // Update existing preamble message
                    if (!newPreambleString && preambleMessageIdx !== -1) { // If preamble becomes empty, remove it
                        llmMessages.splice(preambleMessageIdx, 1);
                        preambleMessageIdx = -1; // Mark as removed
                    }
                    currentTotalTokens = await countPromptTokens(llmMessages, modelIdForCounting, env);
                    log(LogLevel.DEBUG, `Preamble truncated. RAG ${currentDynamicRagCount}@${currentDynamicRagLength}, KD ${currentDynamicKnowledgeCount}@${currentDynamicKnowledgeLength}. Tokens: ${currentTotalTokens}`, logContext);
                } else { break; /* No more RAG/Knowledge truncation options */ }
            }
        }
        if (currentTotalTokens > targetMaxPromptTokens) {
            log(LogLevel.WARN, `Prompt still exceeds target after RAG/Knowledge attempts (${currentTotalTokens}).`, logContext);
        }
        log(LogLevel.INFO, `After RAG/Knowledge truncation. Tokens: ${currentTotalTokens}`, logContext);
    }

    if (currentTotalTokens > maxTokensForModel) {
        log(LogLevel.ERROR, `CRITICAL FINAL CHECK: Prompt still > absolute model max tokens (${currentTotalTokens} > ${maxTokensForModel}).`, logContext);
    }
    log(LogLevel.INFO, `Final prompt ready. Tokens: ${currentTotalTokens}, Messages: ${llmMessages.length}`, logContext);
    return llmMessages;
}

