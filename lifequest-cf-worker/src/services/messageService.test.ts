import { vi, describe, it, expect, beforeEach, afterEach, Mock } from 'vitest';
import { getSupabaseClient } from '../supabaseClient';
import * as messageServiceActual from './messageService';
import { MessageInsert } from './messageService';
import { Env } from '../types';
import { SupabaseError } from '../errors/AppError';
import { log, LogLevel } from '../utils/logger';

vi.mock('../supabaseClient', () => ({
    getSupabaseClient: vi.fn(),
}));

vi.mock('../utils/logger', async () => {
  const actual = await vi.importActual('../utils/logger');
  return {
    ...actual,
    log: vi.fn(),
  };
});

const mockEnv = {} as Env;
const testActualUserId = 1;

describe('Message Service', () => {
    let mockSupabase: any;
    let mockQueryBuilder: any;

    beforeEach(() => {
        vi.clearAllMocks();
        (log as Mock).mockClear();

        mockQueryBuilder = {
            insert: vi.fn(), // Will be set per test to mockResolvedValue or mockRejectedValue
        };
        mockSupabase = {
            from: vi.fn(() => mockQueryBuilder),
        };
        (getSupabaseClient as Mock).mockReturnValue(mockSupabase);
    });

    describe('logMessage', () => {
        it('should log a message successfully', async () => {
            mockQueryBuilder.insert.mockResolvedValueOnce({ error: null, data: null }); // Insert success
            
            await expect(messageServiceActual.logMessage(testActualUserId, 'user', 'Test message', mockEnv))
                .resolves.toBeUndefined(); // Returns void on success
            
            expect(mockSupabase.from).toHaveBeenCalledWith('messages');
            expect(mockQueryBuilder.insert).toHaveBeenCalledWith(
                expect.objectContaining({
                    user_id: testActualUserId,
                    role: 'user',
                    content: 'Test message',
                })
            );
            expect(log).toHaveBeenCalledWith(LogLevel.INFO, 'Message logged successfully to DB', expect.any(Object));
        });

        it('should throw SupabaseError if actualUserId is not provided', async () => {
            await expect(messageServiceActual.logMessage(0, 'user', 'Test message', mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(messageServiceActual.logMessage(0, 'user', 'Test message', mockEnv))
                .rejects.toThrow('actualUserId is required to log a message');
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'actualUserId is required to log a message', expect.any(Object));
        });

        it('should throw SupabaseError if content is not provided', async () => {
            await expect(messageServiceActual.logMessage(testActualUserId, 'user', '', mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(messageServiceActual.logMessage(testActualUserId, 'user', '', mockEnv))
                .rejects.toThrow('content is required to log a message');
            expect(log).toHaveBeenCalledWith(LogLevel.WARN, 'content is required to log a message', expect.any(Object));
        });

        it('should throw SupabaseError if Supabase insert fails', async () => {
            const dbError = { message: 'Insert failed for message', code: 'DB201', details: '', hint: '' };
            mockQueryBuilder.insert.mockResolvedValueOnce({ error: dbError, data: null });
            mockQueryBuilder.insert.mockResolvedValueOnce({ error: dbError, data: null });

            await expect(messageServiceActual.logMessage(testActualUserId, 'user', 'Test message', mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(messageServiceActual.logMessage(testActualUserId, 'user', 'Test message', mockEnv))
                .rejects.toThrow(dbError.message);
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, expect.stringContaining(dbError.message), expect.any(Object));
        });

        it('should throw SupabaseError on unexpected error during insert', async () => {
            const unexpectedError = new Error('Network issue during message insert');
            mockQueryBuilder.insert.mockRejectedValueOnce(unexpectedError);
            mockQueryBuilder.insert.mockRejectedValueOnce(unexpectedError);

            await expect(messageServiceActual.logMessage(testActualUserId, 'user', 'Test message', mockEnv))
                .rejects.toThrow(SupabaseError);
            await expect(messageServiceActual.logMessage(testActualUserId, 'user', 'Test message', mockEnv))
                .rejects.toThrow(unexpectedError.message);
            expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'logMessage: Unexpected error during DB operation', expect.any(Object));
        });
    });
}); 