import { Env } from '../types';
import { getSupabaseClient } from '../supabaseClient';
import { Database, Json } from '../types/supabase'; // Import Json type
import { log, LogLevel, LogContext } from '../utils/logger'; // Import logger
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';
// import { Database } from '../types/supabase'; // Not strictly needed if we use generated table types

// Corresponds to the structure of the 'game_state' table in Supabase
// and Database['public']['Tables']['game_state']['Row']
export type GameState = Database['public']['Tables']['game_state']['Row'];
export type GameStateInsert = Database['public']['Tables']['game_state']['Insert'];
export type GameStateUpdate = Database['public']['Tables']['game_state']['Update']; // This is the correct Supabase update type

/**
 * Defines the operations that can be performed on a user's game state.
 * Used by the `updateGameState` function to specify changes like adding XP,
 * adding coins, setting an absolute energy level, or updating metadata.
 */
export interface GameStateOperations {
    xp_to_add?: number;
    coins_to_add?: number;
    energy_to_set?: number; // Absolute value
    metadata_to_update?: Json; 
}

// Potentially define a type for updates to make it more structured
// export interface GameStateUpdates {
//   xp_to_add?: number;
//   coins_to_add?: number;
//   energy_to_set?: number;
//   // ... other updatable fields
// }

// Basic structure. Functions will be added in subsequent subtasks.
console.log('gameStateService.ts initialized');

// Re-using ActionResult from confirmationService or define locally/sharedly
interface ActionResult {
    success: boolean;
    message?: string; 
    error?: string; 
}

/**
 * Defines the payload structure for awarding XP to a user.
 * Includes the amount of XP, an optional reason for the award,
 * and an optional related quest ID.
 */
export interface AwardXpPayload {
    amount: number;
    reason?: string;
    related_quest_id?: number;
}

interface ServiceResult<T> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}

const SERVICE_NAME = 'GameStateService';

/**
 * Retrieves the game state for a given user.
 * @param actualUserId The user's actual ID from the 'users' table.
 * @param env The Cloudflare Worker environment.
 * @returns A GameState object or null if not found or error.
 */
export async function getGameState(actualUserId: number, env: Env): Promise<GameState | null> {
    const functionName = 'getGameState';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId } }; 
    log(LogLevel.INFO, 'Attempting to get game state for user', logContext);

    if (!actualUserId) {
        log(LogLevel.ERROR, 'actualUserId is required to get game state', { ...logContext, error: 'actualUserId not provided' });
        // Consider throwing an AppError here for invalid input
        return null;
    }
    const supabase = getSupabaseClient(env);
    try {
        const response = await supabase
            .from('game_state')
            .select('*')
            .eq('user_id', actualUserId)
            .maybeSingle();

        const data = processSupabaseResponse(response, { ...logContext, operation: 'fetchGameState' });

        if (!data) {
            log(LogLevel.INFO, 'No game state found for user, can be initialized', logContext);
            return null; 
        }
        log(LogLevel.INFO, 'Game state fetched successfully', { ...logContext, payload: { ...logContext.payload, hasData: !!data } });
        return data;
    } catch (error) {
        // If processSupabaseResponse threw a SupabaseError, it should be re-thrown.
        // Check if the error message starts with "Supabase Error:" as a more direct check for our handled errors.
        if (error && typeof (error as Error).message === 'string' && (error as Error).message.startsWith('Supabase Error:')) {
            throw error;
        }
        // If it's a different kind of unexpected error, log it and wrap it.
        log(LogLevel.ERROR, 'Unexpected error fetching game state', { ...logContext, error: (error as Error).message, stack: (error as Error).stack });
        throw new SupabaseError('Unexpected error fetching game state', { originalError: (error as Error).message }, error as Error);
    }
}

/**
 * Pure helper function to calculate new XP.
 */
export function addXP(currentState: GameState, amount: number): GameStateUpdate {
    return {
        xp: (currentState.xp || 0) + amount,
        last_updated: new Date().toISOString(),
    };
}

/**
 * Pure helper function to calculate new coins.
 */
export function addCoins(currentState: GameState, amount: number): GameStateUpdate {
    return {
        coins: (currentState.coins || 0) + amount,
        last_updated: new Date().toISOString(),
    };
}

/**
 * Pure helper function to set energy level.
 */
export function setEnergy(currentState: GameState, level: number): GameStateUpdate {
    return {
        energy: level,
        last_updated: new Date().toISOString(),
    };
}

/**
 * Updates the game state for a user. Can create if not exists.
 * @param actualUserId The user's actual ID from the 'users' table.
 * @param operations An object specifying what parts of the game state to update.
 * @param env The Cloudflare Worker environment.
 * @returns The updated GameState.
 * @throws {SupabaseError} if the database operation fails.
 * @throws {AppError} if actualUserId is not provided.
 */
export async function updateGameState(
    actualUserId: number, 
    operations: GameStateOperations,
    env: Env
): Promise<GameState> {
    const functionName = 'updateGameState';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, operations } }; 
    log(LogLevel.INFO, 'Attempting to update game state', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to update game state';
        log(LogLevel.ERROR, errorMsg, { ...logContext, error: 'actualUserId not provided' });
        throw new SupabaseError(errorMsg, { ...logContext, error: 'actualUserId not provided' });
    }

    const initialCurrentState = await getGameState(actualUserId, env);

    let newValues: GameStateUpdate = { user_id: actualUserId }; 

    const effectiveCurrentState: GameState = initialCurrentState ?? {
        id: -1, 
        user_id: actualUserId,
        xp: 0,
        coins: 0,
        energy: null,
        last_updated: new Date().toISOString(),
        metadata: null
    };

    if (!initialCurrentState) {
        log(LogLevel.INFO, 'No existing game state for user, initializing newValues for upsert', logContext);
        newValues.xp = 0;
        newValues.coins = 0;
        newValues.energy = null; 
    }
    
    if (Object.keys(operations).length === 0 && initialCurrentState) {
        // If no operations, but state exists, copy all fields and update timestamp
        newValues = { 
            ...effectiveCurrentState, // Copy all fields from current state
            user_id: actualUserId,    // Ensure user_id is correctly set
            id: undefined,            // Do not try to upsert the primary key 'id' from the read record
            last_updated: new Date().toISOString() 
        };
        // Remove created_at if it was spread from effectiveCurrentState and is not part of GameStateUpdate
        delete (newValues as any).created_at;
    } else {
        // Apply specific operations if there are any
        if (operations.xp_to_add !== undefined) {
            const xpUpdate = addXP(effectiveCurrentState, operations.xp_to_add);
            newValues.xp = xpUpdate.xp;
            newValues.last_updated = xpUpdate.last_updated;
        }
        if (operations.coins_to_add !== undefined) {
            const coinsUpdate = addCoins(effectiveCurrentState, operations.coins_to_add);
            newValues.coins = coinsUpdate.coins;
            newValues.last_updated = coinsUpdate.last_updated; 
        }
        if (operations.energy_to_set !== undefined) {
            const energyUpdate = setEnergy(effectiveCurrentState, operations.energy_to_set);
            newValues.energy = energyUpdate.energy;
            newValues.last_updated = energyUpdate.last_updated; 
        }
        if (operations.metadata_to_update) {
            newValues.metadata = effectiveCurrentState.metadata 
                ? { ...(effectiveCurrentState.metadata as object || {}), ...(operations.metadata_to_update as object) } 
                : operations.metadata_to_update;
            newValues.last_updated = new Date().toISOString();
        }

        // Ensure last_updated is always set if any update occurred
        if (Object.keys(operations).length > 0 && !newValues.last_updated) {
            newValues.last_updated = new Date().toISOString();
        }
    }
    
    // For a truly new record, ensure defaults if not set by operations
    if (!initialCurrentState) {
        if (newValues.xp === undefined) newValues.xp = 0;
        if (newValues.coins === undefined) newValues.coins = 0;
        if (newValues.last_updated === undefined) newValues.last_updated = new Date().toISOString();
    }

    const supabase = getSupabaseClient(env);

    try {
        const userCheckResponse = await supabase
            .from('users')
            .select('id', { count: 'exact', head: true })
            .eq('id', actualUserId);
        if (userCheckResponse.error) {
            log(LogLevel.WARN, 'Pre-upsert user check failed (query error)', { ...logContext, error: userCheckResponse.error.message });
        }
        const userExists = (userCheckResponse.count ?? 0) > 0;
        if (!userExists) {
            throw new SupabaseError(`User with id ${actualUserId} not found before game_state upsert.`, logContext);
        }
    } catch (checkError: any) {
        throw new SupabaseError(`Failed pre-upsert user check for id ${actualUserId}.`, { originalError: checkError.message }, checkError);
    }

    log(LogLevel.DEBUG, 'Upserting game state with values', { ...logContext, payload: { ...logContext.payload, newValues } });
    const response = await supabase
        .from('game_state')
        .upsert(newValues, { onConflict: 'user_id' })
        .select()
        .single();

    const upsertedData = processSupabaseResponse(response, { ...logContext, operation: 'upsertGameState' });
    
    if (!upsertedData) {
        log(LogLevel.ERROR, 'Upsert operation did not return data as expected, though no explicit Supabase error was thrown by client.', { ...logContext });
        throw new SupabaseError('Upsert succeeded but returned no data unexpectedly.', { ...logContext, operation: 'upsertGameState' });
    }

    log(LogLevel.INFO, 'Game state updated successfully', { ...logContext, payload: { ...logContext.payload, upsertedId: upsertedData?.id } });
    return upsertedData;
}

/**
 * Awards XP to a user and updates their game state.
 * @param payload The XP award details.
 * @param env The Cloudflare Worker environment.
 * @param actualUserId The actual ID of the user from the 'users' table.
 * @returns An object indicating success and a user-facing message.
 */
export async function awardXp(
    payload: AwardXpPayload, 
    env: Env, 
    actualUserId: number
): Promise<ActionResult> {
    const functionName = 'awardXp';
    const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { actualUserId, ...payload } }; 
    log(LogLevel.INFO, 'Attempting to award XP', logContext);

    if (!actualUserId) {
        log(LogLevel.ERROR, 'actualUserId is required to award XP', { ...logContext, error: 'actualUserId not provided' });
        return { success: false, error: 'User ID is required to award XP.', message: 'Error: User not identified.' };
    }
    if (payload.amount <= 0) {
        log(LogLevel.WARN, 'XP amount must be positive', { ...logContext, payload: { ...logContext.payload, amount: payload.amount } });
        return { success: false, error: 'XP amount must be positive.', message: 'XP amount must be greater than 0.' };
    }

    const operations: GameStateOperations = { xp_to_add: payload.amount };
    if (payload.reason || payload.related_quest_id) {
        operations.metadata_to_update = { 
            last_xp_award: {
                reason: payload.reason,
                related_quest_id: payload.related_quest_id,
                timestamp: new Date().toISOString()
            }
        };
    }

    try {
        const updatedState = await updateGameState(actualUserId, operations, env);
        log(LogLevel.INFO, 'XP awarded successfully', { ...logContext, payload: { ...logContext.payload, newXp: updatedState.xp } });
        return { success: true, message: `${payload.amount} XP awarded for "${payload.reason || 'your efforts'}"! Current XP: ${updatedState.xp}.` };
    } catch (error) {
        // Logged by handleSupabaseError if it's a SupabaseError from updateGameState
        // Or by updateGameState itself for input errors.
        // Here we just construct the ActionResult.
        const errorMessage = (error instanceof Error) ? error.message : 'Unknown error awarding XP.';
        if (!(error instanceof SupabaseError)) { // If not already logged by our utility
             log(LogLevel.ERROR, 'Failed to award XP due to game state update failure', { ...logContext, error: errorMessage, stack: (error as Error).stack });
        }
        return { success: false, error: `Failed to award XP: ${errorMessage}`, message: `Could not award XP: ${errorMessage}` };
    }
}
