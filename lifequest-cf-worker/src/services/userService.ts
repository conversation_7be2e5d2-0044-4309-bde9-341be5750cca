import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase'; // Adjust path as necessary
import { Env } from '../types'; // Adjust path as necessary
import { getSupabaseClient } from '../supabaseClient';
import { log, LogLevel, LogContext } from '../utils/logger'; // Import logger
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';

export type User = Database['public']['Tables']['users']['Row'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];

const SERVICE_NAME = 'UserService';

/**
 * Checks if a user with the given Telegram ID exists in the database.
 *
 * @param telegramId The Telegram ID of the user.
 * @param env The environment object.
 * @returns True if the user exists, false otherwise.
 * @throws {SupabaseError} if there's an issue communicating with Supabase.
 */
export async function checkUserExists(telegramId: number, env: Env): Promise<boolean> {
  const functionName = 'checkUserExists';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId: telegramId };
  log(LogLevel.INFO, 'Checking if user exists', logContext);

  if (!telegramId) {
    const errorMsg = 'telegramId is required to check user existence';
    log(LogLevel.ERROR, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext );
  }

  const supabase = getSupabaseClient(env);
  try {
    const response = await supabase
        .from('users')
        .select('id', { count: 'exact', head: true }) // More efficient: only count, don't retrieve data
        .eq('telegram_id', telegramId);
        // No .maybeSingle() here, rely on processSupabaseResponse for error handling
        // and check response.count for existence

    // For a count query, response.error should be checked by processSupabaseResponse.
    // processSupabaseResponse will throw if response.error is set.
    // If no error, response.count will tell us if the user exists.
    // The data part of the response for a head:true query is null, so we can't pass it to processSupabaseResponse directly
    // if it expects data not to be null without an error.
    // Let's handle the error part first.
    if (response.error) {
        handleSupabaseError(response.error, { ...logContext, operation: 'checkUserExists.count' });
    }

    const exists = (response.count ?? 0) > 0;
    log(LogLevel.INFO, `User exists check result: ${exists}`, logContext);
    return exists;
  } catch (e: any) {
    if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'checkUserExists: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected error in checkUserExists', { originalError: e.message }, e);
    }
    throw e; 
  }
}

/**
 * Represents the full profile of a user, extending the base User type.
 * In this context, it's an alias for the Supabase-generated User row type.
 */
export interface UserProfile extends User {}

/**
 * Retrieves an existing user by Telegram ID or creates a new one if not found.
 *
 * @param telegramId The Telegram ID of the user.
 * @param env The environment object.
 * @param userData Optional: additional data from Telegram for new user creation (currently not used for UserInsert).
 * @returns The user's profile data from Supabase.
 * @throws {SupabaseError} if there's an issue communicating with Supabase or creating the user.
 */
export async function getOrCreateUser(
  telegramId: number, 
  env: Env,
  userData?: { username?: string | null; first_name?: string | null; last_name?: string | null } // Kept for signature, but not used
): Promise<UserProfile> {
  const functionName = 'getOrCreateUser';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId: telegramId, payload: { telegramId } }; 
  log(LogLevel.INFO, 'Attempting to get or create user', logContext);

  if (!telegramId) {
    const errorMsg = 'telegramId is required to get or create user';
    log(LogLevel.ERROR, errorMsg, { ...logContext });
    throw new SupabaseError(errorMsg, logContext);
  }

  const supabase = getSupabaseClient(env);

  try {
    // Try to fetch existing user by telegram_id
    const fetchResponse = await supabase
        .from('users')
        .select('*')
        .eq('telegram_id', telegramId)
        .maybeSingle<UserProfile>();

    // processSupabaseResponse handles error, and returns null if data is null (user not found by maybeSingle)
    const existingUser = processSupabaseResponse(fetchResponse, { ...logContext, operation: 'getOrCreateUser.fetch' });

    if (existingUser) {
        log(LogLevel.INFO, 'Existing user found', { ...logContext, payload: { ...logContext.payload, userId: existingUser.id } });
        return existingUser;
    }
  } catch (error) {
    // If processSupabaseResponse threw a SupabaseError, rethrow it. Otherwise, wrap.
    if (error instanceof SupabaseError) throw error;
    log(LogLevel.ERROR, 'getOrCreateUser: Unexpected error during fetch attempt', { ...logContext, error: (error as Error).message });
    throw new SupabaseError('Unexpected error fetching user in getOrCreateUser', { originalError: (error as Error).message }, error as Error);
  }

  // User not found, create new user
  log(LogLevel.INFO, 'Existing user not found, creating new user', logContext);
  const newUserInsert: UserInsert = {
    telegram_id: telegramId,
  };

  try {
    const { error: insertError } = await supabase
        .from('users')
        .insert(newUserInsert);

    if (insertError) {
        handleSupabaseError(insertError, { ...logContext, operation: 'getOrCreateUser.insertAttempt' }); // This will throw
    }

    // If insert was successful, re-fetch the newly created user to ensure we get the complete record with DB-generated fields
    log(LogLevel.DEBUG, 'Insert attempt for new user seemed successful, re-fetching...', logContext);
    const fetchNewUserResponse = await supabase
        .from('users')
        .select('*')
        .eq('telegram_id', telegramId)
        .single<UserProfile>();
    
    const newUser = processSupabaseResponse(fetchNewUserResponse, { ...logContext, operation: 'getOrCreateUser.fetchAfterInsert' });
    if (!newUser) {
        // This case implies the insert seemed to work (no error thrown by handleSupabaseError),
        // but the subsequent select failed to find the user, which is highly problematic.
        log(LogLevel.ERROR, 'Failed to fetch user immediately after successful-looking insert', { ...logContext });
        throw new SupabaseError('Could not retrieve user record immediately after creation attempt.', { ...logContext, operation: 'getOrCreateUser.fetchAfterInsert' });
    }

    log(LogLevel.INFO, 'New user created and fetched successfully', { ...logContext, payload: { ...logContext.payload, userId: newUser.id } });
    return newUser;
  } catch (e: any) {
     if (!(e instanceof SupabaseError)) {
        log(LogLevel.ERROR, 'getOrCreateUser: Unexpected error during insert operation', { ...logContext, error: e.message, stack: e.stack });
        throw new SupabaseError(e.message || 'Unexpected error inserting user in getOrCreateUser', { originalError: e.message }, e);
    }
    throw e;
  }
} 