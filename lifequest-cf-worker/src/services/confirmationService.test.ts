import { vi, describe, it, expect, beforeEach, Mock } from 'vitest';
import { getSupabaseClient } from '../supabaseClient';
import { processConfirmation, PendingConfirmation } from './confirmationService';
import * as memoryService from './memoryService';
import * as gameStateService from './gameStateService';
import { Env } from '../types';
import { log, LogLevel } from '../utils/logger';
import { CallbackQuery } from '@grammyjs/types';

vi.mock('../supabaseClient', () => ({ getSupabaseClient: vi.fn() }));
vi.mock('../utils/logger', async () => ({ ...(await vi.importActual('../utils/logger')), log: vi.fn() }));
vi.mock('./memoryService', () => ({ saveMemoryInsight: vi.fn() }));
vi.mock('./gameStateService', () => ({ awardXp: vi.fn() }));

const mockEnv = {} as Env;
const testConfirmationUUID = 'test-uuid-123';
const testTelegramUserId = 12345;
const testUserDbId = 789;
const testOriginalMessageId = 54321;

const mockCallbackQuery = { from: { id: testTelegramUserId } } as CallbackQuery;

const createMockPendingConfirmation = (overrides: Partial<PendingConfirmation> = {}): PendingConfirmation => ({
    id: testConfirmationUUID,
    user_telegram_id: testTelegramUserId,
    action_type: 'TEST_ACTION',
    action_payload: { test: 'payload' },
    original_message_id: testOriginalMessageId,
    created_at: new Date().toISOString(),
    expires_at: new Date(Date.now() + 3600 * 1000).toISOString(),
    ...overrides,
});

describe('Confirmation Service', () => {
    let mockSupabase: any;

    beforeEach(() => {
        vi.clearAllMocks();
        mockSupabase = { from: vi.fn() };
        (getSupabaseClient as Mock).mockReturnValue(mockSupabase);
    });

    describe('processConfirmation', () => {
        it('should return error if confirmation not found', async () => {
            const dbError = { message: 'Not found', code: 'PGRST116' };
            const mockPendingConfirmationsBuilder = {
                select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(),
                single: vi.fn().mockResolvedValueOnce({ data: null, error: dbError })
            };
            mockSupabase.from.mockImplementationOnce((tableName: string) =>
                tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilder : undefined
            );
            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(false);
            expect(result.message).toContain('Could not find this confirmation');
        });

        it('should return error if user ID mismatches', async () => {
            const confirmation = createMockPendingConfirmation({ user_telegram_id: 99999 });
            const mockPendingConfirmationsBuilderFetch = {
                select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(),
                single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null })
            };
            const mockPendingConfirmationsBuilderDelete = {
                delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null })
            };
            mockSupabase.from.mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined);
            mockSupabase.from.mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(false);
            expect(result.message).toContain('This confirmation is not for you');
        });

        it('should return error if confirmation expired', async () => {
            const confirmation = createMockPendingConfirmation({ expires_at: new Date(Date.now() - 1000).toISOString() });
            const mockPendingConfirmationsBuilderFetch = {
                select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(),
                single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null })
            };
            const mockPendingConfirmationsBuilderDelete = {
                delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null })
            };
            mockSupabase.from.mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined);
            mockSupabase.from.mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(false);
            expect(result.message).toContain('This confirmation has expired');
        });

        it('should process declined (choice=\'n\') confirmation successfully', async () => {
            const confirmation = createMockPendingConfirmation();
            const mockPendingConfirmationsBuilderFetch = {
                select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(),
                single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null })
            };
            const mockPendingConfirmationsBuilderDelete = {
                delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null })
            };
            mockSupabase.from.mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined);
            mockSupabase.from.mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            const result = await processConfirmation(testConfirmationUUID, 'n', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(true);
            expect(result.message).toBe('Action cancelled as per your choice.');
        });

        it('should handle SAVE_MEMORY_INSIGHT action successfully when accepted', async () => {
            const payload = { text: 'Insight', title: 'Insight Title' };
            const confirmation = createMockPendingConfirmation({ action_type: 'SAVE_MEMORY_INSIGHT', action_payload: payload });

            const mockPendingConfirmationsBuilderFetch = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null }) };
            const mockUsersBuilder = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: { id: testUserDbId }, error: null }) };
            const mockPendingConfirmationsBuilderDelete = { delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null }) };

            mockSupabase.from
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUsersBuilder : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            (memoryService.saveMemoryInsight as Mock).mockResolvedValueOnce({ success: true, message: 'Insight saved by mock!' });

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(true);
            expect(result.message).toBe('Insight saved by mock!');
            expect(memoryService.saveMemoryInsight).toHaveBeenCalledWith(payload, mockEnv, testUserDbId);
        });

        it('should handle AWARD_XP action successfully when accepted', async () => {
            const payload = { amount: 100, reason: 'Test XP' };
            const confirmation = createMockPendingConfirmation({ action_type: 'AWARD_XP', action_payload: payload });

            const mockPendingConfirmationsBuilderFetch = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null }) };
            const mockUsersBuilder = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: { id: testUserDbId }, error: null }) };
            const mockPendingConfirmationsBuilderDelete = { delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null }) };

            mockSupabase.from
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUsersBuilder : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            (gameStateService.awardXp as Mock).mockResolvedValueOnce({ success: true, message: 'XP awarded by mock!' });

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(true);
            expect(result.message).toBe('XP awarded by mock!');
            expect(gameStateService.awardXp).toHaveBeenCalledWith(payload, mockEnv, testUserDbId);
        });

        it('should handle failure in action handler (e.g., saveMemoryInsight fails)', async () => {
            const payload = { text: 'Insight', title: 'Insight Title' };
            const confirmation = createMockPendingConfirmation({ action_type: 'SAVE_MEMORY_INSIGHT', action_payload: payload });

            const mockPendingConfirmationsBuilderFetch = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null }) };
            const mockUsersBuilder = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: { id: testUserDbId }, error: null }) };
            const mockPendingConfirmationsBuilderDelete = { delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null }) };

            mockSupabase.from
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUsersBuilder : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            (memoryService.saveMemoryInsight as Mock).mockResolvedValueOnce({ success: false, error: 'Mock save failed' });

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(false);
            expect(result.message).toBe('Mock save failed');
        });

        it('should handle unknown action type', async () => {
            const confirmation = createMockPendingConfirmation({ action_type: 'UNKNOWN_ACTION' });

            const mockPendingConfirmationsBuilderFetch = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null }) };
            const mockUsersBuilder = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: { id: testUserDbId }, error: null }) };
            const mockPendingConfirmationsBuilderDelete = { delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: null }) };

            mockSupabase.from
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUsersBuilder : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDelete : undefined);

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(false);
            expect(result.message).toContain('Unknown action for this confirmation');
        });

        it('should return warning message if deleting processed confirmation fails but action succeeded', async () => {
            const payload = { amount: 50 };
            const confirmation = createMockPendingConfirmation({ action_type: 'AWARD_XP', action_payload: payload });
            const deleteError = { message: 'CRITICAL: Delete failed', code: 'DB500', details: '', hint: '' };

            const mockPendingConfirmationsBuilderFetch = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: confirmation, error: null }) };
            const mockUsersBuilder = { select: vi.fn().mockReturnThis(), eq: vi.fn().mockReturnThis(), single: vi.fn().mockResolvedValueOnce({ data: { id: testUserDbId }, error: null }) };
            const mockPendingConfirmationsBuilderDeleteFailed = { delete: vi.fn().mockReturnThis(), eq: vi.fn().mockResolvedValueOnce({ error: deleteError }) };

            mockSupabase.from
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderFetch : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'users' ? mockUsersBuilder : undefined)
                .mockImplementationOnce((tableName: string) => tableName === 'pending_confirmations' ? mockPendingConfirmationsBuilderDeleteFailed : undefined);

            (gameStateService.awardXp as Mock).mockResolvedValueOnce({ success: true, message: 'XP awarded! (Test)' });

            const result = await processConfirmation(testConfirmationUUID, 'y', mockEnv, mockCallbackQuery);
            expect(result.success).toBe(true);
            expect(result.message).toBe('XP awarded! (Test) (Note: Finalization issue, please report if action seems to repeat.)');
            expect(result.error).toContain(deleteError.message);
        });
    });
}); 