import { Env } from '../types/index';
import { getSupabaseClient } from '../supabaseClient';
import { Database } from '../types/supabase';
import { log, LogLevel, LogContext } from '../utils/logger';
import { processSupabaseResponse, handleSupabaseError } from '../utils/supabaseErrorUtils';
import { SupabaseError } from '../errors/AppError';

// Define interfaces for Hall of Fame entries
export type HallOfFameEntryInsert = Omit<Database['public']['Tables']['hall_of_fame']['Insert'], 'user_id'> & {
    // user_id will be derived from the authenticated user or passed explicitly
    // No functional change, but making it a type alias to satisfy linter for Omit
};

export type HallOfFameEntryUpdate = Database['public']['Tables']['hall_of_fame']['Update'];

export type HallOfFameEntry = Database['public']['Tables']['hall_of_fame']['Row'];

/**
 * Defines the available filters for querying Hall of Fame entries.
 * Allows filtering by entry type (e.g., 'manual_add', 'insight'),
 * a time period (day, week, month, year, all),
 * tags (via metadata), and limiting the number of results.
 */
export interface HallOfFameFilters {
    type?: string; // Assuming 'type' might be a field in metadata or a dedicated column if schema changes
    period?: 'day' | 'week' | 'month' | 'year' | 'all';
    tags?: string[];
    limit?: number; // Added limit for number of entries
    // Add other potential filter fields
}

// Placeholder for service functions
// Actual implementations will follow in subsequent subtasks

const SERVICE_NAME = 'HallOfFameService';

/**
 * Adds a new entry to the Hall of Fame.
 * @param actualUserId The authenticated user's actual ID from the 'users' table.
 * @param entryData Data for the new Hall of Fame entry.
 * @param env Environment variables.
 * @returns The created Hall of Fame entry.
 * @throws {SupabaseError} if actualUserId is not provided or if the database operation fails.
 */
export async function addHallOfFameEntry(
    actualUserId: number,
    entryData: HallOfFameEntryInsert,
    env: Env
): Promise<HallOfFameEntry> {
    const functionName = 'addHallOfFameEntry';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName,
        payload: { actualUserId, entryContent: entryData.content } 
    };
    log(LogLevel.INFO, 'Attempting to add Hall of Fame entry', logContext);
    
    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to add Hall of Fame entry';
        log(LogLevel.ERROR, errorMsg, { ...logContext });
        throw new SupabaseError(errorMsg, logContext);
    }
    if (!entryData.content || !entryData.entry_type) {
        const errorMsg = 'content and entry_type are required for Hall of Fame entry';
        log(LogLevel.WARN, errorMsg, { ...logContext });
        throw new SupabaseError(errorMsg, logContext);
    }

    const supabase = getSupabaseClient(env);
    const dataToInsert = {
        ...entryData,
        user_id: actualUserId,
    };

    try {
        log(LogLevel.DEBUG, 'Inserting Hall of Fame entry into DB', { ...logContext, payload: { ...logContext.payload, dataToInsert }});
        const response = await supabase
            .from('hall_of_fame')
            .insert(dataToInsert)
            .select()
            .single();

        const createdEntry = processSupabaseResponse(response, { ...logContext, operation: 'addHallOfFameEntry.insert' });
        if (!createdEntry) { 
            log(LogLevel.ERROR, 'Hall of Fame entry insert succeeded but returned no data', { ...logContext });
            throw new SupabaseError('Hall of Fame entry insert did not return data as expected', { ...logContext, operation: 'addHallOfFameEntry.insert' });
        }

        log(LogLevel.INFO, 'Hall of Fame entry added successfully', { ...logContext, payload: { ...logContext.payload, createdEntryId: createdEntry.id }});
        return createdEntry;
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'addHallOfFameEntry: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected error in addHallOfFameEntry', { originalError: e.message }, e);
        }
        throw e; 
    }
}

/**
 * Retrieves Hall of Fame entries for a user, with optional filters.
 * @param actualUserId The authenticated user's actual ID from the 'users' table.
 * @param filters Optional filters to apply.
 * @param env Environment variables.
 * @returns A list of Hall of Fame entries.
 * @throws {SupabaseError} if actualUserId is not provided or if the database operation fails.
 */
export async function getHallOfFameEntries(
    actualUserId: number,
    filters: HallOfFameFilters = {},
    env: Env
): Promise<HallOfFameEntry[]> {
    const functionName = 'getHallOfFameEntries';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName,
        payload: { actualUserId, filters }
    };
    log(LogLevel.INFO, 'Attempting to get Hall of Fame entries', logContext);

    if (!actualUserId) {
        const errorMsg = 'actualUserId is required to get Hall of Fame entries';
        log(LogLevel.ERROR, errorMsg, { ...logContext });
        throw new SupabaseError(errorMsg, logContext);
    }

    const supabase = getSupabaseClient(env);
    let query = supabase
        .from('hall_of_fame')
        .select('*')
        .eq('user_id', actualUserId);

    if (filters.type) {
        query = query.eq('entry_type', filters.type);
    }
    if (filters.period && filters.period !== 'all') {
        const now = new Date();
        let startDate: Date | undefined;
        switch (filters.period) {
            case 'day': startDate = new Date(now.setDate(now.getDate() - 1)); break;
            case 'week': startDate = new Date(now.setDate(now.getDate() - 7)); break;
            case 'month': startDate = new Date(now.setMonth(now.getMonth() - 1)); break;
            case 'year': startDate = new Date(now.setFullYear(now.getFullYear() - 1)); break;
        }
        if (startDate) {
            query = query.gte('created_at', startDate.toISOString());
        }
    }
    if (filters.tags && filters.tags.length > 0) {
        query = query.contains('metadata->tags', filters.tags); 
    }
    query = query.order('created_at', { ascending: false });

    if (filters.limit && filters.limit > 0) {
      query = query.limit(filters.limit);
    }

    try {
        log(LogLevel.DEBUG, 'Fetching Hall of Fame entries from DB', logContext);
        const response = await query;
        const entries = processSupabaseResponse(response, { ...logContext, operation: 'getHallOfFameEntries.select' });
        
        log(LogLevel.INFO, `Fetched ${entries?.length || 0} Hall of Fame entries` , { ...logContext, payload: { ...logContext.payload, count: entries?.length || 0 }});
        return entries || [];
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'getHallOfFameEntries: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected error in getHallOfFameEntries', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Updates an existing Hall of Fame entry.
 * @param entryId The ID of the Hall of Fame entry to update.
 * @param updates The updates to apply.
 * @param actualUserId The ID of the user performing the update for authorization.
 * @param env Environment variables.
 * @returns The updated Hall of Fame entry.
 * @throws {SupabaseError} if entryId or actualUserId is not provided, or if the database operation fails.
 */
export async function updateHallOfFameEntry(
    entryId: number,
    updates: HallOfFameEntryUpdate,
    actualUserId: number, // Added for authorization
    env: Env
): Promise<HallOfFameEntry> {
    const functionName = 'updateHallOfFameEntry';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName,
        payload: { entryId, updates, actualUserId }
    };
    log(LogLevel.INFO, 'Attempting to update Hall of Fame entry', logContext);

    if (!entryId || !actualUserId) {
        const errorMsg = 'entryId and actualUserId are required to update Hall of Fame entry';
        log(LogLevel.ERROR, errorMsg, { ...logContext });
        throw new SupabaseError(errorMsg, logContext);
    }

    const supabase = getSupabaseClient(env);
    try {
        log(LogLevel.DEBUG, 'Updating Hall of Fame entry in DB', { ...logContext });
        const response = await supabase
            .from('hall_of_fame')
            .update(updates)
            .eq('id', entryId)
            .eq('user_id', actualUserId) // Authorization check
            .select()
            .single();

        const updatedEntry = processSupabaseResponse(response, { ...logContext, operation: 'updateHallOfFameEntry.update' });
        if (!updatedEntry) {
            log(LogLevel.WARN, 'Hall of Fame entry not found or user mismatch during update, or .single() returned null unexpectedly', { ...logContext });
            throw new SupabaseError('Hall of Fame entry not found, not authorized, or update yielded no data.', { ...logContext, operation: 'updateHallOfFameEntry.update' });
        }

        log(LogLevel.INFO, 'Hall of Fame entry updated successfully', { ...logContext, payload: { ...logContext.payload, updatedEntryId: updatedEntry.id }});
        return updatedEntry;
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'updateHallOfFameEntry: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected error in updateHallOfFameEntry', { originalError: e.message }, e);
        }
        throw e;
    }
}

/**
 * Deletes a Hall of Fame entry.
 * @param entryId The ID of the Hall of Fame entry to delete.
 * @param actualUserId The ID of the user performing the delete for authorization.
 * @param env Environment variables.
 * @throws {SupabaseError} if entryId or actualUserId is not provided, or if the database operation fails.
 */
export async function deleteHallOfFameEntry(
    entryId: number,
    actualUserId: number, // Added for authorization
    env: Env
): Promise<void> {
    const functionName = 'deleteHallOfFameEntry';
    const logContext: LogContext = { 
        service: SERVICE_NAME, 
        functionName,
        payload: { entryId, actualUserId }
    };
    log(LogLevel.INFO, 'Attempting to delete Hall of Fame entry', logContext);

    if (!entryId || !actualUserId) {
        const errorMsg = 'entryId and actualUserId are required to delete Hall of Fame entry';
        log(LogLevel.ERROR, errorMsg, { ...logContext });
        throw new SupabaseError(errorMsg, logContext);
    }

    const supabase = getSupabaseClient(env);
    try {
        log(LogLevel.DEBUG, 'Deleting Hall of Fame entry from DB', { ...logContext });
        const response = await supabase
            .from('hall_of_fame')
            .delete()
            .eq('id', entryId)
            .eq('user_id', actualUserId); // Authorization check

        // For delete, PostgREST usually returns an error or success.
        // processSupabaseResponse will throw if response.error exists.
        if (response.error) {
             handleSupabaseError(response.error, { ...logContext, operation: 'deleteHallOfFameEntry.delete' });
        }
        // If no error, the delete is considered successful.
        // We might want to check response.count if available and if we expect exactly one row to be deleted.
        // For now, no error means success for delete.

        log(LogLevel.INFO, 'Hall of Fame entry deleted successfully (or did not exist for user to delete)', logContext);
    } catch (e: any) {
        if (!(e instanceof SupabaseError)) {
            log(LogLevel.ERROR, 'deleteHallOfFameEntry: Unexpected error', { ...logContext, error: e.message, stack: e.stack });
            throw new SupabaseError(e.message || 'Unexpected error in deleteHallOfFameEntry', { originalError: e.message }, e);
        }
        throw e;
    }
}
