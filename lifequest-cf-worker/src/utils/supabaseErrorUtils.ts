import { PostgrestError } from '@supabase/supabase-js';
import { log, LogLevel } from './logger';
import { SupabaseError } from '../errors/AppError';

/**
 * Handles errors from Supabase client responses, logs them, and throws a standardized SupabaseError.
 * 
 * @param error The PostgrestError object from a Supabase response.
 * @param context Additional context for logging, e.g., { operation: 'fetchUser', userId: 123 }.
 * @returns Never, as it always throws.
 * @throws {SupabaseError} A standardized error object encapsulating the Supabase error details.
 */
export function handleSupabaseError(error: PostgrestError, context?: Record<string, any>): never {
  const errorMessage = `Supabase Error: ${error.message} (Code: ${error.code})`;
  const logContextDetails: Record<string, any> = {
    supabaseMessage: error.message,
    supabaseCode: error.code,
    supabaseSpecificDetails: error.details, // This is a string
    supabaseHint: error.hint,
  };

  if (context) {
    for (const key in context) {
      if (Object.prototype.hasOwnProperty.call(context, key)) {
        logContextDetails[key] = context[key];
      }
    }
  }

  log(LogLevel.ERROR, errorMessage, { details: logContextDetails }); // Pass structured details

  // The SupabaseError constructor itself takes the raw error.details string
  // as part of its own 'details' parameter, which is fine for AppError's 'details?: unknown'
  throw new SupabaseError(
    errorMessage,
    { 
      message: error.message, 
      code: error.code, 
      details: error.details, // raw string here for SupabaseError instance
      hint: error.hint 
    },
    error as unknown as Error 
  );
}

/**
 * Processes a Supabase response, throwing a SupabaseError if an error is present.
 * 
 * @template T The expected data type of the successful response.
 * @param response The response object from a Supabase client call (e.g., PostgrestSingleResponse<T>).
 * @param context Additional context for logging if an error occurs.
 * @returns The data from the response if no error is present.
 * @throws {SupabaseError} If response.error is present.
 */
export function processSupabaseResponse<T>(
  response: { data: T | null; error: PostgrestError | null },
  context?: Record<string, any>
): T {
  if (response.error) {
    handleSupabaseError(response.error, context);
  }
  // If error is null, data should ideally not be null for successful single-fetches/writes,
  // but for .select() it can be an empty array. Caller should handle null/empty data if it's a valid empty state.
  // This utility focuses on the error case.
  // However, for operations that *must* return data (e.g. insert returning data, or a fetch that should always find something),
  // we might want to throw if data is null even if error is null.
  // For now, keeping it simple: if no error, return data (which might be null or empty array).
  if (response.data === null) {
      // This situation can be ambiguous. For a SELECT that finds nothing, null data and null error is valid.
      // For an INSERT/UPDATE/UPSERT that was configured to return data but didn't, it's an issue.
      // Let's log a warning for now if data is null without an explicit error, as it might be unexpected.
      log(LogLevel.WARN, 'Supabase response returned null data without an explicit error.', { details: context }); // Pass context as details
      // Depending on strictness, one might throw an error here too.
      // For example: throw new SupabaseError('Supabase operation returned null data unexpectedly.', context);
  }
  return response.data as T; // Type assertion, caller must be aware of potential null for certain ops
} 