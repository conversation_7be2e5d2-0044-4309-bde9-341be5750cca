import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Env } from '../types';
import { countTokens, countPromptTokens, LLMMessage } from './tokenizerUtil';
import * as tiktoken from 'tiktoken';

// Mock tiktoken module
vi.mock('tiktoken', async (importOriginal) => {
  const actual = await importOriginal<typeof tiktoken>();
  const mockEncodings: Map<string, any> = new Map();

  const getMockEncoding = (encodingName: string) => {
    if (!mockEncodings.has(encodingName)) {
        // Simple mock encode that counts characters as a proxy for tokens in tests
        // or a fixed number to make assertions easy.
        // For more accurate testing against real token counts, this mock would need to be more sophisticated
        // or we'd need to avoid mocking tiktoken and ensure WASM loads in test env.
        const mockEncode = (text: string) => ({ length: text.split(' ').length }); // Simple word count for testing
        mockEncodings.set(encodingName, {
            name: encodingName,
            encode: mockEncode,
            // Add other methods if needed by the functions under test, though encode().length is primary.
        });
    }
    return mockEncodings.get(encodingName);
  };

  return {
    ...actual, // Spread actual to keep other exports like TiktokenModel, TiktokenEncoding if needed by types
    get_encoding: (encodingName: tiktoken.TiktokenEncoding) => {
        const mock = getMockEncoding(encodingName);
        if (mock) return mock;
        throw new Error(`Mocked get_encoding: No mock for ${encodingName}`);
    },
    encoding_for_model: (modelName: tiktoken.TiktokenModel) => {
        // This mapping needs to align with what getEncodingNameForOpenAIModel expects
        if (modelName === 'gpt-4' || modelName === 'gpt-3.5-turbo') {
            return getMockEncoding('cl100k_base');
        }
        throw new Error(`Mocked encoding_for_model: No encoding for model ${modelName}`);
    },
  };
});

const mockEnv = { AI_MODEL_ID: 'gpt-4' } as Env;

describe('tokenizerUtil', () => {
  beforeEach(() => {
    // Clear memoization cache from tokenizerUtil if it's accessible and needs clearing for tests.
    // For the current implementation, tokenizerCache is module-scoped, so it persists.
    // This might be an issue if tests need different mock behaviors for the same model/encoding.
    // For simplicity now, we assume the mock is consistent or tests don't clash.
    // To properly reset, tokenizerCache would need to be exported or have a reset method.
    vi.clearAllMocks(); // Clears mocks, but not module-scoped cache in tokenizerUtil.ts itself
  });

  describe('countTokens', () => {
    it('should count tokens for a known OpenAI model (gpt-4)', async () => {
      const text = "Hello world, this is a test.";
      // Mocked encode counts words: 6 words
      const expectedTokens = 6;
      const tokens = await countTokens(text, 'gpt-4', mockEnv);
      expect(tokens).toBe(expectedTokens);
    });

    it('should throw an error for an unsupported model ID', async () => {
      const text = "Hello world";
      // Mock encoding_for_model will throw for this
      await expect(countTokens(text, 'unsupported-model', mockEnv))
        .rejects
        .toThrow('Unsupported or unrecognized OpenAI model ID for tokenization: unsupported-model');
    });
  });

  describe('countPromptTokens', () => {
    it('should count tokens for a series of messages for gpt-4', async () => {
      const messages: LLMMessage[] = [
        { role: 'system', content: 'You are helpful.' }, // 3 words
        { role: 'user', content: 'Hello there!' },     // 2 words
        { role: 'assistant', content: 'Hi! How can I help?' } // 5 words
      ];
      // Expected based on OpenAI cookbook logic & word count mock:
      // System: role(2) + content(3) + 3 (structure) = 8
      // User: role(1) + content(2) + 3 (structure) = 6
      // Assistant: role(1) + content(5) + 3 (structure) = 9
      // Total before prime = 8 + 6 + 9 = 23
      // Total + 3 (prime) = 26
      const expectedTokens = 26;
      const tokens = await countPromptTokens(messages, 'gpt-4', mockEnv);
      expect(tokens).toBe(expectedTokens);
    });

    it('should count tokens for messages with a name field', async () => {
      const messages: LLMMessage[] = [
        { role: 'user', content: 'Call the function.' }, // 3 words
        { role: 'function', name: 'get_weather', content: '{"location": "LA"}'} // content: 2 words, name: 1 word
      ];
      // User: role(1) + content(3) + 3 = 7
      // Function: role(1) + name(1) -1 (name token adjustment) + content(2) + 3 = 6
      // Total before prime = 7 + 6 = 13
      // Total + 3 (prime) = 16
      const expectedTokens = 16;
      const tokens = await countPromptTokens(messages, 'gpt-4', mockEnv);
      expect(tokens).toBe(expectedTokens);
    });

    it('should throw an error for an unsupported model ID in prompt counting', async () => {
      const messages: LLMMessage[] = [{ role: 'user', content: 'Hi' }];
      await expect(countPromptTokens(messages, 'unsupported-model-prompt', mockEnv))
        .rejects
        .toThrow('Unsupported or unrecognized OpenAI model ID for prompt tokenization: unsupported-model-prompt');
    });
  });
}); 