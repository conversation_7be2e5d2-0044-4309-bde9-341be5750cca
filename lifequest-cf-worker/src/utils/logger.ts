/**
 * Escapes MarkdownV2 special characters in a string for Telegram.
 * @param text The text to escape.
 * @returns The escaped text.
 */
/**
 * Defines the available logging levels.
 */
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

/**
 * Interface for structured log context.
 * Provides a consistent way to include additional information in log messages,
 * such as user IDs, service name, function name, payload, errors, and other details.
 */
export interface LogContext {
  telegramUserId?: number;
  actualUserId?: number;
  service?: string;
  functionName?: string;
  operation?: string;
  messageId?: number | string;
  command?: string;
  payload?: any;
  error?: any;
  stack?: string;
  details?: Record<string, any>;
  count?: number;
  success?: boolean;
  updateId?: any;
  updateTypes?: string[];
  updateContent?: string;
  wasmModuleType?: string;
  // any other common fields
}

/**
 * Logs a message with a given level and context.
 * The log output is a JSON string containing the timestamp, level, message, and any context provided.
 * Logging is filtered based on a globally set `__LOG_LEVEL__` (defaults to INFO).
 *
 * @param level The logging level (DEBUG, INFO, WARN, ERROR).
 * @param message The main log message string.
 * @param context Optional additional context to include in the log entry.
 */
export function log(level: LogLevel, message: string, context?: LogContext): void {
  // Read from the globally set variable
  const envLogLevelStr = typeof (globalThis as any).__LOG_LEVEL__ === 'string' 
                         ? (globalThis as any).__LOG_LEVEL__.toUpperCase() 
                         : undefined;
  const envLogLevel = envLogLevelStr 
                      ? (LogLevel[envLogLevelStr as keyof typeof LogLevel] || LogLevel.INFO) 
                      : LogLevel.INFO;

  const levelValues: { [key in LogLevel]: number } = {
    [LogLevel.DEBUG]: 0,
    [LogLevel.INFO]: 1,
    [LogLevel.WARN]: 2,
    [LogLevel.ERROR]: 3,
  };

  const currentMinLevelValue = levelValues[envLogLevel] !== undefined ? levelValues[envLogLevel] : levelValues[LogLevel.INFO];
  const messageLevelValue = levelValues[level];

  if (messageLevelValue < currentMinLevelValue) {
    return;
  }

  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    message,
    ...context,
  };

  switch (level) {
    case LogLevel.DEBUG:
      console.debug(JSON.stringify(logEntry)); 
      break;
    case LogLevel.INFO:
      console.info(JSON.stringify(logEntry));
      break;
    case LogLevel.WARN:
      console.warn(JSON.stringify(logEntry));
      break;
    case LogLevel.ERROR:
      console.error(JSON.stringify(logEntry));
      break;
    default:
      console.log(JSON.stringify(logEntry));
  }
} 