// import { escapeMarkdown as escapeTelegramMarkdown } from 'telegram-escape'; // Will be removed

export const EMOJI = {
  SPARKLES: '✨',
  COIN: '💰',
  QUEST: '📜',
  CHECK: '✅',
  CROSS: '❌',
  HOURGLASS: '⏳',
  CHART_UP: '📈',
  BOOK: '📚',
  LIGHTBULB: '💡',
  TROPHY: '🏆',
  FIRE: '🔥',
  ENERGY: '⚡️',
  FOCUS: '🎯',
  ROUTINE: '🔄',
  HALL_OF_FAME: '🏛️',
  SUMMARY: '📊',
  STATS: '📈',
  ERROR: '⚠️',
  INFO: 'ℹ️',
  SUCCESS: '🎉',
  QUESTION: '❓',
  THINKING: '🤔',
  WRITING: '✍️',
  CONSTRUCTION: '🚧',
  CLOCK: '⏰',
  CALENDAR: '📅',
};

/**
 * Creates a simple text-based progress bar.
 * @param currentValue The current value.
 * @param maxValue The maximum value.
 * @param length The desired length of the progress bar in characters.
 * @param filledChar The character to use for the filled part of the bar.
 * @param emptyChar The character to use for the empty part of the bar.
 * @returns A string representing the progress bar.
 */
export function createProgressBar(
  currentValue: number,
  maxValue: number,
  length: number = 10,
  filledChar: string = '█',
  emptyChar: string = '░'
): string {
  if (maxValue === 0) {
    return emptyChar.repeat(length);
  }
  const percentage = Math.max(0, Math.min(1, currentValue / maxValue));
  const filledLength = Math.round(percentage * length);
  const emptyLength = length - filledLength;

  return filledChar.repeat(filledLength) + emptyChar.repeat(emptyLength);
}

// Characters that must be escaped for Telegram MarkdownV2
const TELEGRAM_MDV2_SPECIAL_CHARACTERS = [
  '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!'
];

/**
 * Escapes MarkdownV2 special characters in a string for Telegram.
 * @param text The text to escape.
 * @returns The escaped text.
 */
export function escapeMarkdown(text: string | null | undefined): string {
  if (text === null || text === undefined) {
    return '';
  }
  let escapedText = String(text);

  // IMPORTANT: Order matters. Backslash must be escaped first.
  escapedText = escapedText.replace(/\\/g, '\\\\'); // \ -> \\
  
  // Escape other special characters individually to avoid complex regex interactions
  // and ensure clarity. This list is for MarkdownV2.
  escapedText = escapedText.replace(/_/g, '\\_');     // _ -> \_
  escapedText = escapedText.replace(/\*/g, '\\*');    // * -> \*
  escapedText = escapedText.replace(/\[/g, '\\[');    // [ -> \[
  escapedText = escapedText.replace(/\]/g, '\\]');    // ] -> \]
  escapedText = escapedText.replace(/\(/g, '\\(');    // ( -> \(
  escapedText = escapedText.replace(/\)/g, '\\)');    // ) -> \)
  escapedText = escapedText.replace(/~/g, '\\~');      // ~ -> \~
  escapedText = escapedText.replace(/`/g, '\\`');      // ` -> \`
  escapedText = escapedText.replace(/>/g, '\\>');      // > -> \>
  escapedText = escapedText.replace(/#/g, '\\#');      // # -> \#
  escapedText = escapedText.replace(/\+/g, '\\+');     // + -> \+
  escapedText = escapedText.replace(/-/g, '\\-');      // - -> \-
  escapedText = escapedText.replace(/=/g, '\\=');      // = -> \=
  escapedText = escapedText.replace(/\|/g, '\\|');     // | -> \|
  escapedText = escapedText.replace(/\{/g, '\\{');     // { -> \{
  escapedText = escapedText.replace(/\}/g, '\\}');     // } -> \}
  escapedText = escapedText.replace(/\./g, '\\.');     // . -> \.
  escapedText = escapedText.replace(/!/g, '\\!');     // ! -> \!

  return escapedText;
}

/**
 * Minimal interface representing game state data specifically for formatting purposes.
 * Includes essential fields like XP, coins, and energy.
 */
export interface StatsData {
  xp?: number | null;
  coins?: number | null;
  energy?: number | null;
  // Potentially add user level or other summary stats here in the future
}

/**
 * Formats game statistics into a Markdown string.
 * @param statsData The game state data to format.
 * @returns A Markdown string representing the game statistics.
 */
export function formatStats(statsData: StatsData | null): string {
  if (!statsData) {
    return escapeMarkdown(`${EMOJI.INFO} No stats available yet.`);
  }

  const { xp, coins, energy } = statsData;
  let message = `${EMOJI.STATS} *Your Stats* ${EMOJI.STATS}\n`;
  message += '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n';

  if (xp !== null && xp !== undefined) {
    message += `${EMOJI.SPARKLES} XP: *${xp}*\n`;
  }
  if (coins !== null && coins !== undefined) {
    message += `${EMOJI.COIN} Coins: *${coins}*\n`;
  }
  if (energy !== null && energy !== undefined) {
    message += `${EMOJI.ENERGY} Energy: *${energy}*/100 ${createProgressBar(energy, 100, 10)}\n`;
  }

  if (message === `${EMOJI.STATS} *Your Stats* ${EMOJI.STATS}\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`) {
    return escapeMarkdown(`${EMOJI.INFO} Stats are currently empty. Play more to see your progress!`);
  }

  return message;
}

/**
 * Minimal interface representing a quest, tailored for formatting needs.
 * Includes key fields like ID, title, description, status, type, and rewards.
 */
export interface FormattableQuest {
  id: number;
  title: string;
  description?: string | null;
  status?: string | null; // e.g., 'pending', 'active', 'done', 'failed'
  type?: string | null; // e.g., 'main', 'side', 'routine'
  xp_reward?: number | null;
  coin_reward?: number | null;
  // Consider adding parent_quest_id or streak if relevant for display
}

/**
 * Formats a list of quests into a Markdown string.
 * @param quests An array of quest objects to format.
 * @param title The title for the quest list section (e.g., "Active Quests").
 * @returns A Markdown string representing the list of quests.
 */
export function formatQuestsList(quests: FormattableQuest[] | null, title: string = "Quests"): string {
  if (!quests || quests.length === 0) {
    return `${EMOJI.INFO} No quests to display in "${escapeMarkdown(title)}"\.`;
  }

  let message = `${EMOJI.QUEST} *${escapeMarkdown(title)}* ${EMOJI.QUEST}\n`;
  message += '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n';

  quests.forEach(quest => {
    let questIcon = EMOJI.QUEST;
    if (quest.status === 'done') questIcon = EMOJI.CHECK;
    else if (quest.status === 'failed') questIcon = EMOJI.CROSS;
    else if (quest.status === 'active' || quest.status === 'in-progress') questIcon = EMOJI.HOURGLASS;

    message += `${questIcon} *${escapeMarkdown(quest.title)}* \\(ID: ${quest.id}\\)\n`;
    if (quest.type) {
      message += `    Type: ${escapeMarkdown(quest.type)}\n`;
    }
    if (quest.description) {
      message += `    Desc: ${escapeMarkdown(quest.description)}\n`;
    }
    if (quest.xp_reward !== null && quest.xp_reward !== undefined) {
      message += `    ${EMOJI.SPARKLES} XP: ${quest.xp_reward}\n`;
    }
    if (quest.coin_reward !== null && quest.coin_reward !== undefined) {
      message += `    ${EMOJI.COIN} Coins: ${quest.coin_reward}\n`;
    }
    message += `    Status: ${escapeMarkdown(quest.status || 'unknown')}\n`;
    message += `\n`;
  });

  return message;
}

/**
 * Minimal interface representing a Hall of Fame entry for formatting.
 * Includes ID, content, entry type, creation date, optional period, and metadata.
 */
export interface FormattableHallOfFameEntry {
  id: number;
  content: string;
  entry_type: string;
  created_at?: string | null;
  period?: string | null;
  metadata?: any | null; // Keep flexible for now
}

/**
 * Formats a list of Hall of Fame entries into a Markdown string.
 * @param entries An array of Hall of Fame entry objects to format.
 * @param title The title for the Hall of Fame list section.
 * @returns A Markdown string representing the list of entries.
 */
export function formatHallOfFameEntries(entries: FormattableHallOfFameEntry[] | null, title: string = "Hall of Fame"): string {
  if (!entries || entries.length === 0) {
    return `${EMOJI.INFO} No entries to display in "${escapeMarkdown(title)}"\.`;
  }

  let message = `${EMOJI.HALL_OF_FAME} *${escapeMarkdown(title)}* ${EMOJI.HALL_OF_FAME}\n`;
  message += '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n';

  entries.forEach(entry => {
    message += `${EMOJI.TROPHY} *${escapeMarkdown(entry.content)}* \\(ID: ${entry.id}\\)\n`;
    message += `    Type: ${escapeMarkdown(entry.entry_type)}\n`;
    if (entry.created_at) {
      try {
        const date = new Date(entry.created_at);
        // Dates YYYY-MM-DD, hyphens need escaping for MarkdownV2
        const formattedDate = date.toISOString().split('T')[0].replace(/-/g, '\\-'); 
        message += `    Date: ${formattedDate}\n`;
      } catch (e) {
        message += `    Date: Invalid date\n`;
      }
    }
    if (entry.period) {
      message += `    Period: ${escapeMarkdown(entry.period)}\n`;
    }
    if (entry.metadata && typeof entry.metadata.source === 'string') {
        message += `    Source: ${escapeMarkdown(entry.metadata.source)}\n`;
    }
    message += `\n`;
  });

  return message;
}