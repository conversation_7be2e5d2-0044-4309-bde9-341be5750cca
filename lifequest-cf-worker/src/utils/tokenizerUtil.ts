import { Env } from '../types';
import { log, LogLevel, LogContext } from './logger';
import { Tiktoken, TiktokenModel, TiktokenEncoding, get_encoding, encoding_for_model } from 'tiktoken';
import { init } from 'tiktoken/init';

// Import the WASM module. Wrangler's [[rules]] type = "CompiledWasm" should make this a WebAssembly.Module.
// The @ts-ignore is because TS doesn't natively know this import results in WebAssembly.Module without specific .d.ts for .wasm files.
// @ts-ignore
import wasmModuleInstanceContent from '../../node_modules/tiktoken/tiktoken_bg.wasm';

const SERVICE_NAME = 'TokenizerUtil';
const TARGET_TOKEN_ESTIMATE_DIVISOR = 3.5;

const tokenizerCache: Map<string, Tiktoken> = new Map();
let tiktokenInitialized = false;
let tiktokenInitPromise: Promise<void> | null = null;

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
}

async function ensureTiktokenInitialized(): Promise<void> {
    if (tiktokenInitialized) return;
    if (tiktokenInitPromise) return tiktokenInitPromise;

    const logContext: LogContext = { service: SERVICE_NAME, functionName: 'ensureTiktokenInitialized' };
    log(LogLevel.DEBUG, 'Attempting to initialize Tiktoken WASM with explicitly imported module...', logContext);

    tiktokenInitPromise = (async () => {
        try {
            // The bundler (esbuild via Wrangler) with [[rules]] type="CompiledWasm"
            // should provide wasmModuleInstanceContent as a WebAssembly.Module.
            const wasmModule: WebAssembly.Module = wasmModuleInstanceContent;

            if (!(wasmModule instanceof WebAssembly.Module)) {
                const errorMsg = 'tiktoken_bg.wasm was not imported as a WebAssembly.Module. Check wrangler.toml [[rules]] and import.';
                log(LogLevel.ERROR, errorMsg, { ...logContext, wasmModuleType: typeof wasmModule });
                throw new Error(errorMsg);
            }
            
            await init((imports: WebAssembly.Imports) => WebAssembly.instantiate(wasmModule, imports));
            tiktokenInitialized = true;
            log(LogLevel.INFO, 'Tiktoken WASM initialized successfully via explicitly imported module.', logContext);
        } catch (e: any) {
            log(LogLevel.ERROR, 'Failed to initialize Tiktoken WASM with explicit module', { ...logContext, error: e.message, stack: e.stack });
            tiktokenInitialized = false; 
            throw e; 
        } finally {
            tiktokenInitPromise = null;
        }
    })();
    return tiktokenInitPromise;
}

/**
 * Retrieves a Tiktoken encoding instance for a given encoding name, assuming WASM is self-initialized by tiktoken.
 * Caches instances to avoid re-initialization costs.
 * @param encodingName An OpenAI encoding name (e.g., "cl100k_base").
 * @returns The Tiktoken instance or null if initialization/get fails.
 */
function getOpenAITiktokenEncoding(encodingName: TiktokenEncoding): Tiktoken | null {
  const functionName = 'getOpenAITiktokenEncoding';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { encodingName } };
  if (!tiktokenInitialized) {
    log(LogLevel.ERROR, 'Tiktoken not initialized when trying to get encoding.', logContext);
    throw new Error('Tiktoken WASM not initialized. Ensure ensureTiktokenInitialized was called and awaited.');
  }
  if (tokenizerCache.has(encodingName)) {
    return tokenizerCache.get(encodingName)!;
  }
  try {
    const encoding = get_encoding(encodingName);
    tokenizerCache.set(encodingName, encoding);
    return encoding;
  } catch (error: any) {
    log(LogLevel.ERROR, `Failed to get Tiktoken encoding for ${encodingName}. Error: ${error.message}.`, logContext);
    return null;
  }
}

/**
 * Attempts to map a general OpenAI model ID to its specific TiktokenEncoding name.
 * @param modelId The general model ID (e.g., "gpt-4-turbo", "gpt-3.5-turbo").
 * @returns The TiktokenEncoding name (e.g., "cl100k_base") or null if no mapping is found.
 */
function getEncodingNameForOpenAIModel(modelId: string): TiktokenEncoding | null {
  const functionName = 'getEncodingNameForOpenAIModel';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { modelId } };
  let resolvedModelId = modelId;
  if (modelId.startsWith('openai/')) {
    resolvedModelId = modelId.substring('openai/'.length);
  }

  // Specific overrides based on research for gpt-4.1 and gpt-4o
  if (resolvedModelId.startsWith('gpt-4o') || resolvedModelId.startsWith('gpt-4.1')) {
    log(LogLevel.DEBUG, `Mapping ${resolvedModelId} to o200k_base based on known patterns.`, logContext);
    return 'o200k_base';
  }

  // Try tiktoken's direct mapping first for exact model names it recognizes
  try {
    const encoding = encoding_for_model(resolvedModelId as TiktokenModel);
    log(LogLevel.DEBUG, `Used encoding_for_model for ${resolvedModelId}, got encoding: ${encoding.name}`, logContext);
    return encoding.name as TiktokenEncoding;
  } catch (e) {
    log(LogLevel.DEBUG, `encoding_for_model failed for ${resolvedModelId}. Trying fallbacks. Error: ${(e as Error).message}`, logContext);
  }

  // Pattern-based fallbacks if direct mapping fails or for broader categories
  if (resolvedModelId.startsWith('gpt-4')) { // Catches other gpt-4 variants not gpt-4o or gpt-4.1
    log(LogLevel.WARN, `Defaulting to 'cl100k_base' for GPT-4 variant: ${resolvedModelId}`, logContext);
    return 'cl100k_base'; 
  }
  if (resolvedModelId.startsWith('gpt-3.5-turbo')) {
    log(LogLevel.DEBUG, `Mapping ${resolvedModelId} to cl100k_base (standard for gpt-3.5-turbo).`, logContext);
    return 'cl100k_base';
  }
  if (resolvedModelId.startsWith('text-embedding-3-large') || 
      resolvedModelId.startsWith('text-embedding-3-small') || 
      resolvedModelId.startsWith('text-embedding-ada-002')) {
    log(LogLevel.DEBUG, `Mapping ${resolvedModelId} (embedding model) to cl100k_base.`, logContext);
    return 'cl100k_base';
  }
  if (resolvedModelId.includes('davinci')) {
    log(LogLevel.DEBUG, `Mapping ${resolvedModelId} to p50k_base.`, logContext);
    return 'p50k_base';
  }
  
  log(LogLevel.ERROR, `No TiktokenEncoding for modelId: '${modelId}'`, logContext);
  return null;
}

/**
 * Counts tokens in a given text string for a specified OpenAI model ID.
 * Uses Tiktoken for accurate counting.
 *
 * @param text The text to count tokens for.
 * @param modelId The OpenAI model ID (e.g., env.AI_MODEL_ID) to determine the tokenizer.
 * @param env The Cloudflare Worker environment.
 * @returns A promise that resolves to the number of tokens.
 * @throws Error if tokenizer cannot be initialized for the model or if encoding fails.
 */
export async function countTokens(text: string, modelId: string, env: Env): Promise<number> {
  await ensureTiktokenInitialized(); // Ensure WASM is ready
  const functionName = 'countTokens';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { modelId, textLength: text.length } };
  log(LogLevel.DEBUG, `Attempting to count tokens for OpenAI model: ${modelId}`, logContext);

  const encodingName = getEncodingNameForOpenAIModel(modelId);
  if (!encodingName) {
    log(LogLevel.ERROR, `Unsupported or unrecognized OpenAI model ID for tokenization: ${modelId}`, logContext);
    throw new Error(`Unsupported or unrecognized OpenAI model ID for tokenization: ${modelId}`);
  }

  const tokenizer = getOpenAITiktokenEncoding(encodingName);
  if (!tokenizer) {
    log(LogLevel.ERROR, `Could not get/initialize tokenizer for encoding ${encodingName} (derived from ${modelId})`, logContext);
    throw new Error(`Could not get/initialize tokenizer for encoding ${encodingName}`);
  }

  try {
    const tokens = tokenizer.encode(text).length;
    log(LogLevel.INFO, `Counted ${tokens} tokens for model ${modelId} using encoding ${encodingName}`, logContext);
    return tokens;
  } catch (error: any) {
    log(LogLevel.ERROR, `Error encoding text with Tiktoken for model ${modelId}`, { ...logContext, error: error.message, stack: error.stack });
    throw new Error(`Error encoding text with Tiktoken for model ${modelId}: ${error.message}`);
  }
}

/**
 * Counts tokens in an array of LLM messages for OpenAI models, considering special tokens.
 * Based on OpenAI's cookbook for counting tokens with tiktoken.
 *
 * @param messages The array of messages.
 * @param modelId The OpenAI model ID (e.g., "gpt-4", "gpt-3.5-turbo").
 * @param env The Cloudflare Worker environment.
 * @returns A promise that resolves to the total number of tokens.
 * @throws Error if the model is not supported/recognized or an encoding issue occurs.
 */
export async function countPromptTokens(
  messages: LLMMessage[], 
  modelId: string, 
  env: Env 
): Promise<number> {
  await ensureTiktokenInitialized(); // Ensure WASM is ready
  const functionName = 'countPromptTokens';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, payload: { modelId, messageCount: messages.length } }; 
  log(LogLevel.DEBUG, `Attempting to count prompt tokens for OpenAI model: ${modelId}`, logContext);

  const encodingName = getEncodingNameForOpenAIModel(modelId);
  if (!encodingName) {
    log(LogLevel.ERROR, `Unsupported or unrecognized OpenAI model ID for prompt tokenization: ${modelId}`, logContext);
    throw new Error(`Unsupported or unrecognized OpenAI model ID for prompt tokenization: ${modelId}`);
  }
  
  const tokenizer = getOpenAITiktokenEncoding(encodingName);
  if (!tokenizer) {
    log(LogLevel.ERROR, `Could not get/initialize tokenizer for encoding ${encodingName} (derived from ${modelId}) for prompt tokenization`, logContext);
    throw new Error(`Could not get/initialize tokenizer for encoding ${encodingName} for prompt tokenization`);
  }

  let numTokens = 0;
  try {
    for (const message of messages) {
      numTokens += tokenizer.encode(message.role).length;
      numTokens += tokenizer.encode(message.content).length;
      if (message.name) {
        numTokens += tokenizer.encode(message.name).length;
        numTokens -= 1; 
      }
      numTokens += 3; 
    }
    numTokens += 3; 
    log(LogLevel.INFO, `Counted ${numTokens} prompt tokens for model ${modelId} using Tiktoken encoding ${encodingName}.`, logContext);
    return numTokens;
  } catch (error: any) {
    log(LogLevel.ERROR, `Error counting prompt tokens with Tiktoken for ${modelId} using encoding ${encodingName}: ${error.message}`, { ...logContext, error: error.message, stack: error.stack });
    throw new Error(`Error counting prompt tokens with Tiktoken for model ${modelId} using encoding ${encodingName}: ${error.message}`);
  }
} 