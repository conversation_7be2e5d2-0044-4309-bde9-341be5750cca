import { describe, it, expect } from 'vitest';
import {
  EMOJI,
  createProgressBar,
  escapeMarkdown,
  formatStats,
  StatsData,
  formatQuestsList,
  FormattableQuest,
  formatHallOfFameEntries,
  FormattableHallOfFameEntry
} from './formatter';

describe('formatter utilities', () => {
  describe('createProgressBar', () => {
    it('should create an empty bar for 0 max value', () => {
      expect(createProgressBar(0, 0, 10)).toBe('░░░░░░░░░░');
    });
    it('should create a full bar when current equals max', () => {
      expect(createProgressBar(10, 10, 10)).toBe('██████████');
    });
    it('should create a half bar correctly', () => {
      expect(createProgressBar(5, 10, 10)).toBe('█████░░░░░');
    });
    it('should handle zero current value', () => {
      expect(createProgressBar(0, 10, 10)).toBe('░░░░░░░░░░');
    });
    it('should handle custom characters', () => {
      expect(createProgressBar(3, 5, 5, '#', '-')).toBe('###--');
    });
  });

  describe('escapeMarkdown', () => {
    it('should return empty string for null or undefined', () => {
      expect(escapeMarkdown(null)).toMatchInlineSnapshot(`""`);
      expect(escapeMarkdown(undefined)).toMatchInlineSnapshot(`""`);
    });

    it('should escape character: _', () => { expect(escapeMarkdown('_')).toMatchInlineSnapshot(`"\\_"`); });
    it('should escape character: *', () => { expect(escapeMarkdown('*')).toMatchInlineSnapshot(`"\\*"`); });
    it('should escape character: [', () => { expect(escapeMarkdown('[')).toMatchInlineSnapshot(`"\\["`); });
    it('should escape character: ]', () => { expect(escapeMarkdown(']')).toMatchInlineSnapshot(`"\\]"`); });
    it('should escape character: (', () => { expect(escapeMarkdown('(')).toMatchInlineSnapshot(`"\\("`); });
    it('should escape character: )', () => { expect(escapeMarkdown(')')).toMatchInlineSnapshot(`"\\)"`); });
    it('should escape character: ~', () => { expect(escapeMarkdown('~')).toMatchInlineSnapshot(`"\\~"`); });
    it('should escape character: `', () => { expect(escapeMarkdown('`')).toMatchInlineSnapshot(`"\\\`"`); });
    it('should escape character: >', () => { expect(escapeMarkdown('>')).toMatchInlineSnapshot(`"\\>"`); });
    it('should escape character: #', () => { expect(escapeMarkdown('#')).toMatchInlineSnapshot(`"\\#"`); });
    it('should escape character: +', () => { expect(escapeMarkdown('+')).toMatchInlineSnapshot(`"\\+"`); });
    it('should escape character: -', () => { expect(escapeMarkdown('-')).toMatchInlineSnapshot(`"\\-"`); });
    it('should escape character: =', () => { expect(escapeMarkdown('=')).toMatchInlineSnapshot(`"\\="`); });
    it('should escape character: |', () => { expect(escapeMarkdown('|')).toMatchInlineSnapshot(`"\\|"`); });
    it('should escape character: {', () => { expect(escapeMarkdown('{')).toMatchInlineSnapshot(`"\\{"`); });
    it('should escape character: }', () => { expect(escapeMarkdown('}')).toMatchInlineSnapshot(`"\\}"`); });
    it('should escape character: .', () => { expect(escapeMarkdown('.')).toMatchInlineSnapshot(`"\\."`); });
    it('should escape character: !', () => { expect(escapeMarkdown('!')).toMatchInlineSnapshot(`"\\!"`); });

    it('should not escape a non-special character like "a" or "1" ', () => {
      expect(escapeMarkdown('a')).toMatchInlineSnapshot(`"a"`);
      expect(escapeMarkdown('1')).toMatchInlineSnapshot(`"1"`);
      expect(escapeMarkdown('Hello World')).toMatchInlineSnapshot(`"Hello World"`);
    });

    it('should pass through a single backslash unchanged', () => {
      expect(escapeMarkdown('\\')).toMatchInlineSnapshot(`"\\\\"`);
    });

    it('should pass through multiple backslashes unchanged', () => {
      expect(escapeMarkdown('\\\\\\')).toMatchInlineSnapshot(`"\\\\\\\\\\\\"`);
    });

    it('should correctly escape a mixed string: Hello _World_! Check this. And link [Test](http://example.com=test). <script>', () => {
      const text = 'Hello _World_! Check this. And link [Test](http://example.com=test). <script>';
      expect(escapeMarkdown(text)).toMatchInlineSnapshot(`"Hello \\_World\\_\\! Check this\\. And link \\[Test\\]\\(http://example\\.com\\=test\\)\\. <script\\>"`);
    });

    it('should handle string with only backslashes and special chars: \\*\-.=', () => {
      const text = '\\\\*\\-\\.\\=';
      expect(escapeMarkdown(text)).toMatchInlineSnapshot(`"\\\\\\\\\\*\\\\\\-\\\\\\.\\\\\\="`);
    });
  });

  describe('formatStats', () => {
    it('should return "no stats" message for null data', () => {
      expect(formatStats(null)).toBe(escapeMarkdown(`${EMOJI.INFO} No stats available yet.`));
    });
    it('should return "empty stats" message for undefined stats', () => {
      const emptyStats: StatsData = {};
      expect(formatStats(emptyStats)).toBe(escapeMarkdown(`${EMOJI.INFO} Stats are currently empty. Play more to see your progress!`));
    });
    it('should format all available stats correctly', () => {
      const stats: StatsData = { xp: 100, coins: 50, energy: 75 };
      expect(formatStats(stats)).toMatchInlineSnapshot(`
        "📈 *Your Stats* 📈
        ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        ✨ XP: *100*
        💰 Coins: *50*
        ⚡️ Energy: *75*/100 ████████░░
        "
      `);
    });
    it('should format partial stats correctly', () => {
      const stats: StatsData = { xp: 100, energy: 0 };
      expect(formatStats(stats)).toMatchInlineSnapshot(`
        "📈 *Your Stats* 📈
        ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        ✨ XP: *100*
        ⚡️ Energy: *0*/100 ░░░░░░░░░░
        "
      `);
    });
  });

  describe('formatQuestsList', () => {
    it('should return "no quests" message for null or empty quests', () => {
      expect(formatQuestsList(null, 'Active Quests')).toBe(`${EMOJI.INFO} No quests to display in "Active Quests".`);
      expect(formatQuestsList([], 'Pending Quests')).toBe(`${EMOJI.INFO} No quests to display in "Pending Quests".`);
    });

    const quest1: FormattableQuest = {
      id: 1,
      title: 'Defeat the Dragon',
      description: 'A mighty beast guards the treasure.',
      status: 'active',
      type: 'main',
      xp_reward: 1000,
      coin_reward: 500,
    };
    const quest2: FormattableQuest = {
      id: 2,
      title: 'Collect 10 Herbs',
      status: 'done',
      type: 'side',
      xp_reward: 50,
    };
     const quest3: FormattableQuest = {
      id: 3,
      title: '<Evil Script Test>',
      status: 'pending',
      type: 'daily',
      coin_reward: 10,
    };

    it('should format a list of quests correctly', () => {
      const quests = [quest1, quest2, quest3];
      expect(formatQuestsList(quests, 'My Quests')).toMatchInlineSnapshot(`
        "📜 *My Quests* 📜
        ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        ⏳ *Defeat the Dragon* \\(ID: 1\\)
            Type: main
            Desc: A mighty beast guards the treasure\\.
            ✨ XP: 1000
            💰 Coins: 500
            Status: active

        ✅ *Collect 10 Herbs* \\(ID: 2\\)
            Type: side
            ✨ XP: 50
            Status: done

        📜 *<Evil Script Test\\>* \\(ID: 3\\)
            Type: daily
            💰 Coins: 10
            Status: pending

        "
      `);
    });
  });

  describe('formatHallOfFameEntries', () => {
    it('should return "no entries" message for null or empty entries', () => {
      expect(formatHallOfFameEntries(null, 'Achievements')).toBe(`${EMOJI.INFO} No entries to display in "Achievements".`);
      expect(formatHallOfFameEntries([], 'Learnings')).toBe(`${EMOJI.INFO} No entries to display in "Learnings".`);
    });

    const entry1: FormattableHallOfFameEntry = {
      id: 101,
      content: 'Solved a complex bug',
      entry_type: 'Achievement',
      created_at: '2023-10-26T10:00:00Z',
      period: 'Q4 2023',
      metadata: { source: 'Work' }
    };
    const entry2: FormattableHallOfFameEntry = {
      id: 102,
      content: 'Read \'Sapiens\'',
      entry_type: 'Learning',
      created_at: '2023-11-15T14:30:00Z',
    };

    it('should format a list of hall of fame entries correctly', () => {
      const entries = [entry1, entry2];
      expect(formatHallOfFameEntries(entries, 'Epic Wins')).toMatchInlineSnapshot(`
        "🏛️ *Epic Wins* 🏛️
        ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        🏆 *Solved a complex bug* \\(ID: 101\\)
            Type: Achievement
            Date: 2023\\-10\\-26
            Period: Q4 2023
            Source: Work

        🏆 *Read 'Sapiens'* \\(ID: 102\\)
            Type: Learning
            Date: 2023\\-11\\-15

        "
      `);
    });
  });
}); 