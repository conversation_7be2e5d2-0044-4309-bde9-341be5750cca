import { InlineKeyboardButton } from '@grammyjs/types';
import { log, LogLevel, LogContext } from './logger'; // Import logger

const SERVICE_NAME = 'TelegramUtils';

/**
 * Sends a message with inline confirmation buttons (Yes/No).
 *
 * @param botToken The Telegram bot token.
 * @param chatId The chat ID to send the message to.
 * @param text The text of the message prompt.
 * @param confirmationUUID The UUID to be included in the callback_data for tracking.
 * @returns Promise<Response> The raw fetch Response from the Telegram API call.
 */
export async function sendConfirmationMessage(
  botToken: string,
  chatId: string, // Usually a number, but Telegram API accepts string for chat_id
  text: string,
  confirmationUUID: string,
  telegramUserIdForLog?: number // Optional: For logging context
): Promise<Response> {
  const functionName = 'sendConfirmationMessage';
  const logContext: LogContext = { 
    service: SERVICE_NAME, 
    functionName, 
    telegramUserId: telegramUserIdForLog,
    payload: { chatId, confirmationUUID, textLength: text.length }
  }; 
  log(LogLevel.INFO, 'Attempting to send confirmation message via Telegram', logContext);

  if (!botToken) {
    log(LogLevel.ERROR, 'Telegram bot token is missing', { ...logContext, error: 'Bot token not provided' });
    // Depending on how this utility is used, throwing an error might be appropriate
    // For now, returning a synthetic failed response to indicate critical failure.
    return new Response(JSON.stringify({ ok: false, description: "Bot token missing" }), { status: 500, headers: { 'Content-Type': 'application/json' } });
  }

  const inlineKeyboard: InlineKeyboardButton[][] = [
    [
      { text: '✅ Yes', callback_data: `cfm:${confirmationUUID}:y` },
      { text: '❌ No', callback_data: `cfm:${confirmationUUID}:n` },
    ],
  ];

  // Helper function to escape MarkdownV2 (can be moved to a shared util if used elsewhere too)
  // This is a copy from telegramHandler.ts, consider centralizing if not already.
  function escapeMarkdownV2Local(textToEscape: string): string {
    const V2_SPECIAL_CHARS = [
      '_', '*', '[', ']', '(', ')', '~', '`', '>', 
      '#', '+', '-', '=', '|', '{', '}', '.', '!'
    ];
    return textToEscape.replace(new RegExp(`([${V2_SPECIAL_CHARS.join('\\')}])`, 'g'), '\\$1');
  }

  const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
  const requestBody = {
    chat_id: chatId,
    text: escapeMarkdownV2Local(text), // Apply escaping here
    reply_markup: {
      inline_keyboard: inlineKeyboard,
    },
    parse_mode: 'MarkdownV2', // Ensure this is consistent with text formatting
  };

  try {
    log(LogLevel.DEBUG, 'Sending request to Telegram API for confirmation message', { ...logContext, payload: { ...logContext.payload, url, requestBodyPreview: { chat_id: chatId, textLength: text.length, parse_mode: 'MarkdownV2'} } });
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorBody = await response.text(); // Always await text() or json() before using response again
      log(LogLevel.ERROR, 'Error sending confirmation message to Telegram API', { 
        ...logContext, 
        error: `API Error: ${response.status}`, 
        details: { errorBody, status: response.status }
      });
      // Return the actual failed response from Telegram
      return new Response(errorBody, { status: response.status, headers: response.headers }); 
    }

    log(LogLevel.INFO, 'Confirmation message sent successfully via Telegram API', { ...logContext, payload: { ...logContext.payload, status: response.status } });
    return response; // Return the successful response

  } catch (error: any) {
    log(LogLevel.ERROR, 'Network or unexpected error sending confirmation message', { ...logContext, error: error.message, stack: error.stack });
    // Return a synthetic failed response for unhandled errors
    return new Response(JSON.stringify({ ok: false, description: `Client-side error: ${error.message}` }), { status: 500, headers: { 'Content-Type': 'application/json' } });
  }
} 