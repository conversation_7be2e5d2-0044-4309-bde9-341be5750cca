import { Env, MemoryRAGChunk } from './types';
// import { Ai } from '@cloudflare/ai';
import { log, LogLevel, LogContext } from './utils/logger'; // Import logger
// import { VectorizeIndex, VectorizeMatches } from '@cloudflare/workers-types'; // We might need to adjust this import based on actual availability

/**
 * @file vectorizeClient.ts
 * This file contains functions for interacting with Cloudflare Vectorize,
 * including generating text embeddings, storing vectors, and querying similar memories.
 */

const SERVICE_NAME = 'VectorizeClient';

/**
 * Generates text embeddings using Cloudflare Workers AI.
 * @param env The environment object containing bindings.
 * @param text The text to embed.
 * @returns A promise that resolves to an array of numbers representing the embedding.
 * @throws Will throw an error if the AI service fails or returns an unexpected response.
 */
export async function generateTextEmbedding(env: Env, text: string, telegramUserId?: number): Promise<number[] | null> {
  const functionName = 'generateTextEmbedding';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId, payload: { textLength: text.length } }; 
  log(LogLevel.INFO, 'Attempting to generate text embedding', logContext);

  if (!env.AI) {
    log(LogLevel.ERROR, 'env.AI binding for Workers AI is not available', logContext);
    return null;
  }
  if (!text || text.trim() === '') {
    log(LogLevel.WARN, 'Cannot generate embedding for empty text', logContext);
    return null;
  }

  try {
    const configuredModelId = env.CF_EMBEDDING_MODEL_ID;
    const fallbackModelId = '@cf/baai/bge-m3'; // Default fallback
    let modelToUse = fallbackModelId;
    let modelSource = 'fallback';

    if (configuredModelId && configuredModelId.trim() !== '') {
      modelToUse = configuredModelId;
      modelSource = 'env';
      log(LogLevel.INFO, `Using configured embedding model: ${modelToUse}`, { ...logContext, details: { modelUsed: modelToUse, source: modelSource } });
    } else {
      log(LogLevel.WARN, `CF_EMBEDDING_MODEL_ID not set or empty, using fallback embedding model: ${fallbackModelId}`, { ...logContext, details: { modelUsed: fallbackModelId, source: modelSource } });
    }

    log(LogLevel.DEBUG, `Running AI model ${modelToUse} for text embedding via env.AI.run`, { ...logContext, payload: { ...(logContext.payload || {}), modelUsed: modelToUse }});
    const embeddingResponse: any = await env.AI.run(modelToUse, { text: [text] });
    
    if (embeddingResponse && embeddingResponse.data && embeddingResponse.data.length > 0) {
      log(LogLevel.INFO, 'Text embedding generated successfully', { ...logContext, details: { modelUsed: modelToUse, source: modelSource } });
      return embeddingResponse.data[0];
    } else {
      log(LogLevel.ERROR, 'Failed to generate text embedding or response was empty', { ...logContext, payload: { ...(logContext.payload || {}), embeddingResponse, modelUsed: modelToUse }});
      return null;
    }
  } catch (error: any) {
    log(LogLevel.ERROR, 'Error during text embedding generation', { ...logContext, error: error.message, stack: error.stack });
    return null;
  }
}

/**
 * Interface for the input required to store a memory vector.
 * The embedding will be generated internally from the text.
 */
// export interface MemoryChunkInput { ... } // Comment out or remove existing MemoryChunkInput

/**
 * Stores a memory chunk (text and metadata) into Cloudflare Vectorize after generating its embedding.
 * @param env The environment object containing bindings.
 * @param memoryInput The memory chunk data to store, conforming to MemoryRAGChunk.
 * @returns A promise that resolves when the operation is complete.
 * @throws Will throw an error if embedding generation or vector insertion fails.
 */
export async function storeMemoryVector(env: Env, memoryInput: MemoryRAGChunk, telegramUserIdForLog?: number): Promise<void> {
  const functionName = 'storeMemoryVector';
  const logContext: LogContext = { 
    service: SERVICE_NAME, 
    functionName, 
    telegramUserId: telegramUserIdForLog, 
    payload: { userId: String(memoryInput.user_id), textLength: memoryInput.text.length, chunkId: memoryInput.id } 
  }; 
  log(LogLevel.INFO, 'Attempting to store memory vector', logContext);

  if (!env.VECTORIZE_INDEX) {
    log(LogLevel.ERROR, 'env.VECTORIZE_INDEX binding for Vectorize is not available', logContext);
    throw new Error('Vectorize index not configured.');
  }

  const embedding = await generateTextEmbedding(env, memoryInput.text, telegramUserIdForLog);
  if (!embedding) {
    log(LogLevel.ERROR, 'Failed to generate embedding, cannot store memory vector', logContext);
    throw new Error('Failed to generate text embedding for memory vector.');
  }

  // Create metadata payload from MemoryRAGChunk, excluding the 'vector' field itself.
  // The 'text' field (original text) is included in the metadata.
  // Ensure user_id is stored as a string for consistent filtering.
  const metadataForVectorize: Record<string, any> = {};
  for (const key in memoryInput) {
    if (key !== 'vector') {
      if (key === 'user_id') {
        metadataForVectorize['vector_owner_id'] = String(memoryInput[key]);
      } else if (key !== 'debug_tag') {
        metadataForVectorize[key] = (memoryInput as any)[key];
      }
    }
  }
  metadataForVectorize.text = memoryInput.text;

  log(LogLevel.WARN, 'VECTORIZE_METADATA_OWNER_ID_CHECK', {
    ...logContext,
    details: {
        original_user_id_in_memoryInput: memoryInput.user_id,
        type_of_original_user_id: typeof memoryInput.user_id,
        vector_owner_id_put_in_metadata: metadataForVectorize.vector_owner_id,
        type_of_vector_owner_id_in_metadata: typeof metadataForVectorize.vector_owner_id,
        full_metadata_object_for_vectorize: metadataForVectorize
    }
  });

  const vectorDataForInsert = {
    id: memoryInput.id,
    values: embedding,
    metadata: metadataForVectorize,
  };

  try {
    log(LogLevel.DEBUG, 'Inserting vector into Vectorize index', { ...logContext, payload: { ...logContext.payload, vectorId: memoryInput.id, metadataKeys: Object.keys(vectorDataForInsert.metadata) } });
    await env.VECTORIZE_INDEX.insert([vectorDataForInsert]); // Pass as array
    log(LogLevel.INFO, 'Memory vector stored successfully', { ...logContext, payload: { ...logContext.payload, vectorId: memoryInput.id } });
  } catch (error: any) {
    log(LogLevel.ERROR, 'Error storing memory vector in Vectorize', { ...logContext, error: error.message, stack: error.stack, payload: { ...logContext.payload, vectorId: memoryInput.id } });
    throw new Error(`Failed to store memory vector: ${error.message}`);
  }
}

/**
 * Defines the expected structure of a match returned by Vectorize query.
 * This is based on common Vectorize response formats.
 */
export interface VectorMatch {
  id: string;
  score: number;
  vector?: number[]; // Vector itself, optional
  metadata?: Record<string, any>; // Other metadata stored alongside
  // Add other fields returned by Vectorize query as needed
}

/**
 * Queries Cloudflare Vectorize for memories similar to a given query vector, filtered by user ID.
 * @param env The environment object containing bindings.
 * @param queryVector The vector representation of the query.
 * @param userId The user ID (string) to filter memories by. Must match the `userId` in stored metadata.
 * @param topK The maximum number of similar memories to retrieve.
 * @returns A promise that resolves to an array of matching memories, or an empty array if none are found.
 * Each match includes id, score, and metadata.
 * @throws Will throw an error if the query to Vectorize fails.
 */
export async function querySimilarMemories(env: Env, queryVector: number[], userId: string, topK: number = 5, telegramUserIdForLog?: number): Promise<VectorMatch[]> {
  const functionName = 'querySimilarMemories';
  const logContext: LogContext = { service: SERVICE_NAME, functionName, telegramUserId: telegramUserIdForLog, payload: { userId, topK } }; 
  log(LogLevel.INFO, 'Attempting to query similar memories', logContext);

  if (!env.VECTORIZE_INDEX) {
    log(LogLevel.ERROR, 'env.VECTORIZE_INDEX binding for Vectorize is not available', logContext);
    return []; // Or throw, depending on desired error handling
  }
  if (!queryVector || queryVector.length === 0) {
    log(LogLevel.WARN, 'Cannot query with an empty vector', logContext);
    return [];
  }

  try {
    log(LogLevel.DEBUG, 'Querying Vectorize index (Filtering by vector_owner_id)', logContext);
    const results = await env.VECTORIZE_INDEX.query(queryVector, {
      topK,
      filter: { vector_owner_id: String(userId) }
    });

    log(LogLevel.INFO, `Vectorize query returned ${results.matches.length} raw matches (filtering by vector_owner_id).`, { 
        ...logContext, 
        payload: { 
            ...logContext.payload, 
            rawMatchCount: results.matches.length,
            rawMatchesDetails: results.matches.map((match: VectorMatch) => ({
                id: match.id,
                score: match.score,
                metadata: match.metadata
            }))
        }
    });

    if (!results || !results.matches || results.matches.length === 0) {
      log(LogLevel.INFO, "No similar memories found in Vectorize or matches array is empty.", logContext);
      return [];
    }

    const similarInsights = results.matches.map((match: VectorMatch) => {
      const metadata = match.metadata;
      return {
        id: metadata?.id || match.id,
        user_id: metadata?.vector_owner_id || "",
        text: metadata?.text || "",
        source_type: metadata?.source_type || "unknown",
        source_id: metadata?.source_id || "unknown",
        importance_score: metadata?.importance_score || 0,
        timestamp: metadata?.timestamp || "",
        tags: metadata?.tags || [],
        metadata: metadata?.metadata || {},
        score: match.score
      };
    });

    return similarInsights;
  } catch (error: any) {
    log(LogLevel.ERROR, 'Error querying similar memories from Vectorize', { ...logContext, error: error.message, stack: error.stack });
    return []; // Return empty on error to prevent downstream failures
  }
}

// Further implementations will go here
