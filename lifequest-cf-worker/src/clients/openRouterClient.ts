import { Env } from '../types';
import { Message, LLMResponse } from '../workersAIClient'; // Assuming Message & LLMResponse are exported from there
import { log, LogLevel, LogContext } from '../utils/logger';

const OPENROUTER_API_ENDPOINT = 'https://openrouter.ai/api/v1/chat/completions';
const HTTP_REFERER = 'https://lifequest.ai/bot'; // Or your actual app URL
const X_TITLE = 'LifeQuest AI'; // Your app name

const SERVICE_NAME = 'OpenRouterClient';

export async function callOpenRouter(
  env: Env,
  openRouterModelId: string,
  messages: Message[]
): Promise<LLMResponse | null> {
  const functionName = 'callOpenRouter';
  const logContextBase: LogContext = { 
    service: SERVICE_NAME, 
    functionName, 
    payload: { modelId: openRouterModelId, messageCount: messages.length }
  };
  log(LogLevel.INFO, 'Attempting to call OpenRouter API', logContextBase);

  if (!env.OPENROUTER_API_KEY) {
    log(LogLevel.ERROR, 'OPENROUTER_API_KEY is not set in environment variables.', logContextBase);
    return null;
  }

  // Transform messages to the format OpenRouter expects if necessary.
  // OpenAI-compatible format is often supported.
  const openAiMessages = messages.map(m => ({
    role: m.role,
    content: m.content,
  }));

  const requestPayload = {
    model: openRouterModelId,
    messages: openAiMessages,
    // stream: false, // Default is false for OpenRouter, can be added if streaming is needed later
  };

  try {
    const response = await fetch(OPENROUTER_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${env.OPENROUTER_API_KEY}`,
        // 'HTTP-Referer': HTTP_REFERER, 
        // 'X-Title': X_TITLE,
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorBody = await response.text();
      log(LogLevel.ERROR, 'OpenRouter API request failed', { 
        ...logContextBase, 
        error: `Status: ${response.status}`,
        payload: { ...logContextBase.payload, errorBody, statusText: response.statusText }
      });
      return null;
    }

    const responseData: any = await response.json();
    log(LogLevel.DEBUG, 'OpenRouter API response received', { ...logContextBase, payload: { ...logContextBase.payload, responseData }});

    // Adapt the OpenRouter response to your LLMResponse format.
    // This depends on OpenRouter's exact response structure for chat completions (typically OpenAI compatible).
    if (responseData.choices && responseData.choices.length > 0 && responseData.choices[0].message && responseData.choices[0].message.content) {
      const llmResponse: LLMResponse = {
        response: responseData.choices[0].message.content,
        // Optionally, you could try to get usage data if OpenRouter provides it in a standard way
        // usage: responseData.usage ? { 
        //   prompt_tokens: responseData.usage.prompt_tokens,
        //   completion_tokens: responseData.usage.completion_tokens,
        //   total_tokens: responseData.usage.total_tokens 
        // } : undefined
      };
      log(LogLevel.INFO, 'Successfully received and parsed response from OpenRouter', logContextBase);
      return llmResponse;
    } else {
      log(LogLevel.WARN, 'OpenRouter response did not contain expected content structure.', { 
        ...logContextBase, 
        payload: { ...logContextBase.payload, responseData }
      });
      return null;
    }
  } catch (error: any) {
    log(LogLevel.ERROR, 'Error calling OpenRouter API', { 
        ...logContextBase, 
        error: error.message, 
        stack: error.stack 
    });
    if (error.cause) {
        log(LogLevel.ERROR, 'Cause of OpenRouter API call error:', { ...logContextBase, payload: { cause: error.cause } });
    }
    return null;
  }
} 