import { Update, Message as TelegramMessageTypes, CallbackQuery } from '@grammyjs/types';
import { processConfirmation, ConfirmationResult, CompleteQuestPayload } from './services/confirmationService';
import { getSupabaseClient } from './supabaseClient';
import { sendConfirmationMessage as sendTelegramConfirmationMessage } from './utils/telegramUtils';
import { getOrCreateUser, UserProfile } from './services/userService';
import { Env } from './types'; // Import the global Env type
import { logMessage, MessageRow, getRecentMessages } from './services/messageService'; // Added getRecentMessages
import { buildContext } from './services/memoryService'; // Import for insight extraction, added buildContext
import { runLLM, Message as LLMMessage } from './workersAIClient'; // Corrected Import path for runLLM
import { log, LogLevel, LogContext } from './utils/logger'; // Import logger
import { SupabaseError } from './errors/AppError'; // Corrected import path for SupabaseError
import { createQuest, QuestInsert, getQuests, QuestFilters, Quest, findQuestByIdOrDescription, resetMissedStreaks } from './services/questService'; // Import quest service functions
import { formatHallOfFameEntries, formatQuestsList, formatStats, EMOJI as FORMATTER_EMOJI } from './utils/formatter'; // Import formatter functions
import {
  addHallOfFameEntry,
  HallOfFameEntryInsert,
  HallOfFameFilters,
  getHallOfFameEntries, // Import getHallOfFameEntries
  HallOfFameEntry // Import HallOfFameEntry
} from './services/hallOfFameService'; // Import for Hall of Fame
import { updateGameState, GameStateOperations, getGameState, GameState } from './services/gameStateService';
import { extractInsights, Insight, SaveMemoryInsightPayload } from './services/memoryService';

export interface SummaryScope {
  type: 'period' | 'quest' | 'topic' | 'all';
  value?: string; // For period name or topic string or quest description
  questId?: number; // For specific quest ID
}

export interface SummaryDataBundle {
  recentMessages?: MessageRow[];
  relevantQuests?: Quest[];
  relevantHallOfFameEntries?: HallOfFameEntry[];
  gameState?: GameState | null;
  contextDescription: string;
  scope: SummaryScope;
}

const ActionTypes = {
  SAVE_MEMORY_INSIGHT: 'SAVE_MEMORY_INSIGHT',
  AWARD_XP: 'AWARD_XP',
  SAVE_EXTRACTED_INSIGHTS: 'SAVE_EXTRACTED_INSIGHTS',
  COMPLETE_QUEST: 'COMPLETE_QUEST',
  SAVE_SUMMARY_TO_MEMORY: 'SAVE_SUMMARY_TO_MEMORY', // New action type
};

function escapeMarkdownV2(text: string): string {
  if (!text) return '';
  return text.replace(/[_*\[\]()~`>#+\-=|{}.!]/g, '\\$&');
}

function escapeMarkdownV2PreserveBold(text: string): string {
  // Define all characters that need escaping in MarkdownV2
  const specialChars = /[_*\[\]()~`>#+\-=|{}.!]/g;
  // Define characters that need escaping even inside bold, for the inner replace
  const innerSpecialChars = /[_\`>#+\-=|{}.!]/g; // Excludes *, [, ], ( ,) , ~

  // Handle hyphen-only lines separately first
  let processedText = text.replace(/^-+$/gm, '──────────────────────────────');

  return processedText.replace(/\*\*([\s\S]*?)\*\*|[_*\[\]()~`>#+\-=|{}.!]|[^_*\\\[\]()~`>#+\-=|{}.!]+/g, (match, boldContent) => {
    if (boldContent !== undefined) {
      // It's a bold segment
      // Escape only the inner special characters within the bold content
      const escapedBoldContent = boldContent.replace(innerSpecialChars, '\\$&');
      return `**${escapedBoldContent}**`;
    } else if (specialChars.test(match)) {
      // It's a special character outside bold that needs escaping
      return '\\' + match;
    }
    // It's a non-special character outside bold, or part of a sequence of non-special chars
    return match;
  });
}

const EMOJI = FORMATTER_EMOJI;

// Convert standalone functions to methods of TelegramHandler class
export class TelegramHandler {
  private env: Env;

  constructor(env: Env) {
    this.env = env;
  }

  /**
   * Sends a message to a Telegram chat.
   * It also logs the outgoing message to the database.
   * Markdown V2 is used for formatting, and special characters are escaped.
   *
   * @param chatId The ID of the chat to send the message to.
   * @param text The text of the message to send.
   * @param actualUserIdForLogging The user's database ID for logging purposes.
   * @param extra Any additional parameters to include in the Telegram API request.
   * @returns A Promise resolving to the Response from the Telegram API.
   */
  private async sendTelegramMessage(chatId: number, text: string, actualUserIdForLogging?: number, extra: any = {}, isPreformatted: boolean = false) {
    const logContextOriginal: LogContext = { 
        service: 'TelegramHandler', 
        functionName: 'sendTelegramMessage', 
        actualUserId: actualUserIdForLogging, 
        payload: { chatId, textLength: text?.length, originalExtra: extra } // Log original extra
    };
    log(LogLevel.DEBUG, 'Attempting to send Telegram message (potentially splitting)', logContextOriginal);
    
    const textToEscape = text || ""; 
    const textToSendInitially = isPreformatted ? textToEscape : escapeMarkdownV2(textToEscape);
    const MAX_TELEGRAM_MESSAGE_LENGTH = 4000; 

    const chunks: string[] = [];
    if (textToSendInitially.length <= MAX_TELEGRAM_MESSAGE_LENGTH) {
        chunks.push(textToSendInitially);
    } else {
        log(LogLevel.WARN, `Message exceeds ${MAX_TELEGRAM_MESSAGE_LENGTH} chars, splitting. Original length: ${textToSendInitially.length}`, logContextOriginal);
        let remainingText = textToSendInitially;
        while (remainingText.length > 0) {
            if (remainingText.length <= MAX_TELEGRAM_MESSAGE_LENGTH) {
                chunks.push(remainingText);
                break;
            }
            let splitPoint = -1;
            // Prioritize escaped newlines if text was pre-escaped, then normal newlines
            const searchChars = isPreformatted ? ['\\n', '\n'] : ['\n', '\\n']; 
            for (const nl of searchChars) {
                splitPoint = remainingText.lastIndexOf(nl, MAX_TELEGRAM_MESSAGE_LENGTH - nl.length);
                if (splitPoint !== -1) {
                    splitPoint += nl.length; // Include the newline in the current chunk
                    break;
                }
            }
            if (splitPoint === -1 || splitPoint === 0) { 
                splitPoint = MAX_TELEGRAM_MESSAGE_LENGTH;
            }
            chunks.push(remainingText.substring(0, splitPoint));
            remainingText = remainingText.substring(splitPoint);
        }
    }

    let overallSuccess = true;
    let lastResponse: Response | null = null;

    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        // Create a fresh log context for each chunk call, inheriting base details
        const chunkLogContext: LogContext = { 
            ...logContextOriginal, 
            payload: { ...logContextOriginal.payload, chunkNum: `${i+1}/${chunks.length}`, chunkLength: chunk.length }
        };
        
        if (actualUserIdForLogging && chunk) { // Log assistant chunk if it has content
            try { await logMessage(actualUserIdForLogging, 'assistant', chunk, this.env); }
            catch (e: any) { log(LogLevel.ERROR, 'Failed to log assistant message chunk', { ...chunkLogContext, error: e.message }); }
        }

        const payload: any = {
            chat_id: chatId,
            text: chunk, // Chunk is already appropriately escaped or preformatted
            ...extra,
        };

        if (extra.parse_mode === null || extra.parse_mode === '') {
            delete payload.parse_mode;
        } else if (!extra.hasOwnProperty('parse_mode')) {
            payload.parse_mode = 'MarkdownV2';
        }
        
        const url = `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/sendMessage`;
        log(LogLevel.DEBUG, `Sending chunk ${i+1}/${chunks.length} to Telegram API`, { ...chunkLogContext, details: { text_payload_preview: chunk.substring(0,100), parse_mode: payload.parse_mode } });
        
        try {
            lastResponse = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
            if (!lastResponse.ok) {
                const errBody = await lastResponse.text();
                log(LogLevel.ERROR, `Error sending chunk ${i+1}/${chunks.length}`, { ...chunkLogContext, details: { status: lastResponse.status, errBody } });
                overallSuccess = false; 
            } else {
                log(LogLevel.INFO, `Sent chunk ${i+1}/${chunks.length} successfully`, { ...chunkLogContext, details: { status: lastResponse.status } });
            }
        } catch (fetchError: any) {
            log(LogLevel.ERROR, `Network error sending chunk ${i+1}/${chunks.length}`, { ...chunkLogContext, error: fetchError.message });
            overallSuccess = false;
            lastResponse = new Response('Network error', { status: 500 }); // Simulate a response for consistent return type
        }

        if (i < chunks.length - 1 && chunks.length > 1) { // Only delay if there are more chunks
            await new Promise(resolve => setTimeout(resolve, 750)); 
        }
    }
    
    if (chunks.length === 0 && text && actualUserIdForLogging) { // Original text was not empty, but resulted in 0 chunks (e.g. only whitespace, but still logged)
         // This case is rare if textToEscape already handles null/empty, but good for completeness
         try { await logMessage(actualUserIdForLogging, 'assistant', "(Empty message after processing)", this.env); } catch (e) {}
    }

    return lastResponse ?? new Response(overallSuccess ? 'OK' : 'Error sending some chunks', { status: overallSuccess ? 200 : 500 });
  }

  /**
   * Edits an existing message in a Telegram chat.
   * Markdown V2 is used for formatting, and special characters are escaped.
   *
   * @param chatId The ID of the chat where the message is.
   * @param messageId The ID of the message to edit.
   * @param text The new text for the message.
   * @param extra Any additional parameters to include in the Telegram API request.
   * @returns A Promise resolving to the Response from the Telegram API.
   */
  private async editTelegramMessage(chatId: number, messageId: number, text: string, extra: any = {}, isPreformatted: boolean = false) { 
    const logContext: LogContext = { service: 'TelegramHandler', functionName: 'editTelegramMessage', payload: {chatId, messageId, textLength: text?.length, isPreformatted}};
    log(LogLevel.DEBUG, 'Attempting to edit message', logContext);
    const url = `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/editMessageText`;
    const textToSend = isPreformatted ? text : escapeMarkdownV2(text);
    const bodyPayload = { chat_id: chatId, message_id: messageId, text: textToSend, parse_mode: 'MarkdownV2', ...extra };
    if (extra.parse_mode === null || extra.parse_mode === '') {
      delete bodyPayload.parse_mode;
    } else if (!extra.hasOwnProperty('parse_mode')) {
      bodyPayload.parse_mode = 'MarkdownV2';
    }
    if (bodyPayload.reply_markup === null) delete bodyPayload.reply_markup;
    const response = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(bodyPayload) });
    if(!response.ok) {const err = await response.text(); log(LogLevel.ERROR, 'Error editing message', {...logContext, error:err});}
    return response;
  }

  /**
   * Answers a callback query from an inline keyboard button.
   *
   * @param callbackQueryId The ID of the callback query to answer.
   * @param text Optional text to display to the user.
   * @param showAlert If true, an alert will be shown to the user instead of a notification.
   * @returns A Promise resolving to the Response from the Telegram API.
   */
  private async answerCallbackQuery(callbackQueryId: string, text?: string, showAlert?: boolean) {
    const logContext: LogContext = { service: 'TelegramHandler', functionName: 'answerCallbackQuery', payload: {callbackQueryId, textLength: text?.length, showAlert}};
    log(LogLevel.DEBUG, 'Answering callback query', logContext);
    const url = `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`;
    const body:any = { callback_query_id: callbackQueryId };
    if (text) body.text = text; 
    if (showAlert) body.show_alert = showAlert;
    const response = await fetch(url, {method: 'POST', headers: {'Content-Type':'application/json'}, body: JSON.stringify(body)});
    if(!response.ok){const err = await response.text(); log(LogLevel.ERROR, 'Error answering callback', {...logContext, error:err});}
    return response;
   }

  /**
   * Handles an incoming update from Telegram.
   * This is the main entry point for all Telegram interactions.
   * It parses the update, identifies the user, logs messages,
   * and routes commands or callback queries to appropriate handlers.
   *
   * @param request The incoming Request object from Cloudflare's fetch listener.
   * @returns A Promise resolving to a Response object to be sent back to Telegram.
   */
  public async handleTelegramUpdate(request: Request): Promise<Response> {
    let update: Update;
    try { update = await request.json(); } 
    catch (e: any) { log(LogLevel.ERROR, 'Failed to parse request JSON', { service: 'TelegramHandler', error: e.message }); return new Response('Invalid JSON', { status: 400 }); }
    log(LogLevel.INFO, 'Received Telegram update', { service: 'TelegramHandler', updateId: update.update_id });

    let telegramUserId: number | undefined;
    let userProfile: UserProfile | undefined;
    let fromUserObj: TelegramMessageTypes['from'] | CallbackQuery['from'] | undefined;
    let chatId: number | undefined;
    let currentActualUserId: number | undefined;

    try {
      if (update.message?.from) {
        telegramUserId = update.message.from.id;
        fromUserObj = update.message.from;
        chatId = update.message.chat.id;
      } else if (update.callback_query?.from) {
        telegramUserId = update.callback_query.from.id;
        fromUserObj = update.callback_query.from;
        if (update.callback_query.message) { chatId = update.callback_query.message.chat.id; }
      }

      if (!telegramUserId || !chatId) { 
        log(LogLevel.ERROR, 'Could not determine Telegram User ID or Chat ID', { service: 'TelegramHandler', updateId: update.update_id }); 
        if (update.callback_query?.id) { await this.answerCallbackQuery(update.callback_query.id, 'Error: User/chat ID missing.', true); }
        return new Response('User ID or Chat ID missing', { status: 400 });
      }

      try {
        userProfile = await getOrCreateUser(telegramUserId, this.env, fromUserObj ? { username: fromUserObj.username, first_name: fromUserObj.first_name, last_name: fromUserObj.last_name } : undefined);
        currentActualUserId = userProfile.id;
      } catch (error: any) {
        log(LogLevel.ERROR, 'Error in getOrCreateUser', { service: 'TelegramHandler', telegramUserId, error: error.message });
        await this.sendTelegramMessage(chatId, "I had a hiccup identifying you. Please try again.", undefined, {}, false); 
        return new Response('User profile error', { status: 500 });
      }
      
      const actualUserId = currentActualUserId;
      const baseLogCtx: LogContext = { service: 'TelegramHandler', actualUserId, telegramUserId }; 
      log(LogLevel.INFO, 'User profile obtained/created', { ...baseLogCtx });

      if (update.message && 'text' in update.message) {
        const tgMessage = update.message as TelegramMessageTypes.TextMessage;
        const text = tgMessage.text || '';
        // chatId is already confirmed if we reached here with a message

        try { await logMessage(actualUserId, 'user', text, this.env); } 
        catch (e: any) { log(LogLevel.ERROR, 'Failed to log user message', { ...baseLogCtx, error: e.message }); }

        const commandMatch = text.match(/^\/(\w+)\s*(.*)$/s);
        
        if (commandMatch) {
          const command = commandMatch[1].toLowerCase();
          const commandArgs = commandMatch[2].trim();
          log(LogLevel.INFO, 'Command received', { ...baseLogCtx, command, payload: { commandArgs } });

          switch (command) {
            case 'start':
              try {
                const llmMessages = await buildContext(telegramUserId, text, this.env, true);
                const llmResponse = await runLLM(this.env, llmMessages, this.env.AI_MODEL_ID);
                let responseText = "Welcome to LifeQuest AI! Ready to gamify your life? Type /help for commands.";
                if (llmResponse && llmResponse.response) { 
                  responseText = llmResponse.response; 
                  log(LogLevel.DEBUG, '/start LLM response received', {...baseLogCtx, payload: {responseLength: responseText.length}});
                }
                await this.sendTelegramMessage(chatId, responseText, actualUserId, {}, false); // Ensure isPreformatted is false
              } catch (error: any) {
                log(LogLevel.ERROR, `Error processing /start command with LLM`, { ...baseLogCtx, command:'/start', error: error.message, stack: error.stack });
                await this.sendTelegramMessage(chatId, "There was an issue initiating our conversation. Please try /start again.", actualUserId, {}, false);
              }
              break;
            case 'help':
              const helpSubCommand = commandArgs;
              let helpText = '';
              if (!helpSubCommand) {
                helpText =
                  `LifeQuest AI - Command Guide ${EMOJI.BOOK}\n\n` +
                  `Here are the commands you can use:\n\n` +
                  `• /help [command] - Shows this guide or help for a specific command.\n` +
                  `• /start - Displays the welcome message and initiates conversation.\n` +
                  `• /checkin [energy] - Daily check-in, log energy (1-10), see routine streaks.\n` +
                  `• /stats - View your game stats, active quests, streaks, and Hall of Fame highlights.\n` +
                  `• /quests [filters] - List your quests (e.g., status=pending type=main).\n` +
                  `• /add_quest [desc] - Add a new quest or routine.\n` +
                  `• /complete_quest [id/desc] - Mark a quest as complete and get rewards.\n` +
                  `• /hall_add [text] - Add an achievement or memory to your Hall of Fame.\n` +
                  `• /hall_show [filters] - View your Hall of Fame entries.\n` +
                  `• /summary [target] - Get an AI-generated summary (e.g., /summary week).\n` +
                  `\nDeveloper/Test Commands ${EMOJI.CONSTRUCTION}\n` +
                  `• /test_confirm_insight <text> - Test insight confirmation flow.\n` +
                  `• /test_confirm_xp <amount> [reason] - Test XP award confirmation flow.\n` +
                  `• /test_extract_insights <user_msg> || <ai_resp> - Test insight extraction.\n` +
                  `\nType \`/help <command_name>\` for more details on a specific command (e.g., \`/help quests\`).`;
              } else {
                // Specific command help cases
                switch (helpSubCommand) {
                  case 'start': helpText = '/start: Displays welcome message & initiates conversation with AI.'; break;
                  case 'help': helpText = '/help [command_name]: Shows general help or help for a specific command_name.'; break;
                  case 'checkin': helpText = '/checkin [energy_level (1-10)]: Daily check-in, log energy, see routine streaks.'; break;
                  case 'stats': helpText = '/stats: View comprehensive game status.'; break;
                  case 'quests': helpText = '/quests [filters]: List quests. Filters: status=..., type=...'; break;
                  case 'add_quest': helpText = '/add_quest [description]: Add a new quest.'; break;
                  case 'complete_quest': helpText = '/complete_quest [id/desc]: Mark quest complete.'; break;
                  case 'hall_add': helpText = '/hall_add [text]: Add to Hall of Fame.'; break;
                  case 'hall_show': helpText = '/hall_show [filters]: View Hall of Fame. Filters: type=..., period=..., tags=...'; break;
                  case 'summary': helpText = '/summary [target]: Get AI summary for period, quest, or topic.'; break;
                  case 'test_confirm_insight': helpText = '/test_confirm_insight <text>: Test insight confirmation.'; break;
                  case 'test_confirm_xp': helpText = '/test_confirm_xp <amount> [reason]: Test XP award.'; break;
                  case 'test_extract_insights': helpText = '/test_extract_insights <user_msg> || <ai_resp>: Test insight extraction.'; break;
                  default: helpText = `Sorry, no specific help for /${escapeMarkdownV2(helpSubCommand)}. Try /help.`;
                }
              }
              await this.sendTelegramMessage(chatId, helpText, actualUserId, {}, false); // Help text is plain, not preformatted
              break;
            case 'checkin':
              log(LogLevel.INFO, 'Processing /checkin command', { ...baseLogCtx, command, payload: { commandArgs } });
              let energyLevel: number | undefined = undefined;
              let checkinResponseText = '';
              if (commandArgs) {
                const parsedEnergy = parseInt(commandArgs, 10);
                if (!isNaN(parsedEnergy) && parsedEnergy >= 1 && parsedEnergy <= 10) {
                  energyLevel = parsedEnergy;
                } else {
                  checkinResponseText = 'Invalid energy for /checkin [1-10].';
                  await this.sendTelegramMessage(chatId, checkinResponseText, actualUserId, {}, false);
                  return new Response('OK'); // early exit for invalid arg
                }
              }
              try {
                const resetQuestIds = await resetMissedStreaks(actualUserId, this.env);
                if (resetQuestIds.length > 0) { log(LogLevel.INFO, `Reset ${resetQuestIds.length} streaks`, { ...baseLogCtx }); }
              } catch (e: any) { log(LogLevel.ERROR, 'Error resetting streaks for /checkin', {...baseLogCtx, error: e.message }); }
              if (energyLevel !== undefined) {
                checkinResponseText = `Energy ${energyLevel} noted.`;
                try { 
                  const updatedState = await updateGameState(actualUserId, { energy_to_set: energyLevel }, this.env);
                  checkinResponseText = `Energy ${updatedState.energy} recorded.`;
                } catch (e: any) { log(LogLevel.ERROR, 'Err updating energy', {...baseLogCtx, error: e.message }); checkinResponseText += ' (save error)'; }
              } else {
                checkinResponseText = 'How are you feeling today? Use `/checkin [1-10]` to log energy.';
              }
              let routineQuestsText = '';
              try {
                const routines = await getQuests(actualUserId, { type: 'routine', status: 'pending' }, this.env);
                if (routines.length > 0) {
                  routineQuestsText = "\n\n🔄 **Routine Quest Status:**\n" + routines.map(q => `• ${escapeMarkdownV2(q.title)}: ${(q.streak || 0) > 0 ? `${q.streak} days ${EMOJI.FIRE}` : 'No streak'}`).join('\n');
                  if (routines.some(q => (q.streak || 0) > 0)) routineQuestsText += "\nKeep up those streaks! 💪";
                }
              } catch (e: any) { log(LogLevel.ERROR, 'Err getting routines for /checkin', {...baseLogCtx, error: e.message }); }
              if (routineQuestsText) checkinResponseText += routineQuestsText;
              checkinResponseText += (checkinResponseText ? "\n\n" : "") + "What is your main focus for today? ✨";
              await this.sendTelegramMessage(chatId, checkinResponseText, actualUserId, {}, false);
              break;
            case 'stats':
              log(LogLevel.INFO, 'Processing /stats command', { ...baseLogCtx, command });
              try {
                const gameState = await getGameState(actualUserId, this.env);
                const activeQuestsAndRoutines = await getQuests(actualUserId, { status: 'pending' }, this.env);
                const hallOfFameEntries = await getHallOfFameEntries(actualUserId, { period: 'all', limit: 3 }, this.env);

                let statsMessage = `${EMOJI.STATS} *Your Current Stats* ${EMOJI.STATS}\n\n`;
                // Format Game State
                statsMessage += formatStats(gameState); // formatStats returns MarkdownV2 escaped string

                // Format Active Quests & Routines
                statsMessage += `\n\n${EMOJI.QUEST} *Active Quests & Routines* ${EMOJI.QUEST}\n`;
                const noActiveQuestsStr = "No active quests or routines. Add your first quest with /add_quest!\n";
                const allClearStr = "All clear on main/side quests! Focus on your routines.\n";
                if (activeQuestsAndRoutines.length === 0) {
                    statsMessage += escapeMarkdownV2(noActiveQuestsStr);
                } else {
                    const nonRoutineActiveQuests = activeQuestsAndRoutines.filter(q => q.type !== 'routine');
                    if (nonRoutineActiveQuests.length > 0) {
                        statsMessage += formatQuestsList(nonRoutineActiveQuests, "Active Main/Side Quests"); // formatQuestsList also handles escaping
                    } else if (activeQuestsAndRoutines.filter(q => q.type === 'routine').length > 0) {
                        statsMessage += escapeMarkdownV2(allClearStr);
                    } else {
                        statsMessage += escapeMarkdownV2(noActiveQuestsStr);
                    }
                }
                
                // Format Current Streaks
                statsMessage += `\n\n${EMOJI.FIRE} *Current Streaks* ${EMOJI.FIRE}\n`;
                const routineQuestsForStreaks = activeQuestsAndRoutines.filter(q => q.type === 'routine');
                if (routineQuestsForStreaks.length > 0) {
                    let hasActiveStreaks = false;
                    let longestStreak = 0;
                    for (const quest of routineQuestsForStreaks) {
                        const streakCount = quest.streak || 0;
                        if (streakCount > 0) {
                            statsMessage += `• ${escapeMarkdownV2(quest.title)}: ${streakCount} days ${EMOJI.FIRE}\n`;
                            hasActiveStreaks = true;
                            if (streakCount > longestStreak) longestStreak = streakCount;
                        }
                    }
                    if (!hasActiveStreaks) {
                        statsMessage += escapeMarkdownV2("No active streaks yet. Complete routine quests to build them!\n");
                    } else {
                        statsMessage += `\n*${escapeMarkdownV2("Longest current streak: " + longestStreak + " days! Keep it up!")}* ${EMOJI.SPARKLES}\n`;
                    }
                } else {
                    statsMessage += escapeMarkdownV2("No active routine quests to track streaks. Add some with /add_quest and set type to 'routine'.\n");
                }

                // Format Hall of Fame Highlights
                statsMessage += `\n\n${EMOJI.TROPHY} *Hall of Fame Highlights* ${EMOJI.TROPHY}\n`;
                statsMessage += formatHallOfFameEntries(hallOfFameEntries, "Recent Highlights"); // formatHallOfFameEntries handles escaping
                
                // Add "What's next?" section (escaped plain text)
                statsMessage += `\n\n${EMOJI.SPARKLES} *What's next?* ${EMOJI.SPARKLES}\n`;
                if (activeQuestsAndRoutines.filter(q => q.type !== 'routine').length === 0 && routineQuestsForStreaks.length === 0) {
                    statsMessage += escapeMarkdownV2("• Use /add_quest to create your first quest or routine.\n");
                } else {
                    if (activeQuestsAndRoutines.filter(q => q.type !== 'routine').length > 0) {
                        statsMessage += escapeMarkdownV2("• Use /quests to see all your active quests.\n");
                        statsMessage += escapeMarkdownV2("• Use /complete_quest to mark a quest as done.\n");
                    }
                    if (routineQuestsForStreaks.length === 0) {
                        statsMessage += escapeMarkdownV2("• Add 'routine' quests to build daily habits and streaks.\n");
                    }
                }
                statsMessage += escapeMarkdownV2("• Use /checkin to start your day and see routine reminders.\n");
                statsMessage += escapeMarkdownV2("• Add memorable moments with /hall_add.\n");

                await this.sendTelegramMessage(chatId, statsMessage, actualUserId, {}, false); // true because formatters handle MD V2
              } catch (e:any) { 
                log(LogLevel.ERROR, 'Error in /stats processing', {...baseLogCtx, command:'stats', error:e.message, stack: e.stack}); 
                await this.sendTelegramMessage(chatId, "I couldn\'t retrieve your full stats right now. Please try again in a moment.", actualUserId, {}, false);
              }
              break;
            case 'quests':
              log(LogLevel.INFO, 'Processing /quests', { ...baseLogCtx, command, payload: { commandArgs } });
              const filters: QuestFilters = {};
              if (commandArgs) {
                const rawFilters = commandArgs.split(' ');
                for (const rawFilter of rawFilters) {
                  const [key, ...valueParts] = rawFilter.split('=');
                  const value = valueParts.join('=');
                  if (key && value) {
                    if (key.toLowerCase() === 'status') filters.status = value;
                    else if (key.toLowerCase() === 'type') filters.type = value;
                    else { await this.sendTelegramMessage(chatId, `Unknown filter key: ${key}`, actualUserId, {}, false); return new Response('OK'); }
                  } else if (rawFilter.trim()) { await this.sendTelegramMessage(chatId, `Invalid filter: ${rawFilter}`, actualUserId, {}, false); return new Response('OK'); }
                }
              }
              try {
                const quests = await getQuests(actualUserId, filters, this.env);
                if (quests.length === 0) { await this.sendTelegramMessage(chatId, "No quests found matching filters.", actualUserId, {}, false); } 
                else { const fm = formatQuestsList(quests, "Quests"); await this.sendTelegramMessage(chatId, fm, actualUserId, {}, false); }
              } catch (e:any) { log(LogLevel.ERROR, 'Err /quests', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err fetching quests.", actualUserId, {}, false); }
              break;
            case 'add_quest':
              log(LogLevel.INFO, 'Processing /add_quest', { ...baseLogCtx, command, payload: { commandArgs } });
              const questInputText = commandArgs;
              if (!questInputText) { await this.sendTelegramMessage(chatId, "Usage: /add_quest <description>", actualUserId, {}, false); break; }
              try {
                const payload: QuestInsert = { user_id: actualUserId, title: questInputText, description: questInputText, type: 'side', status: 'pending', xp_reward: 10 };
                const newQuest = await createQuest(actualUserId, payload, this.env);
                // Construct message with raw title, let sendTelegramMessage handle escaping.
                const successMessage = `Quest "${newQuest.title}\" (ID: ${newQuest.id}) created!`;
                await this.sendTelegramMessage(chatId, successMessage, actualUserId, {}, false);
              } catch (e:any) { log(LogLevel.ERROR, 'Err /add_quest', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err creating quest.", actualUserId, {}, false); }
              break;
            case 'complete_quest':
              log(LogLevel.INFO, 'Processing /complete_quest', { ...baseLogCtx, command, payload: { commandArgs } });
              const searchTerm = commandArgs;
              if (!searchTerm) { await this.sendTelegramMessage(chatId, "Usage: /complete_quest <id_or_desc>", actualUserId, {}, false); break; }
              try {
                const quest = await findQuestByIdOrDescription(actualUserId, searchTerm, this.env);
                if (!quest) { await this.sendTelegramMessage(chatId, `Quest not found: ${escapeMarkdownV2(searchTerm)}`, actualUserId, {}, false); break; }
                if (quest.status === 'done') { await this.sendTelegramMessage(chatId, `Quest "${escapeMarkdownV2(quest.title)}" already done!`, actualUserId, {}, false); break; }
                const xpR = quest.xp_reward || 10; const coinR = quest.coin_reward || 0;
                const cUUID = crypto.randomUUID();
                const actionPayload: CompleteQuestPayload = { questId: quest.id, questTitle: quest.title, xpReward: xpR, coinReward: coinR };
                let rTxt = xpR > 0 ? `+${xpR} XP` : ''; if (coinR > 0) { if(rTxt) rTxt += ' & '; rTxt += `+${coinR} coins`; } if (!rTxt) rTxt = 'satisfaction!';
                const pMsg = `Complete "${quest.title}"?\nRewards: ${rTxt}`;
                const smr = await sendTelegramConfirmationMessage(this.env.TELEGRAM_BOT_TOKEN, chatId.toString(), pMsg, cUUID, actualUserId );
                if (!smr.ok) { /* log & send error */ await this.sendTelegramMessage(chatId, "Err sending complete confirm.", actualUserId, {}, false); break; }
                const smr_json = await smr.json() as {ok:boolean, result?:{message_id:number}};
                if (!smr_json.ok || !smr_json.result?.message_id) { /* log & send error */ await this.sendTelegramMessage(chatId, "Err with confirm msg_id.", actualUserId, {}, false); break; }
                const oMsgId = smr_json.result.message_id;
                const dti = {id:cUUID, user_telegram_id:telegramUserId, action_type:ActionTypes.COMPLETE_QUEST, action_payload:actionPayload as any, original_message_id:oMsgId, expires_at: new Date(Date.now()+600000).toISOString()};
                const {error: insError} = await getSupabaseClient(this.env).from('pending_confirmations').insert(dti);
                if (insError) { /* log & send error */ await this.sendTelegramMessage(chatId, "Err saving complete confirm.", actualUserId, {}, false); break; }
                log(LogLevel.INFO, 'Complete quest confirm sent', {...baseLogCtx});
              } catch (e:any) { log(LogLevel.ERROR, 'Err /complete_quest', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err completing quest.", actualUserId, {}, false); }
              break;
            case 'hall_add':
              log(LogLevel.INFO, 'Processing /hall_add', { ...baseLogCtx, command, payload: { commandArgs } });
              const entryText = commandArgs;
              if (!entryText) { await this.sendTelegramMessage(chatId, "Usage: /hall_add <text_of_entry>", actualUserId, {}, false); break; }
              try {
                const payload: HallOfFameEntryInsert = { content: entryText, entry_type: 'manual_add', metadata: {sourceType: 'manual_hall_add_command'} };
                const newEntry = await addHallOfFameEntry(actualUserId, payload, this.env);
                await this.sendTelegramMessage(chatId, `Entry "${escapeMarkdownV2(entryText.substring(0,50))}${entryText.length > 50 ? '...' : ''}" added to Hall of Fame! (ID: ${newEntry.id})`, actualUserId, {}, false);
              } catch (e:any) { log(LogLevel.ERROR, 'Err /hall_add', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err saving to HoF.", actualUserId, {}, false); }
              break;
            case 'hall_show':
              log(LogLevel.INFO, 'Processing /hall_show', { ...baseLogCtx, command, payload: { commandArgs } });
              const hofFilters: HallOfFameFilters = {};
              if (commandArgs) {
                const rawFilters = commandArgs.split(' ');
                for (const rawFilter of rawFilters) {
                  const [key, ...valueParts] = rawFilter.split('=');
                  const value = valueParts.join('=');
                  if (key && value) {
                    const lowerKey = key.toLowerCase();
                    if (lowerKey === 'type') hofFilters.type = value;
                    else if (lowerKey === 'period') { if (["day","week","month","year","all"].includes(value.toLowerCase())) { hofFilters.period = value.toLowerCase() as HallOfFameFilters['period']; } else { /* send err */ break; } }
                    else if (lowerKey === 'tags') { hofFilters.tags = value.split(',').map(t=>t.trim()).filter(t=>t); if(hofFilters.tags.length === 0){/*send err*/ break;}}
                    else { /* send err */ break; }
                  } else if (rawFilter.trim()) { /* send err */ break; }
                }
              }
              try {
                const entries = await getHallOfFameEntries(actualUserId, hofFilters, this.env);
                if (entries.length === 0) { await this.sendTelegramMessage(chatId, "No HoF entries found matching filters.", actualUserId, {}, false); } 
                else { const fm = formatHallOfFameEntries(entries, "HoF Entries"); await this.sendTelegramMessage(chatId, fm, actualUserId, {}, false); }
              } catch (e:any) { log(LogLevel.ERROR, 'Err /hall_show', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err fetching HoF.", actualUserId, {}, false); }
              break;
            case 'summary':
              log(LogLevel.INFO, 'Processing /summary', { ...baseLogCtx, command, payload: {commandArgs} });
              let scope: SummaryScope = { type: 'all' };
              if (commandArgs) {
                const lowerArgs = commandArgs.toLowerCase();
                if (lowerArgs === 'today' || lowerArgs === 'week' || lowerArgs === 'month') {
                  scope = { type: 'period', value: lowerArgs };
                } else if (lowerArgs.startsWith('quest ')) {
                  const questIdentifier = commandArgs.substring('quest '.length).trim();
                  const questIdNum = parseInt(questIdentifier, 10);
                  if (!isNaN(questIdNum)) {
                    scope = { type: 'quest', questId: questIdNum, value: questIdentifier };
                  } else {
                    scope = { type: 'quest', value: questIdentifier };
                  }
                } else {
                  scope = { type: 'topic', value: commandArgs };
                }
              }
              const summaryDataBundle: SummaryDataBundle = { contextDescription: '', scope, recentMessages:[], relevantQuests:[], relevantHallOfFameEntries:[], gameState:null };
              try {
                summaryDataBundle.gameState = await getGameState(actualUserId, this.env);
                summaryDataBundle.recentMessages = await getRecentMessages(actualUserId, this.env, 20);
                switch (scope.type) { 
                    case 'period': 
                        summaryDataBundle.contextDescription = `Summary for period: ${scope.value}`;
                        summaryDataBundle.relevantQuests = await getQuests(actualUserId, {status:'done', limit:15}, this.env);
                        summaryDataBundle.relevantHallOfFameEntries = await getHallOfFameEntries(actualUserId, {period:scope.value as HallOfFameFilters['period'] }, this.env); 
                        break;
                    case 'quest': 
                        summaryDataBundle.contextDescription = `Summary for quest: ${scope.value || 'ID ' + scope.questId}`;
                        let fq: Quest | null = null; 
                        if(scope.questId){fq=await findQuestByIdOrDescription(actualUserId, scope.questId.toString(),this.env);}
                        else if(scope.value){fq=await findQuestByIdOrDescription(actualUserId, scope.value,this.env);}
                        summaryDataBundle.relevantQuests=fq?[fq]:[]; 
                        summaryDataBundle.relevantHallOfFameEntries=await getHallOfFameEntries(actualUserId,{limit:10,period:'all'},this.env); 
                        break;
                    case 'topic': 
                        summaryDataBundle.contextDescription = `Summary for topic: ${scope.value}`;
                        summaryDataBundle.relevantQuests = await getQuests(actualUserId, {status:'done',limit:20},this.env); 
                        summaryDataBundle.relevantHallOfFameEntries=await getHallOfFameEntries(actualUserId,{limit:20,period:'month'},this.env); 
                        break;
                    default: // 'all'
                        summaryDataBundle.contextDescription='General summary'; 
                        summaryDataBundle.relevantQuests=await getQuests(actualUserId,{status:'done',limit:10},this.env); 
                        summaryDataBundle.relevantHallOfFameEntries=await getHallOfFameEntries(actualUserId,{limit:10,period:'all'},this.env); 
                        break;
                }
                log(LogLevel.DEBUG, 'Summary data gathered', {...baseLogCtx});
                
                let summarizationQueryInput = `Focus of Summary: ${summaryDataBundle.contextDescription}\nRelevant Info:\n`;
                if (summaryDataBundle.gameState) { summarizationQueryInput += `GameState: ${JSON.stringify({xp: summaryDataBundle.gameState.xp, energy: summaryDataBundle.gameState.energy})}\n`; }
                if (summaryDataBundle.relevantQuests && summaryDataBundle.relevantQuests.length > 0) { summarizationQueryInput += `Quests: ${summaryDataBundle.relevantQuests.map(q=>q.title).join(', ')}\n`; }
                if (summaryDataBundle.relevantHallOfFameEntries && summaryDataBundle.relevantHallOfFameEntries.length > 0) { summarizationQueryInput += `HoF: ${summaryDataBundle.relevantHallOfFameEntries.map(h=>h.content.substring(0,30)+'...').join(', ')}\n`; }
                if (summaryDataBundle.recentMessages && summaryDataBundle.recentMessages.length > 0) { summarizationQueryInput += `Recent chat context included for summarization implicitly via buildContext.`; }
                summarizationQueryInput += `\nBased on ALL available context (including our full conversation history), provide a concise summary for: \"${summaryDataBundle.contextDescription}\".`;

                const llmMessages = await buildContext(telegramUserId, summarizationQueryInput, this.env, false);
                const llmResponse = await runLLM(this.env, llmMessages, this.env.AI_MODEL_ID);
                let aiSummary = llmResponse?.response || "Could not generate summary at this time.";
                await this.sendTelegramMessage(chatId, aiSummary, actualUserId, {}, true);
                
                const cUUIDsum = crypto.randomUUID(); 
                const sPayload = {summaryText:aiSummary, contextDescription: summaryDataBundle.contextDescription, scopeType:scope.type, scopeValue:scope.value, scopeQuestId:scope.questId};
                const pMsgSum = "Would you like to save this summary to your long-term RAG memory?";
                const smrSum = await sendTelegramConfirmationMessage(this.env.TELEGRAM_BOT_TOKEN, chatId.toString(), pMsgSum, cUUIDsum, actualUserId);
                if(smrSum.ok){
                    const smrJson = await smrSum.json() as {ok:boolean, result?:{message_id:number}};
                    if(smrJson.ok && smrJson.result?.message_id){
                        const oMsgIdSum = smrJson.result.message_id;
                        const dtiSum = {id:cUUIDsum, user_telegram_id:telegramUserId, action_type:ActionTypes.SAVE_SUMMARY_TO_MEMORY, action_payload:sPayload as any, original_message_id:oMsgIdSum, expires_at:new Date(Date.now()+600000).toISOString()};
                        const {error: insErrorSum} = await getSupabaseClient(this.env).from('pending_confirmations').insert(dtiSum);
                        if (insErrorSum) { log(LogLevel.ERROR, 'DB err saving summary confirm', {...baseLogCtx, error: insErrorSum.message});}
                    } else {log(LogLevel.ERROR, 'Err confirm summary msg_id', {...baseLogCtx});}
                } else {log(LogLevel.ERROR, 'Err sending summary confirm TG API', {...baseLogCtx});}
              } catch (e:any) { log(LogLevel.ERROR, 'Err /summary', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err generating summary.", actualUserId); }
              break;
            case 'test_confirm_insight':
              log(LogLevel.INFO, 'Processing /test_confirm_insight', { ...baseLogCtx, command, payload: { commandArgs }});
              try {
                const insightText = commandArgs;
                if (!insightText) { await this.sendTelegramMessage(chatId, "Usage: /test_confirm_insight <text>", actualUserId); break; }
                const cUUID = crypto.randomUUID();
                const actionPayload = { text: insightText, title: `Test Insight: ${insightText.substring(0,20)}...` };
                const pMsg = `Save insight: \"${insightText}\"?`;
                const smr = await sendTelegramConfirmationMessage(this.env.TELEGRAM_BOT_TOKEN, chatId.toString(), pMsg, cUUID, actualUserId );
                if (!smr.ok) { await this.sendTelegramMessage(chatId, "Err sending test insight confirm.", actualUserId); break; }
                const smr_json = await smr.json() as {ok:boolean, result?:{message_id:number}};
                if (!smr_json.ok || !smr_json.result?.message_id) { await this.sendTelegramMessage(chatId, "Err with test insight confirm msg_id.", actualUserId); break; }
                const oMsgId = smr_json.result.message_id;
                const dti = {id:cUUID, user_telegram_id:telegramUserId, action_type:ActionTypes.SAVE_MEMORY_INSIGHT, action_payload:actionPayload as any, original_message_id:oMsgId, expires_at: new Date(Date.now()+600000).toISOString()};
                const {error: insError} = await getSupabaseClient(this.env).from('pending_confirmations').insert(dti);
                if (insError) { await this.sendTelegramMessage(chatId, "Err saving test insight confirm DB.", actualUserId); break; }
                log(LogLevel.DEBUG, 'Test insight confirm sent', {...baseLogCtx});
              } catch (e:any) { log(LogLevel.ERROR, 'Err /test_confirm_insight', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err in test insight.", actualUserId); }
              break;
            case 'test_confirm_xp':
              log(LogLevel.INFO, 'Processing /test_confirm_xp', { ...baseLogCtx, command, payload: { commandArgs }});
              try {
                const parts = commandArgs.split(' '); const amountStr = parts[0]; const reason = parts.slice(1).join(' ') || 'Test XP'; const amount = parseInt(amountStr, 10);
                if (isNaN(amount) || amount <= 0) { await this.sendTelegramMessage(chatId, "Usage: /test_confirm_xp <amount> [reason]", actualUserId); break; }
                const cUUIDxp = crypto.randomUUID(); const actionPayloadXP = { amount, reason };
                const pMsgXP = `Award ${amount} XP for \"${reason}\"?`;
                const smrXP = await sendTelegramConfirmationMessage(this.env.TELEGRAM_BOT_TOKEN, chatId.toString(), pMsgXP, cUUIDxp, actualUserId );
                if (!smrXP.ok) { await this.sendTelegramMessage(chatId, "Err sending test XP confirm.", actualUserId); break; }
                const smrXP_json = await smrXP.json() as {ok:boolean, result?:{message_id:number}};
                if (!smrXP_json.ok || !smrXP_json.result?.message_id) { await this.sendTelegramMessage(chatId, "Err with test XP confirm msg_id.", actualUserId); break; }
                const oMsgIdXP = smrXP_json.result.message_id;
                const dtiXP = {id:cUUIDxp, user_telegram_id:telegramUserId, action_type:ActionTypes.AWARD_XP, action_payload:actionPayloadXP, original_message_id:oMsgIdXP, expires_at: new Date(Date.now()+300000).toISOString()};
                const {error: insErrorXP} = await getSupabaseClient(this.env).from('pending_confirmations').insert(dtiXP);
                if (insErrorXP) { await this.sendTelegramMessage(chatId, "Err saving test XP confirm DB.", actualUserId); break; }
                log(LogLevel.DEBUG, 'Test XP confirm sent', {...baseLogCtx});
              } catch (e:any) { log(LogLevel.ERROR, 'Err /test_confirm_xp', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err in test XP.", actualUserId); }
              break;
            case 'test_extract_insights':
              log(LogLevel.INFO, 'Processing /test_extract_insights', { ...baseLogCtx, command, payload: { commandArgs }});
              const content = commandArgs; const partsExt = content.split('||');
              if (partsExt.length !== 2) { await this.sendTelegramMessage(chatId, "Usage: /test_extract_insights <user_msg> || <ai_resp>", actualUserId); break; }
              const userMsgExt = partsExt[0].trim(); const aiRespExt = partsExt[1].trim();
              if (!userMsgExt || !aiRespExt) { await this.sendTelegramMessage(chatId, "User & AI msgs required.", actualUserId); break; }
              try {
                const extracted = await extractInsights(userMsgExt, aiRespExt, this.env, actualUserId);
                if (!extracted || extracted.length === 0) { await this.sendTelegramMessage(chatId, "No insights extracted.", actualUserId); break; }
                const cUUIDext = crypto.randomUUID(); const actionPayloadExt = { insights: extracted };
                let summaryTxtExt = "Extracted Insights:\n"; extracted.forEach((ins, idx) => { summaryTxtExt += `${idx+1}. ${ins.text.substring(0,50)}... (Imp: ${ins.importance_score}, Tags: ${ins.tags.join(', ')})`; }); summaryTxtExt += "Save these?";
                const smrExt = await sendTelegramConfirmationMessage(this.env.TELEGRAM_BOT_TOKEN, chatId.toString(), summaryTxtExt, cUUIDext, actualUserId );
                if (!smrExt.ok) { await this.sendTelegramMessage(chatId, "Err sending test extract confirm.", actualUserId); break; }
                const smrExt_json = await smrExt.json() as {ok:boolean, result?:{message_id:number}};
                if (!smrExt_json.ok || !smrExt_json.result?.message_id) { await this.sendTelegramMessage(chatId, "Err with test extract confirm msg_id.", actualUserId); break; }
                const oMsgIdExt = smrExt_json.result.message_id;
                const dtiExt = {id:cUUIDext, user_telegram_id:telegramUserId, action_type:ActionTypes.SAVE_EXTRACTED_INSIGHTS, action_payload:actionPayloadExt as any, original_message_id:oMsgIdExt, expires_at: new Date(Date.now()+600000).toISOString()};
                const {error: insErrorExt} = await getSupabaseClient(this.env).from('pending_confirmations').insert(dtiExt);
                if (insErrorExt) { await this.sendTelegramMessage(chatId, "Err saving test extract confirm DB.", actualUserId); break; }
                log(LogLevel.DEBUG, 'Test extract confirm sent', {...baseLogCtx});
              } catch (e:any) { log(LogLevel.ERROR, 'Err /test_extract_insights', {...baseLogCtx, error:e.message}); await this.sendTelegramMessage(chatId, "Err in test extract.", actualUserId); }
              break;
            default:
              log(LogLevel.WARN, 'Unknown command received in switch', { ...baseLogCtx, command });
              await this.sendTelegramMessage(chatId, `Sorry, I don't recognize /${command}. Type /help.`, actualUserId, {}, false);
          }
        } else {
          // This is free-form text
          log(LogLevel.INFO, 'Processing free-form message with LLM', { ...baseLogCtx, payload: { textLength: text.length } });
          try {
            const llmMessages = await buildContext(telegramUserId, text, this.env, false);
            const llmResponse = await runLLM(this.env, llmMessages, this.env.AI_MODEL_ID);

            if (llmResponse && llmResponse.response) {
              await this.sendTelegramMessage(chatId, llmResponse.response, actualUserId, {}, false); // Key Change: isPreformatted: false
            } else {
              log(LogLevel.WARN, 'LLM did not return a response for free-form text.', { ...baseLogCtx });
              await this.sendTelegramMessage(chatId, "I'm not sure how to respond to that right now. Could you try rephrasing?", actualUserId, {}, false);
            }
          } catch (error: any) {
            log(LogLevel.ERROR, `Error processing free-form message with LLM`, { ...baseLogCtx, command: 'free-form_chat', error: error.message, stack: error.stack });
            await this.sendTelegramMessage(chatId, "I'm having a little trouble thinking right now. Please try again.", actualUserId, {}, false);
          }
        }
      } else if (update.callback_query) {
        const callbackQuery = update.callback_query;
        // chatId is already derived if callbackQuery.message exists
        const callbackQueryId = callbackQuery.id;
        const callbackData = callbackQuery.data;
        // actualUserId is already known from userProfile
        log(LogLevel.INFO, 'Processing callback query', { ...baseLogCtx, payload: { callbackQueryId, callbackData } });

        if (!chatId || !callbackQuery.message?.message_id) { 
            log(LogLevel.ERROR, 'Chat ID or message_id missing in callback_query', { ...baseLogCtx, payload: { callbackQueryId } });
            await this.answerCallbackQuery(callbackQueryId, 'Error: Message context lost.', true);
            return new Response('OK', { status: 200 });
        }
        const originalMessageId = callbackQuery.message.message_id;

        if (callbackData && callbackData.startsWith('cfm:')) {
            const parts = callbackData.split(':');
            if (parts.length === 3) {
              const confirmationUUID = parts[1];
              const choice = parts[2] as 'y' | 'n'; 
              if (choice !== 'y' && choice !== 'n') {
                log(LogLevel.ERROR, 'Invalid choice in callback_data', { ...baseLogCtx, payload: { callbackData } });
                await this.editTelegramMessage(chatId, originalMessageId, 'Error: Invalid choice format.', {reply_markup:{}}, false);
                await this.answerCallbackQuery(callbackQueryId, 'Error: Invalid choice.', true);
                return new Response('OK');
              }
              try {
                const result: ConfirmationResult = await processConfirmation(confirmationUUID, choice, this.env, callbackQuery);
                await this.answerCallbackQuery(callbackQueryId, result.error ? result.message.substring(0,100) : undefined, result.error ? true : false);
                await this.editTelegramMessage(chatId, originalMessageId, result.message, { reply_markup: {} }, true); 
              } catch (error: any) {
                log(LogLevel.ERROR, 'Error processing confirmation callback', { ...baseLogCtx, payload: { callbackData }, error: error.message });
                await this.answerCallbackQuery(callbackQueryId, 'Error processing action.', true);
                try { await this.editTelegramMessage(chatId, originalMessageId, `An error occurred: ${escapeMarkdownV2(error.message)}`, {reply_markup:{}}, false); } 
                catch (editErr) {log(LogLevel.ERROR, 'Failed to edit message on callback error', {...baseLogCtx, error:(editErr as Error).message });}
              }
            } else {
                log(LogLevel.ERROR, 'Invalid cfm: callback_data format', { ...baseLogCtx, payload: {callbackData}}); 
                await this.editTelegramMessage(chatId, originalMessageId, 'Error: Malformed confirmation data.', {reply_markup:{}}, false); 
                await this.answerCallbackQuery(callbackQueryId, 'Error: Invalid data.', true);
            }
        } else {
            log(LogLevel.WARN, 'Unhandled callback_data', { ...baseLogCtx, payload: {callbackData}}); 
            await this.answerCallbackQuery(callbackQueryId, 'This action is not yet handled.');
        }
      } else {
        log(LogLevel.WARN, 'Unhandled Telegram update type', { service: 'TelegramHandler', updateId: update.update_id, updateTypes: Object.keys(update) });
      }
      return new Response('OK', { status: 200 });
    } catch (error: any) {
      log(LogLevel.ERROR, 'Critical unhandled error in handleTelegramUpdate', { service: 'TelegramHandler', telegramUserId, actualUserId: currentActualUserId, error: error.message, updateContent: JSON.stringify(update).substring(0, 500) });
      if (chatId && telegramUserId) { 
        try { await this.sendTelegramMessage(chatId, "An unexpected error occurred. My team has been notified.", currentActualUserId, {}, false); } 
        catch (sendError: any) { log(LogLevel.ERROR, 'Failed to send critical error message', { service: 'TelegramHandler', telegramUserId, error: sendError.message }); }
      }
      return new Response('Internal Server Error', { status: 500 });
    }
  }
}