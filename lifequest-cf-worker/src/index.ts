/**
 * Welcome to Cloudflare Workers! This is your first worker.
 *
 * - Run `npm run dev` in your terminal to start a development server
 * - Open a browser tab at http://localhost:8787/ to see your worker in action
 * - Run `npm run deploy` to publish your worker
 *
 * Bind resources to your worker in `wrangler.jsonc`. After adding bindings, a type definition for the
 * `Env` object can be regenerated with `npm run cf-typegen`.
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

import { TelegramHandler } from './telegramHandler';
import { Env } from './types'; // Import the global Env type
import { log, LogLevel } from './utils/logger'; // Added logger import

// Remove or comment out the local Env interface
/*
export interface Env {
	TELEGRAM_BOT_TOKEN: string; // Matched to your .env
	SUPABASE_URL: string;
	SUPABASE_ANON_KEY: string; // Matched to your .env
	// Add other bindings from wrangler.toml as needed, e.g.:
	// VECTORIZE_INDEX: VectorizeIndex;
	// AI: any;
}
*/

export default {
	async fetch(
		request: Request,
		env: Env,
		ctx: ExecutionContext
	): Promise<Response> {
		(globalThis as any).__LOG_LEVEL__ = env.LOG_LEVEL; // Set the global variable

		console.log('!!! RAW REQUEST RECEIVED AT WORKER ENTRY POINT !!!');
		// console.log('>>> env.LOG_LEVEL as seen by worker: ', env.LOG_LEVEL); // No longer needed
		// console.log('>>> globalThis.__LOG_LEVEL__ set to: ', (globalThis as any).__LOG_LEVEL__); // No longer needed

		const url = new URL(request.url);
		// Use a unique identifier for the request if available (e.g., from headers like CF-Ray)
		// For now, we'll skip adding a specific workerRequestId, but it could be added here.
		log(LogLevel.INFO, 'Worker received request', { 
			service: 'WorkerFetch', 
			functionName: 'fetch', 
			payload: { method: request.method, url: request.url, headers: Object.fromEntries(request.headers) }
		});

		if (request.method === 'POST') {
			const secretPath = `/telegram/${env.TELEGRAM_BOT_TOKEN}`;
			if (url.pathname === secretPath) {
				try {
					const handler = new TelegramHandler(env);
					return await handler.handleTelegramUpdate(request);
				} catch (e: any) {
					log(LogLevel.ERROR, 'Error in Telegram webhook handler', { 
						service: 'WorkerFetch', 
						functionName: 'fetch', 
						error: e.message, 
						stack: e.stack,
						payload: { method: request.method, url: request.url }
					});
					return new Response('Error handling Telegram update', { status: 500 });
				}
			} else {
				log(LogLevel.WARN, 'POST request to incorrect path', {
					service: 'WorkerFetch',
					functionName: 'fetch',
					payload: { path: url.pathname, expectedPath: secretPath }
				});
				return new Response('Not found', { status: 404 });
			}
		} else if (request.method === 'GET') {
			if (url.pathname === '/') {
				log(LogLevel.INFO, 'GET request to root path - health check', {
					service: 'WorkerFetch',
					functionName: 'fetch',
					payload: { path: url.pathname }
				});
				return new Response('LifeQuest AI Worker is running!', { status: 200 });
			} else if (url.pathname === '/favicon.ico') {
				return new Response('', { status: 204 }); // No content for favicon
			}
		}

		log(LogLevel.WARN, 'Unhandled request', {
			service: 'WorkerFetch',
			functionName: 'fetch',
			payload: { method: request.method, url: request.url }
		});
		return new Response('Not found', { status: 404 });
	},
} satisfies ExportedHandler<Env>;
