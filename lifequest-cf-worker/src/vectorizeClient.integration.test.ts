import { vi, describe, it, expect, beforeEach, Mock } from 'vitest';
import { Env, MemoryRAGChunk } from './types';
// Import actual functions from the module under test
import {
  generateTextEmbedding, 
  storeMemoryVector,
  querySimilarMemories,
  VectorMatch
} from './vectorizeClient';
import { log, LogLevel } from './utils/logger';

// Mock utilities early
vi.mock('./utils/logger', async () => {
  const actualLogger = await vi.importActual('./utils/logger');
  return { ...actualLogger, log: vi.fn() };
});

// No longer mocking './vectorizeClient' itself for this file

const mockAiRun = vi.fn();
const mockVectorizeInsert = vi.fn();
const mockVectorizeQuery = vi.fn();

const mockEnv = {
  AI: { run: mockAiRun },
  VECTORIZE_INDEX: { insert: mockVectorizeInsert, query: mockVectorizeQuery },
  CF_EMBEDDING_MODEL_ID: '@cf/test-embedding-model', 
} as unknown as Env;


describe('Vectorize Client (Integration Tests)', () => {

  beforeEach(() => {
    vi.clearAllMocks(); 
    // Clear mocks for env bindings
    mockAiRun.mockClear();
    mockVectorizeInsert.mockClear();
    mockVectorizeQuery.mockClear();
    (log as Mock).mockClear();
  });

  // storeMemoryVector will call the *actual* generateTextEmbedding,
  // so we mock env.AI.run for it.
  describe('storeMemoryVector', () => {
    const mockMemoryRAGChunk: MemoryRAGChunk = {
      id: 'test-uuid-123',
      user_id: 1,
      text: 'This is a test memory.',
      source_type: 'test_source',
      timestamp: new Date().toISOString(),
      tags: ['test', 'memory'],
      importance_score: 8,
      metadata: { customField: 'customValue' },
    };
    const mockEmbedding = [0.7, 0.8, 0.9];

    it('should store memory vector successfully', async () => {
      // Mock the behavior of the actual generateTextEmbedding via its dependency
      mockAiRun.mockResolvedValue({ data: [mockEmbedding] }); 
      mockVectorizeInsert.mockResolvedValue(undefined);

      await storeMemoryVector(mockEnv, mockMemoryRAGChunk, 123);

      // Check if env.AI.run was called (by the actual generateTextEmbedding)
      expect(mockAiRun).toHaveBeenCalledWith(mockEnv.CF_EMBEDDING_MODEL_ID, { text: [mockMemoryRAGChunk.text] });
      expect(mockVectorizeInsert).toHaveBeenCalledWith([
        {
          id: mockMemoryRAGChunk.id,
          values: mockEmbedding, // This comes from the mocked AI.run via actual generateTextEmbedding
          metadata: {
            id: mockMemoryRAGChunk.id,
            user_id: String(mockMemoryRAGChunk.user_id),
            text: mockMemoryRAGChunk.text,
            source_type: mockMemoryRAGChunk.source_type,
            timestamp: mockMemoryRAGChunk.timestamp,
            tags: mockMemoryRAGChunk.tags,
            importance_score: mockMemoryRAGChunk.importance_score,
            metadata: mockMemoryRAGChunk.metadata,
          },
        },
      ]);
      expect(log).toHaveBeenCalledWith(LogLevel.INFO, 'Memory vector stored successfully', expect.anything());
    });

    it('should throw error if VECTORIZE_INDEX is not available', async () => {
      const envNoVectorize = { AI: mockEnv.AI } as Env; 
      await expect(storeMemoryVector(envNoVectorize, mockMemoryRAGChunk, 123))
        .rejects.toThrow('Vectorize index not configured.');
      expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'env.VECTORIZE_INDEX binding for Vectorize is not available', expect.anything());
    });

    it('should throw error if embedding generation fails (actual generateTextEmbedding returns null because AI.run fails)', async () => {
      mockAiRun.mockResolvedValue({ data: [] }); // AI.run fails to produce embedding
      
      await expect(storeMemoryVector(mockEnv, mockMemoryRAGChunk, 123))
        .rejects.toThrow('Failed to generate text embedding for memory vector.');
      expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Failed to generate embedding, cannot store memory vector', expect.anything());
    });
    
    it('should throw error if vectorize insert fails', async () => {
      mockAiRun.mockResolvedValue({ data: [mockEmbedding] }); // Embedding generation succeeds
      mockVectorizeInsert.mockRejectedValueOnce(new Error('Vectorize exploded'));

      await expect(storeMemoryVector(mockEnv, mockMemoryRAGChunk, 123))
        .rejects.toThrow('Failed to store memory vector: Vectorize exploded');
      expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Error storing memory vector in Vectorize', expect.objectContaining({ error: 'Vectorize exploded' }));
    });
  });

  describe('querySimilarMemories', () => {
    const queryVector = [0.1,0.2,0.3];
    const userIdString = "1"; 
    const mockMatches: VectorMatch[] = [
        { id: "match1", score: 0.9, metadata: { text: "text1", user_id: userIdString, source_type: "typeA" } },
        { id: "match2", score: 0.8, metadata: { text: "text2", user_id: userIdString, tags: ['tag1'] } },
    ];

    it('should query similar memories successfully with user_id filter', async () => {
        mockVectorizeQuery.mockResolvedValueOnce({ matches: mockMatches });
        const result = await querySimilarMemories(mockEnv, queryVector, userIdString, 2, 123);
        expect(result).toEqual(mockMatches);
        expect(mockVectorizeQuery).toHaveBeenCalledWith(queryVector, {
            topK: 2,
            filter: { user_id: userIdString } 
        });
        expect(log).toHaveBeenCalledWith(LogLevel.INFO, 'Vectorize query returned 2 matches', expect.anything());
    });

    it('should return empty array if VECTORIZE_INDEX is not available', async () => {
        const envNoVectorize = { AI: mockEnv.AI } as Env; 
        const result = await querySimilarMemories(envNoVectorize, queryVector, userIdString, 2, 123);
        expect(result).toEqual([]);
        expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'env.VECTORIZE_INDEX binding for Vectorize is not available', expect.anything());
    });
    
    it('should return empty array for an empty query vector', async () => {
        const result = await querySimilarMemories(mockEnv, [], userIdString, 2, 123);
        expect(result).toEqual([]);
        expect(log).toHaveBeenCalledWith(LogLevel.WARN, 'Cannot query with an empty vector', expect.anything());
    });

    it('should return empty array if vectorize query fails', async () => {
        mockVectorizeQuery.mockRejectedValueOnce(new Error('Query failed'));
        const result = await querySimilarMemories(mockEnv, queryVector, userIdString, 2, 123);
        expect(result).toEqual([]);
        expect(log).toHaveBeenCalledWith(LogLevel.ERROR, 'Error querying similar memories from Vectorize', expect.objectContaining({ error: 'Query failed'}));
    });
  });
}); 