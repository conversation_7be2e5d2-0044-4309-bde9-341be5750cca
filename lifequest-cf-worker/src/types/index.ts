declare global {
  var __LOG_LEVEL__: string | undefined;
}
// Ensures this file is treated as a module if it only contains declarations.
// If other exports exist, this might not be strictly necessary but doesn't hurt.
export {}; 

export interface Env {
  TELEGRAM_BOT_TOKEN: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  AI: any; // Binding to Workers AI
  VECTORIZE_INDEX: any; // Binding to Vectorize index
  DB: any; // Binding to D1 database (if used)
  SYSTEM_PROMPT_GIST_URL?: string;
  GITHUB_GIST_PAT?: string;
  APP_ENV?: string;
  LOG_LEVEL?: string;
  MAIN_MODEL_ID?: string; // Optional: Main model ID for LLM calls, fallback for AI_MODEL_ID
  AI_MODEL_ID?: string; // Optional: Specific model ID for AI operations (e.g., '@cf/meta/llama-2-7b-chat-fp16' or 'google/gemini-1.5-flash' for OpenRouter)
  AI_MODEL_MAX_TOKENS?: string; // Max tokens for the configured AI_MODEL_ID (as string, parse to number)
  OPENROUTER_API_KEY?: string; // Optional: API Key for OpenRouter, required if using OpenRouter models via AI_MODEL_ID
  CF_EMBEDDING_MODEL_ID?: string; // Optional: Specific model ID for Cloudflare embeddings (e.g., '@cf/baai/bge-base-en-v1.5')
}

export interface MemoryRAGChunk {
  id: string; // UUID for the memory chunk
  user_id: number; // The user's primary ID from the users table
  text: string; // The actual text content of the memory
  vector?: number[]; // The embedding vector (not stored in Vectorize metadata directly)
  source_type: string; // e.g., "dialogue_insight", "checkin_summary", "hall_of_fame", "knowledge_doc"
  source_id?: string | number; // ID of the original source (e.g., message_id, quest_id, document_id)
  importance_score?: number; // (1-10) Assigned by LLM or user
  timestamp: string; // ISO string for when the original event occurred or memory was logged
  tags?: string[]; // Keywords or categories
  metadata?: Record<string, any>; // For any other flexible key-value pairs
}

export interface UserProfile {
  // ... existing code ...
}

// Routine Quest Scheduling and Streak Interfaces
export interface QuestSchedule {
  type: 'daily' | 'weekly' | 'custom';
  time?: string; // Optional time preference in HH:MM format
  days?: number[]; // For weekly: days of week (1=Monday, 7=Sunday)
  timezone?: string; // User timezone, defaults to UTC
}

export interface RoutineMetadata {
  last_streak_milestone?: number; // Last milestone reached for bonus tracking
  streak_milestones?: number[]; // Defined milestone values (e.g., [3,7,14,30])
  routine_config?: {
    reminder_enabled?: boolean;
    bonus_multiplier?: number;
  };
  [key: string]: any; // Allow additional metadata fields
}

export interface StreakMilestone {
  days: number;
  xp_bonus: number;
  coin_bonus: number;
  description: string;
}

// Constants for streak milestones
export const DEFAULT_STREAK_MILESTONES: StreakMilestone[] = [
  { days: 3, xp_bonus: 5, coin_bonus: 2, description: "3-day streak!" },
  { days: 7, xp_bonus: 15, coin_bonus: 5, description: "Week-long streak!" },
  { days: 14, xp_bonus: 30, coin_bonus: 10, description: "Two-week streak!" },
  { days: 30, xp_bonus: 75, coin_bonus: 25, description: "Monthly streak!" },
  { days: 100, xp_bonus: 300, coin_bonus: 100, description: "Century streak!" }
];

export interface StreakCheckResult {
  isOnSchedule: boolean;
  streakShouldReset: boolean;
  daysSinceLastCompletion: number;
  nextScheduledDate?: Date;
}
