export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      game_state: {
        Row: {
          coins: number | null
          energy: number | null
          id: number
          last_updated: string | null
          metadata: Json | null
          user_id: number | null
          xp: number | null
        }
        Insert: {
          coins?: number | null
          energy?: number | null
          id?: number
          last_updated?: string | null
          metadata?: Json | null
          user_id?: number | null
          xp?: number | null
        }
        Update: {
          coins?: number | null
          energy?: number | null
          id?: number
          last_updated?: string | null
          metadata?: Json | null
          user_id?: number | null
          xp?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "game_state_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      hall_of_fame: {
        Row: {
          content: string
          created_at: string | null
          entry_type: string
          id: number
          metadata: Json | null
          period: string | null
          user_id: number | null
        }
        Insert: {
          content: string
          created_at?: string | null
          entry_type: string
          id?: number
          metadata?: Json | null
          period?: string | null
          user_id?: number | null
        }
        Update: {
          content?: string
          created_at?: string | null
          entry_type?: string
          id?: number
          metadata?: Json | null
          period?: string | null
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "hall_of_fame_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      knowledge_documents: {
        Row: {
          content: string
          created_at: string | null
          document_type: string
          id: number
          title: string | null
          updated_at: string | null
          user_id: number | null
        }
        Insert: {
          content: string
          created_at?: string | null
          document_type: string
          id?: number
          title?: string | null
          updated_at?: string | null
          user_id?: number | null
        }
        Update: {
          content?: string
          created_at?: string | null
          document_type?: string
          id?: number
          title?: string | null
          updated_at?: string | null
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "knowledge_documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          content: string
          id: number
          role: string
          timestamp: string | null
          user_id: number | null
        }
        Insert: {
          content: string
          id?: number
          role: string
          timestamp?: string | null
          user_id?: number | null
        }
        Update: {
          content?: string
          id?: number
          role?: string
          timestamp?: string | null
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      pending_confirmations: {
        Row: {
          action_payload: Json | null
          action_type: string
          created_at: string
          expires_at: string
          id: string
          original_message_id: number
          user_telegram_id: number
        }
        Insert: {
          action_payload?: Json | null
          action_type: string
          created_at?: string
          expires_at: string
          id: string
          original_message_id: number
          user_telegram_id: number
        }
        Update: {
          action_payload?: Json | null
          action_type?: string
          created_at?: string
          expires_at?: string
          id?: string
          original_message_id?: number
          user_telegram_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "pending_confirmations_user_telegram_id_fkey"
            columns: ["user_telegram_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["telegram_id"]
          },
        ]
      }
      quests: {
        Row: {
          coin_reward: number | null
          completed_at: string | null
          created_at: string | null
          description: string | null
          id: number
          metadata: Json | null
          schedule: Json | null
          status: string | null
          streak: number | null
          title: string
          type: string | null
          user_id: number | null
          xp_reward: number | null
        }
        Insert: {
          coin_reward?: number | null
          completed_at?: string | null
          created_at?: string | null
          description?: string | null
          id?: number
          metadata?: Json | null
          schedule?: Json | null
          status?: string | null
          streak?: number | null
          title: string
          type?: string | null
          user_id?: number | null
          xp_reward?: number | null
        }
        Update: {
          coin_reward?: number | null
          completed_at?: string | null
          created_at?: string | null
          description?: string | null
          id?: number
          metadata?: Json | null
          schedule?: Json | null
          status?: string | null
          streak?: number | null
          title?: string
          type?: string | null
          user_id?: number | null
          xp_reward?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "quests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          id: number
          settings: Json | null
          telegram_id: number
        }
        Insert: {
          created_at?: string | null
          id?: number
          settings?: Json | null
          telegram_id: number
        }
        Update: {
          created_at?: string | null
          id?: number
          settings?: Json | null
          telegram_id?: number
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
