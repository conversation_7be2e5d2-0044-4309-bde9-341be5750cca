export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly details?: unknown;

  constructor(
    name: string,
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: unknown,
    stack?: string,
  ) {
    super(message);
    this.name = name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = details;
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

export class SupabaseError extends AppError {
  constructor(
    message: string,
    details?: unknown,
    originalError?: Error, // To store the original PostgrestError if needed
  ) {
    super(
      'SupabaseError',
      message,
      500, // Default, can be overridden or derived from PostgrestError
      true,
      details,
      originalError?.stack,
    );
    if (originalError) {
      // Optionally copy other properties from originalError if needed
    }
  }
} 