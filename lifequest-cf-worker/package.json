{"name": "lifequest-cf-worker", "version": "0.0.0", "private": true, "type": "module", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types"}, "dependencies": {"@grammyjs/types": "^3.4.6", "@supabase/supabase-js": "^2.44.4", "itty-router": "^4.0.23", "tiktoken": "^1.0.21"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "typescript": "^5.5.2", "vitest": "~3.0.7", "wrangler": "^4.16.0"}}